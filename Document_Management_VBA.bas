' نظام إدارة المستندات - Document Management System VBA Code
' Enterprise Management System - Document Upload and Management
' الإصدار 1.0 - Version 1.0

Option Compare Database
Option Explicit

' متغيرات عامة للنظام
Public Const DOC_STORAGE_PATH As String = "C:\EnterpriseSystem\Documents\"
Public Const MAX_FILE_SIZE As Long = 10485760 ' 10MB
Public Const ALLOWED_EXTENSIONS As String = "pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif,txt,zip,rar"

' ===================================================================
' دالة تحميل المستندات - Upload Document Function
' ===================================================================
Public Function UploadDocument(ModuleName As String, RecordID As Long, DocumentTypeID As Long, Optional Title As String = "") As Boolean
    
    On Error GoTo ErrorHandler
    
    Dim fd As FileDialog
    Dim selectedFile As String
    Dim fileName As String
    Dim fileExtension As String
    Dim fileSize As Long
    Dim storedFileName As String
    Dim destinationPath As String
    Dim documentID As Long
    
    ' إنشاء مربع حوار اختيار الملف
    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    
    With fd
        .Title = "اختر المستند للتحميل - Select Document to Upload"
        .AllowMultiSelect = False
        .InitialView = msoFileDialogViewDetails
        
        ' إضافة فلاتر الملفات المسموحة
        .Filters.Clear
        .Filters.Add "جميع الملفات المسموحة", "*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.gif;*.txt;*.zip;*.rar"
        .Filters.Add "ملفات PDF", "*.pdf"
        .Filters.Add "مستندات Word", "*.doc;*.docx"
        .Filters.Add "جداول Excel", "*.xls;*.xlsx"
        .Filters.Add "صور", "*.jpg;*.jpeg;*.png;*.gif"
        .Filters.Add "جميع الملفات", "*.*"
        
        If .Show = -1 Then
            selectedFile = .SelectedItems(1)
        Else
            UploadDocument = False
            Exit Function
        End If
    End With
    
    ' التحقق من وجود الملف
    If Not FileExists(selectedFile) Then
        MsgBox "الملف المحدد غير موجود!", vbCritical, "خطأ"
        UploadDocument = False
        Exit Function
    End If
    
    ' الحصول على معلومات الملف
    fileName = GetFileName(selectedFile)
    fileExtension = GetFileExtension(selectedFile)
    fileSize = FileLen(selectedFile)
    
    ' التحقق من امتداد الملف
    If Not IsAllowedExtension(fileExtension) Then
        MsgBox "نوع الملف غير مسموح!" & vbCrLf & "الأنواع المسموحة: " & ALLOWED_EXTENSIONS, vbCritical, "خطأ"
        UploadDocument = False
        Exit Function
    End If
    
    ' التحقق من حجم الملف
    If fileSize > MAX_FILE_SIZE Then
        MsgBox "حجم الملف كبير جداً!" & vbCrLf & "الحد الأقصى: " & FormatFileSize(MAX_FILE_SIZE), vbCritical, "خطأ"
        UploadDocument = False
        Exit Function
    End If
    
    ' إنشاء اسم ملف فريد
    storedFileName = GenerateUniqueFileName(fileName)
    
    ' إنشاء مسار الوجهة
    destinationPath = DOC_STORAGE_PATH & ModuleName & "\" & storedFileName
    
    ' إنشاء المجلد إذا لم يكن موجوداً
    CreateDirectoryIfNotExists DOC_STORAGE_PATH & ModuleName & "\"
    
    ' نسخ الملف إلى مجلد التخزين
    If CopyFile(selectedFile, destinationPath) Then
        
        ' إدخال بيانات المستند في قاعدة البيانات
        documentID = InsertDocumentRecord(ModuleName, RecordID, DocumentTypeID, Title, fileName, storedFileName, destinationPath, fileSize, fileExtension)
        
        If documentID > 0 Then
            ' تسجيل عملية التحميل في السجل
            LogDocumentAccess documentID, GetCurrentUserID(), "Upload", "تم تحميل المستند بنجاح"
            
            MsgBox "تم تحميل المستند بنجاح!" & vbCrLf & "اسم الملف: " & fileName, vbInformation, "نجح التحميل"
            UploadDocument = True
        Else
            ' حذف الملف في حالة فشل إدخال البيانات
            Kill destinationPath
            MsgBox "فشل في حفظ بيانات المستند!", vbCritical, "خطأ"
            UploadDocument = False
        End If
    Else
        MsgBox "فشل في نسخ الملف!", vbCritical, "خطأ"
        UploadDocument = False
    End If
    
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تحميل المستند: " & Err.Description, vbCritical, "خطأ"
    UploadDocument = False
End Function

' ===================================================================
' دالة إدخال بيانات المستند في قاعدة البيانات
' ===================================================================
Private Function InsertDocumentRecord(ModuleName As String, RecordID As Long, DocumentTypeID As Long, _
                                     Title As String, OriginalFileName As String, StoredFileName As String, _
                                     FilePath As String, FileSize As Long, FileExtension As String) As Long
    
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim documentID As Long
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_Documents", dbOpenDynaset)
    
    With rs
        .AddNew
        !DocumentTypeID = DocumentTypeID
        !RelatedModule = ModuleName
        !RelatedRecordID = RecordID
        !DocumentTitle = IIf(Title = "", OriginalFileName, Title)
        !OriginalFileName = OriginalFileName
        !StoredFileName = StoredFileName
        !FilePath = FilePath
        !FileSize = FileSize
        !FileExtension = fileExtension
        !MimeType = GetMimeType(FileExtension)
        !DocumentDate = Now()
        !DocumentStatus = "Active"
        !ApprovalStatus = "Approved" ' يمكن تغييرها حسب الحاجة
        !UploadedBy = GetCurrentUserID()
        !UploadDate = Now()
        !CreatedDate = Now()
        !CreatedBy = GetCurrentUserID()
        .Update
        
        ' الحصول على معرف المستند الجديد
        .Bookmark = .LastModified
        documentID = !DocumentID
    End With
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    InsertDocumentRecord = documentID
    Exit Function
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    InsertDocumentRecord = 0
End Function

' ===================================================================
' دالة تحميل المستند - Download Document Function
' ===================================================================
Public Function DownloadDocument(DocumentID As Long) As Boolean
    
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    Dim filePath As String
    Dim originalFileName As String
    Dim fd As FileDialog
    Dim destinationPath As String
    
    Set db = CurrentDb
    
    ' الحصول على بيانات المستند
    sql = "SELECT FilePath, OriginalFileName FROM tbl_Documents WHERE DocumentID = " & DocumentID
    Set rs = db.OpenRecordset(sql, dbOpenSnapshot)
    
    If rs.EOF Then
        MsgBox "المستند غير موجود!", vbCritical, "خطأ"
        DownloadDocument = False
        GoTo Cleanup
    End If
    
    filePath = rs!FilePath & ""
    originalFileName = rs!OriginalFileName & ""
    rs.Close
    
    ' التحقق من وجود الملف
    If Not FileExists(filePath) Then
        MsgBox "ملف المستند غير موجود في مجلد التخزين!", vbCritical, "خطأ"
        DownloadDocument = False
        GoTo Cleanup
    End If
    
    ' اختيار مجلد الحفظ
    Set fd = Application.FileDialog(msoFileDialogSaveAs)
    With fd
        .Title = "حفظ المستند - Save Document"
        .InitialFileName = originalFileName
        
        If .Show = -1 Then
            destinationPath = .SelectedItems(1)
        Else
            DownloadDocument = False
            GoTo Cleanup
        End If
    End With
    
    ' نسخ الملف
    If CopyFile(filePath, destinationPath) Then
        ' تحديث عداد التحميل
        UpdateDownloadCount DocumentID
        
        ' تسجيل عملية التحميل
        LogDocumentAccess DocumentID, GetCurrentUserID(), "Download", "تم تحميل المستند"
        
        MsgBox "تم تحميل المستند بنجاح!", vbInformation, "نجح التحميل"
        DownloadDocument = True
    Else
        MsgBox "فشل في تحميل المستند!", vbCritical, "خطأ"
        DownloadDocument = False
    End If
    
Cleanup:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تحميل المستند: " & Err.Description, vbCritical, "خطأ"
    DownloadDocument = False
    GoTo Cleanup
End Function

' ===================================================================
' دالة عرض المستند - View Document Function
' ===================================================================
Public Function ViewDocument(DocumentID As Long) As Boolean
    
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    Dim filePath As String
    
    Set db = CurrentDb
    
    ' الحصول على مسار الملف
    sql = "SELECT FilePath FROM tbl_Documents WHERE DocumentID = " & DocumentID
    Set rs = db.OpenRecordset(sql, dbOpenSnapshot)
    
    If rs.EOF Then
        MsgBox "المستند غير موجود!", vbCritical, "خطأ"
        ViewDocument = False
        GoTo Cleanup
    End If
    
    filePath = rs!FilePath & ""
    rs.Close
    
    ' التحقق من وجود الملف
    If Not FileExists(filePath) Then
        MsgBox "ملف المستند غير موجود!", vbCritical, "خطأ"
        ViewDocument = False
        GoTo Cleanup
    End If
    
    ' فتح الملف بالبرنامج الافتراضي
    Application.FollowHyperlink filePath
    
    ' تسجيل عملية العرض
    LogDocumentAccess DocumentID, GetCurrentUserID(), "View", "تم عرض المستند"
    
    ViewDocument = True
    
Cleanup:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء عرض المستند: " & Err.Description, vbCritical, "خطأ"
    ViewDocument = False
    GoTo Cleanup
End Function

' ===================================================================
' دالة حذف المستند - Delete Document Function
' ===================================================================
Public Function DeleteDocument(DocumentID As Long) As Boolean
    
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    Dim filePath As String
    Dim response As Integer
    
    ' تأكيد الحذف
    response = MsgBox("هل أنت متأكد من حذف هذا المستند؟" & vbCrLf & "لا يمكن التراجع عن هذا الإجراء!", _
                      vbYesNo + vbQuestion, "تأكيد الحذف")
    
    If response = vbNo Then
        DeleteDocument = False
        Exit Function
    End If
    
    Set db = CurrentDb
    
    ' الحصول على مسار الملف
    sql = "SELECT FilePath FROM tbl_Documents WHERE DocumentID = " & DocumentID
    Set rs = db.OpenRecordset(sql, dbOpenSnapshot)
    
    If Not rs.EOF Then
        filePath = rs!FilePath & ""
    End If
    rs.Close
    
    ' تحديث حالة المستند إلى محذوف (بدلاً من الحذف الفعلي)
    sql = "UPDATE tbl_Documents SET DocumentStatus = 'Deleted', ModifiedDate = Now(), ModifiedBy = " & GetCurrentUserID() & _
          " WHERE DocumentID = " & DocumentID
    db.Execute sql
    
    ' تسجيل عملية الحذف
    LogDocumentAccess DocumentID, GetCurrentUserID(), "Delete", "تم حذف المستند"
    
    ' حذف الملف الفعلي (اختياري)
    If FileExists(filePath) Then
        Kill filePath
    End If
    
    MsgBox "تم حذف المستند بنجاح!", vbInformation, "تم الحذف"
    DeleteDocument = True
    
Cleanup:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء حذف المستند: " & Err.Description, vbCritical, "خطأ"
    DeleteDocument = False
    GoTo Cleanup
End Function

' ===================================================================
' دوال مساعدة - Helper Functions
' ===================================================================

' دالة التحقق من وجود الملف
Private Function FileExists(filePath As String) As Boolean
    FileExists = (Dir(filePath) <> "")
End Function

' دالة الحصول على اسم الملف من المسار الكامل
Private Function GetFileName(fullPath As String) As String
    Dim pos As Integer
    pos = InStrRev(fullPath, "\")
    If pos > 0 Then
        GetFileName = Mid(fullPath, pos + 1)
    Else
        GetFileName = fullPath
    End If
End Function

' دالة الحصول على امتداد الملف
Private Function GetFileExtension(fileName As String) As String
    Dim pos As Integer
    pos = InStrRev(fileName, ".")
    If pos > 0 Then
        GetFileExtension = LCase(Mid(fileName, pos + 1))
    Else
        GetFileExtension = ""
    End If
End Function

' دالة التحقق من الامتداد المسموح
Private Function IsAllowedExtension(extension As String) As Boolean
    IsAllowedExtension = (InStr(1, ALLOWED_EXTENSIONS, LCase(extension)) > 0)
End Function

' دالة توليد اسم ملف فريد
Private Function GenerateUniqueFileName(originalFileName As String) As String
    Dim timestamp As String
    Dim extension As String
    Dim baseName As String
    Dim pos As Integer
    
    timestamp = Format(Now(), "yyyymmdd_hhnnss")
    pos = InStrRev(originalFileName, ".")
    
    If pos > 0 Then
        baseName = Left(originalFileName, pos - 1)
        extension = Mid(originalFileName, pos)
        GenerateUniqueFileName = baseName & "_" & timestamp & extension
    Else
        GenerateUniqueFileName = originalFileName & "_" & timestamp
    End If
End Function

' دالة إنشاء المجلد
Private Sub CreateDirectoryIfNotExists(dirPath As String)
    If Dir(dirPath, vbDirectory) = "" Then
        MkDir dirPath
    End If
End Sub

' دالة نسخ الملف
Private Function CopyFile(sourcePath As String, destinationPath As String) As Boolean
    On Error GoTo ErrorHandler
    FileCopy sourcePath, destinationPath
    CopyFile = True
    Exit Function
    
ErrorHandler:
    CopyFile = False
End Function

' دالة الحصول على نوع MIME
Private Function GetMimeType(extension As String) As String
    Select Case LCase(extension)
        Case "pdf": GetMimeType = "application/pdf"
        Case "doc": GetMimeType = "application/msword"
        Case "docx": GetMimeType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        Case "xls": GetMimeType = "application/vnd.ms-excel"
        Case "xlsx": GetMimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        Case "jpg", "jpeg": GetMimeType = "image/jpeg"
        Case "png": GetMimeType = "image/png"
        Case "gif": GetMimeType = "image/gif"
        Case "txt": GetMimeType = "text/plain"
        Case "zip": GetMimeType = "application/zip"
        Case "rar": GetMimeType = "application/x-rar-compressed"
        Case Else: GetMimeType = "application/octet-stream"
    End Select
End Function

' دالة تنسيق حجم الملف
Private Function FormatFileSize(sizeInBytes As Long) As String
    If sizeInBytes < 1024 Then
        FormatFileSize = sizeInBytes & " بايت"
    ElseIf sizeInBytes < 1048576 Then
        FormatFileSize = Format(sizeInBytes / 1024, "0.0") & " كيلوبايت"
    Else
        FormatFileSize = Format(sizeInBytes / 1048576, "0.0") & " ميجابايت"
    End If
End Function

' دالة تحديث عداد التحميل
Private Sub UpdateDownloadCount(DocumentID As Long)
    Dim db As DAO.Database
    Dim sql As String
    
    Set db = CurrentDb
    sql = "UPDATE tbl_Documents SET DownloadCount = DownloadCount + 1, LastAccessDate = Now(), LastAccessBy = " & GetCurrentUserID() & _
          " WHERE DocumentID = " & DocumentID
    db.Execute sql
    Set db = Nothing
End Sub

' دالة تسجيل الوصول للمستندات
Private Sub LogDocumentAccess(DocumentID As Long, UserID As Long, AccessType As String, Notes As String)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_DocumentAccessLog", dbOpenDynaset)
    
    With rs
        .AddNew
        !DocumentID = DocumentID
        !UserID = UserID
        !AccessType = AccessType
        !AccessDate = Now()
        !AccessResult = "Success"
        !Notes = Notes
        .Update
    End With
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' دالة الحصول على معرف المستخدم الحالي
Private Function GetCurrentUserID() As Long
    ' يجب تنفيذ هذه الدالة حسب نظام المصادقة المستخدم
    ' هنا نفترض وجود متغير عام أو جلسة تحتوي على معرف المستخدم
    GetCurrentUserID = 1 ' قيمة افتراضية - يجب تعديلها
End Function

' ===================================================================
' دوال إضافية لإدارة المستندات - Additional Document Management Functions
' ===================================================================

' دالة البحث في المستندات
Public Function SearchDocuments(searchTerm As String, ModuleName As String, DocumentTypeID As Long) As DAO.Recordset

    Dim db As DAO.Database
    Dim sql As String

    Set db = CurrentDb

    sql = "SELECT d.DocumentID, d.DocumentTitle, d.OriginalFileName, d.DocumentDate, " & _
          "d.FileSize, d.DocumentStatus, dt.TypeName, u.FirstName + ' ' + u.LastName AS UploadedBy " & _
          "FROM tbl_Documents d " & _
          "LEFT JOIN tbl_DocumentTypes dt ON d.DocumentTypeID = dt.DocumentTypeID " & _
          "LEFT JOIN tbl_Users u ON d.UploadedBy = u.UserID " & _
          "WHERE d.DocumentStatus = 'Active'"

    If searchTerm <> "" Then
        sql = sql & " AND (d.DocumentTitle LIKE '*" & searchTerm & "*' OR d.OriginalFileName LIKE '*" & searchTerm & "*')"
    End If

    If ModuleName <> "" Then
        sql = sql & " AND d.RelatedModule = '" & ModuleName & "'"
    End If

    If DocumentTypeID > 0 Then
        sql = sql & " AND d.DocumentTypeID = " & DocumentTypeID
    End If

    sql = sql & " ORDER BY d.UploadDate DESC"

    Set SearchDocuments = db.OpenRecordset(sql, dbOpenSnapshot)
End Function

' دالة الحصول على مستندات سجل معين
Public Function GetRecordDocuments(ModuleName As String, RecordID As Long) As DAO.Recordset

    Dim db As DAO.Database
    Dim sql As String

    Set db = CurrentDb

    sql = "SELECT d.DocumentID, d.DocumentTitle, d.OriginalFileName, d.DocumentDate, " & _
          "d.FileSize, dt.TypeName, u.FirstName + ' ' + u.LastName AS UploadedBy " & _
          "FROM tbl_Documents d " & _
          "LEFT JOIN tbl_DocumentTypes dt ON d.DocumentTypeID = dt.DocumentTypeID " & _
          "LEFT JOIN tbl_Users u ON d.UploadedBy = u.UserID " & _
          "WHERE d.DocumentStatus = 'Active' AND d.RelatedModule = '" & ModuleName & "' AND d.RelatedRecordID = " & RecordID & _
          " ORDER BY d.UploadDate DESC"

    Set GetRecordDocuments = db.OpenRecordset(sql, dbOpenSnapshot)
End Function

' دالة مشاركة المستند
Public Function ShareDocument(DocumentID As Long, SharedWithUserID As Long, PermissionType As String, ExpiryDate As Date) As Boolean

    On Error GoTo ErrorHandler

    Dim db As DAO.Database
    Dim rs As DAO.Recordset

    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_DocumentSharing", dbOpenDynaset)

    With rs
        .AddNew
        !DocumentID = DocumentID
        !SharedWithUserID = SharedWithUserID
        !PermissionType = PermissionType
        !ShareDate = Now()
        !SharedBy = GetCurrentUserID()
        !ExpiryDate = ExpiryDate
        !IsActive = True
        .Update
    End With

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    ' تسجيل عملية المشاركة
    LogDocumentAccess DocumentID, GetCurrentUserID(), "Share", "تم مشاركة المستند مع المستخدم " & SharedWithUserID

    ShareDocument = True
    Exit Function

ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    ShareDocument = False
End Function

' دالة إضافة تعليق على المستند
Public Function AddDocumentComment(DocumentID As Long, CommentText As String, Optional ParentCommentID As Long = 0) As Boolean

    On Error GoTo ErrorHandler

    Dim db As DAO.Database
    Dim rs As DAO.Recordset

    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_DocumentComments", dbOpenDynaset)

    With rs
        .AddNew
        !DocumentID = DocumentID
        !UserID = GetCurrentUserID()
        !CommentText = CommentText
        !CommentDate = Now()
        If ParentCommentID > 0 Then !ParentCommentID = ParentCommentID
        !IsActive = True
        .Update
    End With

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    AddDocumentComment = True
    Exit Function

ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    AddDocumentComment = False
End Function

' دالة إنشاء إصدار جديد من المستند
Public Function CreateDocumentVersion(DocumentID As Long, NewFilePath As String, VersionDescription As String) As Boolean

    On Error GoTo ErrorHandler

    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    Dim maxVersion As String
    Dim newVersionNumber As String
    Dim fileName As String
    Dim fileSize As Long
    Dim storedFileName As String
    Dim destinationPath As String

    Set db = CurrentDb

    ' الحصول على أحدث رقم إصدار
    sql = "SELECT MAX(VersionNumber) AS MaxVersion FROM tbl_DocumentVersions WHERE DocumentID = " & DocumentID
    Set rs = db.OpenRecordset(sql, dbOpenSnapshot)

    If IsNull(rs!MaxVersion) Then
        newVersionNumber = "1.1"
    Else
        ' زيادة رقم الإصدار
        newVersionNumber = Format(Val(rs!MaxVersion) + 0.1, "0.0")
    End If
    rs.Close

    ' الحصول على معلومات الملف الجديد
    fileName = GetFileName(NewFilePath)
    fileSize = FileLen(NewFilePath)
    storedFileName = GenerateUniqueFileName(fileName)

    ' نسخ الملف الجديد
    destinationPath = DOC_STORAGE_PATH & "Versions\" & storedFileName
    CreateDirectoryIfNotExists DOC_STORAGE_PATH & "Versions\"

    If CopyFile(NewFilePath, destinationPath) Then

        ' إضافة الإصدار الجديد
        Set rs = db.OpenRecordset("tbl_DocumentVersions", dbOpenDynaset)

        With rs
            .AddNew
            !DocumentID = DocumentID
            !VersionNumber = newVersionNumber
            !VersionDescription = VersionDescription
            !FileName = fileName
            !FilePath = destinationPath
            !FileSize = fileSize
            !UploadedBy = GetCurrentUserID()
            !UploadDate = Now()
            !IsCurrentVersion = False ' الإصدار الحالي يبقى كما هو
            .Update
        End With

        rs.Close
        CreateDocumentVersion = True

        ' تسجيل إنشاء الإصدار الجديد
        LogDocumentAccess DocumentID, GetCurrentUserID(), "Version", "تم إنشاء إصدار جديد: " & newVersionNumber

    Else
        CreateDocumentVersion = False
    End If

    Set rs = Nothing
    Set db = Nothing
    Exit Function

ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
    CreateDocumentVersion = False
End Function

' دالة الحصول على إحصائيات المستندات
Public Function GetDocumentStatistics(ModuleName As String) As String

    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    Dim stats As String

    Set db = CurrentDb

    sql = "SELECT COUNT(*) AS TotalDocs, SUM(FileSize) AS TotalSize, " & _
          "COUNT(CASE WHEN ApprovalStatus = 'Approved' THEN 1 END) AS ApprovedDocs, " & _
          "COUNT(CASE WHEN ApprovalStatus = 'Pending' THEN 1 END) AS PendingDocs " & _
          "FROM tbl_Documents WHERE DocumentStatus = 'Active'"

    If ModuleName <> "" Then
        sql = sql & " AND RelatedModule = '" & ModuleName & "'"
    End If

    Set rs = db.OpenRecordset(sql, dbOpenSnapshot)

    If Not rs.EOF Then
        stats = "إجمالي المستندات: " & rs!TotalDocs & vbCrLf & _
                "الحجم الإجمالي: " & FormatFileSize(Nz(rs!TotalSize, 0)) & vbCrLf & _
                "المستندات المعتمدة: " & Nz(rs!ApprovedDocs, 0) & vbCrLf & _
                "المستندات المعلقة: " & Nz(rs!PendingDocs, 0)
    Else
        stats = "لا توجد مستندات"
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    GetDocumentStatistics = stats
End Function
