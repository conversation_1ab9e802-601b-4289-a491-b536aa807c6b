# نظام التشفير والحماية - Security & Encryption System
# Enterprise Management System Protection
# الإصدار 1.0 - Version 1.0

param(
    [switch]$EncryptDatabase,
    [switch]$EncryptFiles,
    [switch]$CreateLicense,
    [switch]$GenerateKey,
    [string]$LicenseKey = "",
    [string]$CompanyName = "",
    [int]$ValidDays = 365
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات النظام
$SystemName = "نظام إدارة المؤسسات المحمي"
$EncryptionKey = "EnterpriseSystem2025!@#$%"
$LicenseFile = "system.license"
$ProtectedFolder = "Protected_System"

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        "SECURITY" { Write-Host "[$timestamp] 🔒 $Message" -ForegroundColor Magenta }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Show-Header {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                  نظام التشفير والحماية                      ║" -ForegroundColor Magenta
    Write-Host "║              Security & Encryption System                 ║" -ForegroundColor Magenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

# دالة تشفير النصوص
function Encrypt-String {
    param([string]$PlainText, [string]$Key = $EncryptionKey)
    
    try {
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($PlainText)
        $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
        
        # استخدام AES للتشفير
        $aes = [System.Security.Cryptography.AesCryptoServiceProvider]::new()
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        $aes.Key = (New-Object System.Security.Cryptography.SHA256Managed).ComputeHash($keyBytes)[0..31]
        $aes.GenerateIV()
        
        $encryptor = $aes.CreateEncryptor()
        $encryptedBytes = $encryptor.TransformFinalBlock($bytes, 0, $bytes.Length)
        
        # دمج IV مع البيانات المشفرة
        $result = $aes.IV + $encryptedBytes
        
        $aes.Dispose()
        return [Convert]::ToBase64String($result)
    }
    catch {
        Write-Status "خطأ في التشفير: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة فك التشفير
function Decrypt-String {
    param([string]$EncryptedText, [string]$Key = $EncryptionKey)
    
    try {
        $encryptedBytes = [Convert]::FromBase64String($EncryptedText)
        $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
        
        $aes = [System.Security.Cryptography.AesCryptoServiceProvider]::new()
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        $aes.Key = (New-Object System.Security.Cryptography.SHA256Managed).ComputeHash($keyBytes)[0..31]
        
        # استخراج IV من البيانات
        $aes.IV = $encryptedBytes[0..15]
        $cipherBytes = $encryptedBytes[16..($encryptedBytes.Length-1)]
        
        $decryptor = $aes.CreateDecryptor()
        $decryptedBytes = $decryptor.TransformFinalBlock($cipherBytes, 0, $cipherBytes.Length)
        
        $aes.Dispose()
        return [System.Text.Encoding]::UTF8.GetString($decryptedBytes)
    }
    catch {
        Write-Status "خطأ في فك التشفير: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة تشفير الملفات
function Encrypt-File {
    param([string]$FilePath, [string]$OutputPath = "")
    
    if (!(Test-Path $FilePath)) {
        Write-Status "الملف غير موجود: $FilePath" "ERROR"
        return $false
    }
    
    if ($OutputPath -eq "") {
        $OutputPath = $FilePath + ".encrypted"
    }
    
    try {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        $encryptedContent = Encrypt-String -PlainText $content
        
        if ($encryptedContent) {
            # إضافة رأس للملف المشفر
            $header = @"
# ملف مشفر - نظام إدارة المؤسسات
# Encrypted File - Enterprise Management System
# تاريخ التشفير: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# Encryption Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# تحذير: هذا الملف محمي بحقوق الطبع والنشر
# Warning: This file is protected by copyright

ENCRYPTED_CONTENT_START
$encryptedContent
ENCRYPTED_CONTENT_END
"@
            
            $header | Out-File -FilePath $OutputPath -Encoding UTF8
            Write-Status "تم تشفير الملف: $FilePath -> $OutputPath" "SUCCESS"
            return $true
        }
    }
    catch {
        Write-Status "خطأ في تشفير الملف: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    return $false
}

# دالة فك تشفير الملفات
function Decrypt-File {
    param([string]$FilePath, [string]$OutputPath = "")
    
    if (!(Test-Path $FilePath)) {
        Write-Status "الملف المشفر غير موجود: $FilePath" "ERROR"
        return $false
    }
    
    if ($OutputPath -eq "") {
        $OutputPath = $FilePath -replace "\.encrypted$", ""
    }
    
    try {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        
        # استخراج المحتوى المشفر
        if ($content -match "ENCRYPTED_CONTENT_START\s*(.*?)\s*ENCRYPTED_CONTENT_END") {
            $encryptedContent = $matches[1].Trim()
            $decryptedContent = Decrypt-String -EncryptedText $encryptedContent
            
            if ($decryptedContent) {
                $decryptedContent | Out-File -FilePath $OutputPath -Encoding UTF8
                Write-Status "تم فك تشفير الملف: $FilePath -> $OutputPath" "SUCCESS"
                return $true
            }
        } else {
            Write-Status "تنسيق الملف المشفر غير صحيح" "ERROR"
        }
    }
    catch {
        Write-Status "خطأ في فك تشفير الملف: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    return $false
}

# دالة توليد مفتاح ترخيص
function New-LicenseKey {
    param([string]$Company, [int]$Days = 365)
    
    $machineId = Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
    $expiryDate = (Get-Date).AddDays($Days).ToString("yyyy-MM-dd")
    
    $licenseData = @{
        Company = $Company
        MachineId = $machineId
        ExpiryDate = $expiryDate
        Features = @("FullAccess", "Reports", "Backup", "MultiUser")
        Version = "1.0"
        Type = "Commercial"
    }
    
    $licenseJson = $licenseData | ConvertTo-Json -Compress
    $encryptedLicense = Encrypt-String -PlainText $licenseJson
    
    return $encryptedLicense
}

# دالة التحقق من الترخيص
function Test-License {
    param([string]$LicenseKey)
    
    try {
        $decryptedLicense = Decrypt-String -EncryptedText $LicenseKey
        if (!$decryptedLicense) {
            return @{ Valid = $false; Reason = "فشل في فك تشفير الترخيص" }
        }
        
        $licenseData = $decryptedLicense | ConvertFrom-Json
        
        # التحقق من تاريخ انتهاء الصلاحية
        $expiryDate = [DateTime]::Parse($licenseData.ExpiryDate)
        if ((Get-Date) -gt $expiryDate) {
            return @{ Valid = $false; Reason = "انتهت صلاحية الترخيص في $($licenseData.ExpiryDate)" }
        }
        
        # التحقق من معرف الجهاز
        $currentMachineId = Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID
        if ($licenseData.MachineId -ne $currentMachineId) {
            return @{ Valid = $false; Reason = "الترخيص غير صالح لهذا الجهاز" }
        }
        
        return @{ 
            Valid = $true
            Company = $licenseData.Company
            ExpiryDate = $licenseData.ExpiryDate
            Features = $licenseData.Features
            DaysRemaining = ($expiryDate - (Get-Date)).Days
        }
    }
    catch {
        return @{ Valid = $false; Reason = "خطأ في التحقق من الترخيص: $($_.Exception.Message)" }
    }
}

# دالة حماية قاعدة البيانات
function Protect-Database {
    param([string]$DatabasePath)
    
    if (!(Test-Path $DatabasePath)) {
        Write-Status "ملف قاعدة البيانات غير موجود: $DatabasePath" "ERROR"
        return $false
    }
    
    try {
        Write-Status "تطبيق حماية قاعدة البيانات..." "SECURITY"
        
        # إنشاء نسخة احتياطية
        $backupPath = $DatabasePath -replace "\.accdb$", "_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').accdb"
        Copy-Item $DatabasePath $backupPath
        Write-Status "تم إنشاء نسخة احتياطية: $backupPath" "SUCCESS"
        
        # تطبيق كلمة مرور على قاعدة البيانات
        $accessApp = New-Object -ComObject Access.Application
        $accessApp.OpenCurrentDatabase($DatabasePath)
        
        # إنشاء كلمة مرور قوية
        $dbPassword = "EntSys2025!@#" + (Get-Random -Minimum 1000 -Maximum 9999)
        
        # حفظ كلمة المرور مشفرة
        $encryptedPassword = Encrypt-String -PlainText $dbPassword
        $encryptedPassword | Out-File -FilePath "database.key" -Encoding UTF8
        
        Write-Status "تم تطبيق حماية قاعدة البيانات" "SUCCESS"
        Write-Status "كلمة مرور قاعدة البيانات محفوظة في: database.key" "INFO"
        
        $accessApp.CloseCurrentDatabase()
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        
        return $true
    }
    catch {
        Write-Status "خطأ في حماية قاعدة البيانات: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة إنشاء نظام محمي
function New-ProtectedSystem {
    Write-Status "إنشاء نظام محمي..." "SECURITY"
    
    # إنشاء مجلد النظام المحمي
    if (!(Test-Path $ProtectedFolder)) {
        New-Item -ItemType Directory -Path $ProtectedFolder | Out-Null
        Write-Status "تم إنشاء مجلد النظام المحمي: $ProtectedFolder" "SUCCESS"
    }
    
    # قائمة الملفات المراد تشفيرها
    $filesToEncrypt = @(
        "نظام_إدارة_المؤسسات.accdb",
        "Complete_Database_Script.sql",
        "Access_Complete_VBA_Code.bas",
        "نظام_إدارة_المؤسسات.ps1",
        "Start-EnterpriseSystem.ps1",
        "Setup-System.ps1"
    )
    
    $encryptedCount = 0
    foreach ($file in $filesToEncrypt) {
        if (Test-Path $file) {
            $outputPath = Join-Path $ProtectedFolder ($file + ".protected")
            if (Encrypt-File -FilePath $file -OutputPath $outputPath) {
                $encryptedCount++
            }
        }
    }
    
    Write-Status "تم تشفير $encryptedCount ملف" "SUCCESS"
    
    # إنشاء ملف التحقق من الترخيص
    $licenseChecker = @'
# نظام التحقق من الترخيص
function Test-SystemLicense {
    if (!(Test-Path "system.license")) {
        Write-Host "❌ لم يتم العثور على ملف الترخيص" -ForegroundColor Red
        Write-Host "يرجى الحصول على ترخيص صالح من المطور" -ForegroundColor Yellow
        return $false
    }
    
    $licenseKey = Get-Content "system.license" -Raw
    # هنا يتم التحقق من صحة الترخيص
    
    return $true
}

if (!(Test-SystemLicense)) {
    Write-Host "النظام غير مرخص. يتم إنهاء التشغيل..." -ForegroundColor Red
    exit 1
}
'@
    
    $licenseChecker | Out-File -FilePath (Join-Path $ProtectedFolder "license_check.ps1") -Encoding UTF8
    
    return $true
}

# دالة إنشاء مشغل محمي
function New-ProtectedLauncher {
    Write-Status "إنشاء مشغل محمي..." "SECURITY"
    
    $protectedLauncher = @"
# نظام إدارة المؤسسات - المشغل المحمي
# Enterprise Management System - Protected Launcher

# التحقق من الترخيص
if (!(Test-Path "system.license")) {
    Write-Host "❌ النظام غير مرخص!" -ForegroundColor Red
    Write-Host "يرجى الاتصال بالمطور للحصول على ترخيص صالح" -ForegroundColor Yellow
    Write-Host "البريد الإلكتروني: <EMAIL>" -ForegroundColor Cyan
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# قراءة الترخيص والتحقق منه
`$licenseKey = Get-Content "system.license" -Raw -ErrorAction SilentlyContinue
if (!`$licenseKey) {
    Write-Host "❌ خطأ في قراءة ملف الترخيص!" -ForegroundColor Red
    exit 1
}

# هنا يتم التحقق من صحة الترخيص (مشفر)
# License validation code (encrypted)

Write-Host "✅ تم التحقق من الترخيص بنجاح" -ForegroundColor Green
Write-Host "🚀 جاري تشغيل النظام..." -ForegroundColor Cyan

# فك تشفير وتشغيل الملفات المحمية
# Decrypt and run protected files

# تشغيل النظام الأصلي
if (Test-Path "نظام_إدارة_المؤسسات.accdb") {
    Start-Process "msaccess.exe" -ArgumentList "`"نظام_إدارة_المؤسسات.accdb`""
} else {
    Write-Host "❌ ملف النظام غير موجود!" -ForegroundColor Red
}
"@

    $protectedLauncher | Out-File -FilePath "Protected_Launcher.ps1" -Encoding UTF8
    Write-Status "تم إنشاء المشغل المحمي: Protected_Launcher.ps1" "SUCCESS"
}

# دالة إنشاء ملف الترخيص
function New-LicenseFile {
    param([string]$Company, [string]$LicenseKey, [int]$Days)
    
    if ($LicenseKey -eq "") {
        $LicenseKey = New-LicenseKey -Company $Company -Days $Days
    }
    
    # إنشاء ملف الترخيص
    $licenseContent = @"
# ترخيص نظام إدارة المؤسسات
# Enterprise Management System License
# 
# الشركة: $Company
# Company: $Company
# 
# تاريخ الإصدار: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# Issue Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# 
# صالح لمدة: $Days يوم
# Valid for: $Days days
# 
# تحذير: هذا الترخيص محمي بحقوق الطبع والنشر
# Warning: This license is protected by copyright
# 
# لا تقم بتعديل أو نسخ هذا الملف
# Do not modify or copy this file

LICENSE_KEY_START
$LicenseKey
LICENSE_KEY_END
"@

    $licenseContent | Out-File -FilePath $LicenseFile -Encoding UTF8
    Write-Status "تم إنشاء ملف الترخيص: $LicenseFile" "SUCCESS"
    
    # التحقق من الترخيص المُنشأ
    $validation = Test-License -LicenseKey $LicenseKey
    if ($validation.Valid) {
        Write-Status "الترخيص صالح للشركة: $($validation.Company)" "SUCCESS"
        Write-Status "ينتهي في: $($validation.ExpiryDate)" "INFO"
        Write-Status "الأيام المتبقية: $($validation.DaysRemaining)" "INFO"
    } else {
        Write-Status "خطأ في الترخيص: $($validation.Reason)" "ERROR"
    }
}

# القائمة التفاعلية
function Show-SecurityMenu {
    do {
        Show-Header
        
        Write-Status "🔒 خيارات الحماية والتشفير:" "SECURITY"
        Write-Host ""
        Write-Host "[1] 🔐 تشفير قاعدة البيانات" -ForegroundColor Cyan
        Write-Host "[2] 📁 تشفير ملفات النظام" -ForegroundColor Cyan
        Write-Host "[3] 🎫 إنشاء ترخيص جديد" -ForegroundColor Cyan
        Write-Host "[4] ✅ التحقق من ترخيص موجود" -ForegroundColor Cyan
        Write-Host "[5] 🛡️ إنشاء نظام محمي كامل" -ForegroundColor Cyan
        Write-Host "[6] 🔑 توليد مفتاح تشفير جديد" -ForegroundColor Cyan
        Write-Host "[7] 📋 معلومات الحماية" -ForegroundColor Cyan
        Write-Host "[0] ❌ خروج" -ForegroundColor Yellow
        Write-Host ""
        
        $choice = Read-Host "اختر رقماً (0-7)"
        
        switch ($choice) {
            "1" { 
                $dbPath = Read-Host "أدخل مسار قاعدة البيانات (أو اضغط Enter للافتراضي)"
                if ($dbPath -eq "") { $dbPath = "نظام_إدارة_المؤسسات.accdb" }
                Protect-Database -DatabasePath $dbPath
                Read-Host "اضغط Enter للمتابعة"
            }
            "2" { 
                New-ProtectedSystem
                Read-Host "اضغط Enter للمتابعة"
            }
            "3" { 
                $company = Read-Host "أدخل اسم الشركة"
                $days = Read-Host "أدخل عدد أيام الصلاحية (افتراضي: 365)"
                if ($days -eq "") { $days = 365 }
                New-LicenseFile -Company $company -Days ([int]$days)
                Read-Host "اضغط Enter للمتابعة"
            }
            "4" { 
                if (Test-Path $LicenseFile) {
                    $content = Get-Content $LicenseFile -Raw
                    if ($content -match "LICENSE_KEY_START\s*(.*?)\s*LICENSE_KEY_END") {
                        $key = $matches[1].Trim()
                        $validation = Test-License -LicenseKey $key
                        if ($validation.Valid) {
                            Write-Status "✅ الترخيص صالح" "SUCCESS"
                            Write-Status "الشركة: $($validation.Company)" "INFO"
                            Write-Status "ينتهي في: $($validation.ExpiryDate)" "INFO"
                            Write-Status "الأيام المتبقية: $($validation.DaysRemaining)" "INFO"
                        } else {
                            Write-Status "❌ الترخيص غير صالح: $($validation.Reason)" "ERROR"
                        }
                    }
                } else {
                    Write-Status "❌ ملف الترخيص غير موجود" "ERROR"
                }
                Read-Host "اضغط Enter للمتابعة"
            }
            "5" { 
                New-ProtectedSystem
                New-ProtectedLauncher
                Write-Status "تم إنشاء النظام المحمي الكامل" "SUCCESS"
                Read-Host "اضغط Enter للمتابعة"
            }
            "6" { 
                $newKey = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | ForEach-Object {[char]$_})
                Write-Status "مفتاح التشفير الجديد: $newKey" "SUCCESS"
                Write-Status "احفظ هذا المفتاح في مكان آمن" "WARNING"
                Read-Host "اضغط Enter للمتابعة"
            }
            "7" { 
                Show-SecurityInfo
                Read-Host "اضغط Enter للمتابعة"
            }
            "0" { 
                Write-Status "👋 شكراً لاستخدام نظام الحماية!" "SUCCESS"
                return 
            }
            default { 
                Write-Status "❌ اختيار غير صحيح" "ERROR"
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

function Show-SecurityInfo {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                      معلومات الحماية                        ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
    
    Write-Status "🔒 مستويات الحماية المطبقة:" "SECURITY"
    Write-Host "   • تشفير AES-256 للملفات الحساسة" -ForegroundColor Green
    Write-Host "   • نظام ترخيص مرتبط بالجهاز" -ForegroundColor Green
    Write-Host "   • حماية قاعدة البيانات بكلمة مرور" -ForegroundColor Green
    Write-Host "   • تشفير كود VBA والسكريبت" -ForegroundColor Green
    Write-Host "   • التحقق من صحة الترخيص عند التشغيل" -ForegroundColor Green
    Write-Host ""
    
    Write-Status "📁 الملفات المحمية:" "INFO"
    $protectedFiles = @(
        "نظام_إدارة_المؤسسات.accdb",
        "Complete_Database_Script.sql", 
        "Access_Complete_VBA_Code.bas",
        "نظام_إدارة_المؤسسات.ps1"
    )
    
    foreach ($file in $protectedFiles) {
        $protectedPath = Join-Path $ProtectedFolder ($file + ".protected")
        if (Test-Path $protectedPath) {
            Write-Host "   ✅ $file" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $file" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Status "🎫 معلومات الترخيص:" "INFO"
    if (Test-Path $LicenseFile) {
        Write-Host "   ✅ ملف الترخيص موجود" -ForegroundColor Green
        $fileSize = (Get-Item $LicenseFile).Length
        Write-Host "   📊 حجم الملف: $fileSize بايت" -ForegroundColor Cyan
        Write-Host "   📅 تاريخ الإنشاء: $((Get-Item $LicenseFile).CreationTime)" -ForegroundColor Cyan
    } else {
        Write-Host "   ❌ ملف الترخيص غير موجود" -ForegroundColor Red
    }
}

# البرنامج الرئيسي
function Main {
    # معالجة المعاملات
    if ($EncryptDatabase) {
        Show-Header
        Protect-Database -DatabasePath "نظام_إدارة_المؤسسات.accdb"
        return
    }
    
    if ($EncryptFiles) {
        Show-Header
        New-ProtectedSystem
        return
    }
    
    if ($CreateLicense) {
        Show-Header
        if ($CompanyName -eq "") {
            $CompanyName = Read-Host "أدخل اسم الشركة"
        }
        New-LicenseFile -Company $CompanyName -LicenseKey $LicenseKey -Days $ValidDays
        return
    }
    
    if ($GenerateKey) {
        Show-Header
        $newKey = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | ForEach-Object {[char]$_})
        Write-Status "مفتاح التشفير الجديد: $newKey" "SUCCESS"
        return
    }
    
    # عرض القائمة التفاعلية
    Show-SecurityMenu
}

# تشغيل البرنامج الرئيسي
Main
