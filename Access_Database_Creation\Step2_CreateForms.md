# إنشاء النماذج - الخطوة الثانية
## Creating Forms - Step Two

### نموذج تسجيل الدخول (frm_Login)

#### إنشاء النموذج:
```
1. اذه<PERSON> إلى Create > Form Design
2. احفظ النموذج باسم "frm_Login"
3. اضبط خصائص النموذج:
   - Caption: تسجيل الدخول - Login
   - Modal: Yes
   - PopUp: Yes
   - AutoCenter: Yes
   - BorderStyle: Dialog
   - RecordSelectors: No
   - NavigationButtons: No
   - MinMaxButtons: None
   - Width: 8 cm
   - Height: 6 cm
```

#### إضافة العناصر:
```
1. Label (عنوان):
   - Name: lbl_Title
   - Caption: نظام إدارة المؤسسات
   - Font: Tahoma, 16pt, Bold
   - ForeColor: Blue
   - Left: 1 cm, Top: 0.5 cm, Width: 6 cm

2. Label (اسم المستخدم):
   - Name: lbl_Username
   - Caption: اسم المستخدم:
   - Left: 1 cm, Top: 2 cm, Width: 2 cm

3. TextBox (مربع اسم المستخدم):
   - Name: txt_Username
   - Left: 3.5 cm, Top: 2 cm, Width: 3 cm

4. Label (كلمة المرور):
   - Name: lbl_Password
   - Caption: كلمة المرور:
   - Left: 1 cm, Top: 2.8 cm, Width: 2 cm

5. TextBox (مربع كلمة المرور):
   - Name: txt_Password
   - InputMask: Password
   - Left: 3.5 cm, Top: 2.8 cm, Width: 3 cm

6. Command Button (دخول):
   - Name: btn_Login
   - Caption: دخول
   - Left: 2 cm, Top: 4 cm, Width: 1.5 cm
   - BackColor: Blue, ForeColor: White

7. Command Button (إلغاء):
   - Name: btn_Cancel
   - Caption: إلغاء
   - Left: 4 cm, Top: 4 cm, Width: 1.5 cm
```

#### كود VBA للنموذج:
```vba
' في Module الخاص بالنموذج
Private Sub btn_Login_Click()
    Dim Username As String
    Dim Password As String
    
    Username = Nz(Me.txt_Username, "")
    Password = Nz(Me.txt_Password, "")
    
    If Username = "" Or Password = "" Then
        MsgBox "يرجى إدخال اسم المستخدم وكلمة المرور", vbExclamation
        Exit Sub
    End If
    
    ' التحقق من بيانات المستخدم
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT * FROM tbl_Users WHERE Username = '" & Username & "' AND Password = '" & Password & "' AND IsActive = True")
    
    If Not rs.EOF Then
        ' تسجيل دخول ناجح
        TempVars.Add "CurrentUserID", rs!UserID
        TempVars.Add "CurrentUsername", rs!Username
        TempVars.Add "CurrentUserRole", rs!RoleID
        
        ' تحديث آخر دخول
        CurrentDb.Execute "UPDATE tbl_Users SET LastLogin = Now() WHERE UserID = " & rs!UserID
        
        ' إغلاق نموذج الدخول وفتح النموذج الرئيسي
        DoCmd.Close acForm, Me.Name
        DoCmd.OpenForm "frm_MainNavigation"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbCritical
        Me.txt_Password = ""
        Me.txt_Username.SetFocus
    End If
    
    rs.Close
    Set rs = Nothing
End Sub

Private Sub btn_Cancel_Click()
    Application.Quit
End Sub

Private Sub Form_Load()
    ' تعيين قيم افتراضية للاختبار
    Me.txt_Username = "admin"
    Me.txt_Password = "12345"
End Sub
```

---

### النموذج الرئيسي (frm_MainNavigation)

#### إنشاء النموذج:
```
1. Create > Form Design
2. احفظ باسم "frm_MainNavigation"
3. خصائص النموذج:
   - Caption: نظام إدارة المؤسسات - الصفحة الرئيسية
   - Modal: No
   - PopUp: No
   - AutoCenter: Yes
   - BorderStyle: Sizable
   - Width: 20 cm
   - Height: 15 cm
   - BackColor: RGB(245, 245, 245)
```

#### قسم الرأس (Header):
```
1. Rectangle (خلفية الرأس):
   - BackColor: RGB(46, 134, 171) - أزرق
   - Left: 0, Top: 0, Width: 20 cm, Height: 2 cm

2. Label (عنوان النظام):
   - Name: lbl_SystemTitle
   - Caption: نظام إدارة المؤسسات الشامل
   - Font: Tahoma, 18pt, Bold, White
   - Left: 0.5 cm, Top: 0.3 cm

3. Label (العنوان الإنجليزي):
   - Name: lbl_SystemTitleEn
   - Caption: Enterprise Management System
   - Font: Segoe UI, 12pt, White
   - Left: 0.5 cm, Top: 1 cm

4. Label (معلومات المستخدم):
   - Name: lbl_UserInfo
   - Caption: مرحباً، [اسم المستخدم]
   - Font: Tahoma, 11pt, White
   - Left: 15 cm, Top: 0.5 cm

5. Command Button (تسجيل خروج):
   - Name: btn_Logout
   - Caption: تسجيل خروج
   - BackColor: Red, ForeColor: White
   - Left: 17.5 cm, Top: 0.8 cm, Width: 2 cm
```

#### أزرار الوحدات (4 صفوف × 3 أعمدة):

**الصف الأول:**
```
1. btn_Dashboard:
   - Caption: لوحة التحكم
   - Left: 1 cm, Top: 3 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(46, 134, 171)

2. btn_Clients:
   - Caption: إدارة العملاء
   - Left: 7 cm, Top: 3 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(76, 175, 80)

3. btn_Sales:
   - Caption: إدارة المبيعات
   - Left: 13 cm, Top: 3 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(156, 39, 176)
```

**الصف الثاني:**
```
4. btn_Purchases:
   - Caption: إدارة المشتريات
   - Left: 1 cm, Top: 6 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(255, 152, 0)

5. btn_Inventory:
   - Caption: إدارة المخازن
   - Left: 7 cm, Top: 6 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(121, 85, 72)

6. btn_Suppliers:
   - Caption: إدارة الموردين
   - Left: 13 cm, Top: 6 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(0, 150, 136)
```

**الصف الثالث:**
```
7. btn_Employees:
   - Caption: إدارة الموظفين
   - Left: 1 cm, Top: 9 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(63, 81, 181)

8. btn_Accounting:
   - Caption: إدارة الحسابات
   - Left: 7 cm, Top: 9 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(255, 87, 34)

9. btn_Invoices:
   - Caption: إدارة الفواتير
   - Left: 13 cm, Top: 9 cm, Width: 5 cm, Height: 2 cm
   - BackColor: RGB(3, 169, 244)
```

**الصف الرابع:**
```
10. btn_Reports:
    - Caption: إدارة التقارير
    - Left: 1 cm, Top: 12 cm, Width: 5 cm, Height: 2 cm
    - BackColor: RGB(139, 195, 74)

11. btn_Users:
    - Caption: إدارة المستخدمين
    - Left: 7 cm, Top: 12 cm, Width: 5 cm, Height: 2 cm
    - BackColor: RGB(244, 67, 54)

12. btn_Database:
    - Caption: إدارة قاعدة البيانات
    - Left: 13 cm, Top: 12 cm, Width: 5 cm, Height: 2 cm
    - BackColor: RGB(96, 125, 139)
```

#### كود VBA للنموذج الرئيسي:
```vba
Private Sub Form_Load()
    ' عرض معلومات المستخدم
    If Not IsNull(TempVars("CurrentUsername")) Then
        Me.lbl_UserInfo.Caption = "مرحباً، " & TempVars("CurrentUsername")
    End If
End Sub

Private Sub btn_Dashboard_Click()
    DoCmd.OpenForm "frm_Dashboard"
End Sub

Private Sub btn_Clients_Click()
    DoCmd.OpenForm "frm_Clients"
End Sub

Private Sub btn_Sales_Click()
    MsgBox "وحدة إدارة المبيعات قيد التطوير", vbInformation
End Sub

Private Sub btn_Purchases_Click()
    MsgBox "وحدة إدارة المشتريات قيد التطوير", vbInformation
End Sub

Private Sub btn_Inventory_Click()
    DoCmd.OpenForm "frm_Inventory"
End Sub

Private Sub btn_Suppliers_Click()
    DoCmd.OpenForm "frm_Suppliers"
End Sub

Private Sub btn_Employees_Click()
    MsgBox "وحدة إدارة الموظفين قيد التطوير", vbInformation
End Sub

Private Sub btn_Accounting_Click()
    MsgBox "وحدة إدارة الحسابات قيد التطوير", vbInformation
End Sub

Private Sub btn_Invoices_Click()
    MsgBox "وحدة إدارة الفواتير قيد التطوير", vbInformation
End Sub

Private Sub btn_Reports_Click()
    MsgBox "وحدة إدارة التقارير قيد التطوير", vbInformation
End Sub

Private Sub btn_Users_Click()
    DoCmd.OpenForm "frm_Users"
End Sub

Private Sub btn_Database_Click()
    MsgBox "وحدة إدارة قاعدة البيانات قيد التطوير", vbInformation
End Sub

Private Sub btn_Logout_Click()
    If MsgBox("هل تريد تسجيل الخروج؟", vbYesNo + vbQuestion) = vbYes Then
        ' مسح متغيرات المستخدم
        TempVars.RemoveAll
        
        ' إغلاق النموذج الحالي وفتح نموذج الدخول
        DoCmd.Close acForm, Me.Name
        DoCmd.OpenForm "frm_Login"
    End If
End Sub
```

---

### نموذج إدارة العملاء (frm_Clients)

#### إنشاء النموذج:
```
1. Create > Form Wizard
2. اختر tbl_Clients
3. اختر جميع الحقول
4. Layout: Tabular
5. احفظ باسم "frm_Clients"
```

#### تخصيص النموذج:
```
1. اضبط خصائص النموذج:
   - Caption: إدارة العملاء
   - AllowAdditions: Yes
   - AllowEdits: Yes
   - AllowDeletions: No

2. أضف أزرار التحكم:
   - btn_New: عميل جديد
   - btn_Edit: تعديل
   - btn_Delete: حذف
   - btn_Search: بحث
   - btn_Close: إغلاق
```

#### كود VBA لنموذج العملاء:
```vba
Private Sub Form_Load()
    ' تحديث عداد العملاء
    Me.Caption = "إدارة العملاء - العدد: " & DCount("*", "tbl_Clients", "IsActive = True")
End Sub

Private Sub btn_New_Click()
    DoCmd.GoToRecord , , acNewRec
    Me.ClientCode = GenerateClientCode()
    Me.CreatedDate = Now()
    Me.CreatedBy = TempVars("CurrentUserID")
    Me.IsActive = True
End Sub

Private Sub btn_Edit_Click()
    If Not IsNull(Me.ClientID) Then
        Me.AllowEdits = True
        Me.CompanyName.SetFocus
    End If
End Sub

Private Sub btn_Delete_Click()
    If Not IsNull(Me.ClientID) Then
        If MsgBox("هل تريد حذف هذا العميل؟", vbYesNo + vbQuestion) = vbYes Then
            ' حذف ناعم - تعطيل العميل
            Me.IsActive = False
            DoCmd.RunCommand acCmdSaveRecord
            Me.Requery
        End If
    End If
End Sub

Private Sub btn_Search_Click()
    Dim SearchTerm As String
    SearchTerm = InputBox("ادخل اسم الشركة أو رمز العميل:")
    
    If SearchTerm <> "" Then
        Me.Filter = "CompanyName Like '*" & SearchTerm & "*' OR ClientCode Like '*" & SearchTerm & "*'"
        Me.FilterOn = True
    End If
End Sub

Private Sub btn_Close_Click()
    DoCmd.Close acForm, Me.Name
End Sub

' دالة لتوليد رمز العميل التالي
Private Function GenerateClientCode() As String
    Dim rs As DAO.Recordset
    Dim NextNumber As Long
    
    Set rs = CurrentDb.OpenRecordset("SELECT MAX(Val(Mid(ClientCode, 3))) AS MaxNumber FROM tbl_Clients WHERE ClientCode LIKE 'CL*'")
    
    If IsNull(rs!MaxNumber) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNumber + 1
    End If
    
    GenerateClientCode = "CL" & Format(NextNumber, "0000")
    rs.Close
End Function
```

### إعداد النموذج كنموذج البداية:

```
1. اذهب إلى File > Options > Current Database
2. في Display Form اختر "frm_Login"
3. احفظ وأعد تشغيل قاعدة البيانات
```

### اختبار النظام:

```
1. أغلق قاعدة البيانات وأعد فتحها
2. يجب أن يظهر نموذج تسجيل الدخول تلقائياً
3. استخدم: admin / 12345
4. تأكد من فتح النموذج الرئيسي
5. اختبر الأزرار والتنقل
```

**الخطوة التالية:** إضافة المزيد من النماذج والتقارير
