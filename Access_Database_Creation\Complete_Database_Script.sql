-- نظام إدارة المؤسسات الشامل - سكريبت قاعدة البيانات الكامل
-- Enterprise Management System - Complete Database Script
-- تنفذ هذه الأوامر في Microsoft Access Query Design (SQL View)

-- ============================================================================
-- إنشاء الجداول الأساسية - CORE TABLES CREATION
-- ============================================================================

-- 1. جدول الأدوار - Roles Table
CREATE TABLE tbl_Roles (
    RoleID AUTOINCREMENT CONSTRAINT PK_Roles PRIMARY KEY,
    RoleName TEXT(50) NOT NULL,
    Description TEXT(255),
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now()
);

-- 2. جدول المستخدمين - Users Table
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT CONSTRAINT PK_Users PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    RoleID LONG,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME,
    CONSTRAINT UK_Users_Username UNIQUE (Username)
);

-- 3. جدول العملاء - Clients Table
CREATE TABLE tbl_Clients (
    ClientID AUTOINCREMENT CONSTRAINT PK_Clients PRIMARY KEY,
    ClientCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Website TEXT(200),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50) DEFAULT "السعودية",
    TaxID TEXT(50),
    CreditLimit CURRENCY DEFAULT 0,
    PaymentTerms LONG DEFAULT 30,
    ClientType TEXT(20) DEFAULT "Customer",
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Clients_ClientCode UNIQUE (ClientCode)
);

-- 4. جدول جهات اتصال العملاء - Client Contacts Table
CREATE TABLE tbl_ClientContacts (
    ContactID AUTOINCREMENT CONSTRAINT PK_ClientContacts PRIMARY KEY,
    ClientID LONG NOT NULL,
    ContactName TEXT(100) NOT NULL,
    Position TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    IsPrimary YESNO DEFAULT No
);

-- 5. جدول الموردين - Suppliers Table
CREATE TABLE tbl_Suppliers (
    SupplierID AUTOINCREMENT CONSTRAINT PK_Suppliers PRIMARY KEY,
    SupplierCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50) DEFAULT "السعودية",
    TaxID TEXT(50),
    PaymentTerms LONG DEFAULT 30,
    Currency TEXT(3) DEFAULT "SAR",
    Rating LONG DEFAULT 0,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO,
    CONSTRAINT UK_Suppliers_SupplierCode UNIQUE (SupplierCode)
);

-- 6. جدول فئات المنتجات - Categories Table
CREATE TABLE tbl_Categories (
    CategoryID AUTOINCREMENT CONSTRAINT PK_Categories PRIMARY KEY,
    CategoryName TEXT(100) NOT NULL,
    ParentCategoryID LONG,
    Description TEXT(255),
    IsActive YESNO DEFAULT Yes,
    CONSTRAINT UK_Categories_CategoryName UNIQUE (CategoryName)
);

-- 7. جدول المنتجات - Products Table
CREATE TABLE tbl_Products (
    ProductID AUTOINCREMENT CONSTRAINT PK_Products PRIMARY KEY,
    ProductCode TEXT(50) NOT NULL,
    ProductName TEXT(200) NOT NULL,
    CategoryID LONG,
    Description MEMO,
    UnitOfMeasure TEXT(20) DEFAULT "قطعة",
    CostPrice CURRENCY DEFAULT 0,
    SellingPrice CURRENCY DEFAULT 0,
    MinimumStock LONG DEFAULT 0,
    ReorderPoint LONG DEFAULT 0,
    Barcode TEXT(100),
    IsActive YESNO DEFAULT Yes,
    IsService YESNO DEFAULT No,
    SupplierID LONG,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    CONSTRAINT UK_Products_ProductCode UNIQUE (ProductCode)
);

-- 8. جدول مستويات المخزون - Stock Levels Table
CREATE TABLE tbl_StockLevels (
    StockID AUTOINCREMENT CONSTRAINT PK_StockLevels PRIMARY KEY,
    ProductID LONG NOT NULL,
    CurrentStock LONG DEFAULT 0,
    ReservedStock LONG DEFAULT 0,
    LastUpdated DATETIME DEFAULT Now(),
    CONSTRAINT UK_StockLevels_ProductID UNIQUE (ProductID)
);

-- 9. جدول حركات المخزون - Inventory Transactions Table
CREATE TABLE tbl_InventoryTransactions (
    TransactionID AUTOINCREMENT CONSTRAINT PK_InventoryTransactions PRIMARY KEY,
    ProductID LONG NOT NULL,
    TransactionType TEXT(20) NOT NULL,
    Quantity LONG NOT NULL,
    UnitCost CURRENCY,
    ReferenceType TEXT(20),
    ReferenceID LONG,
    TransactionDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO
);

-- 10. جدول الموظفين - Employees Table
CREATE TABLE tbl_Employees (
    EmployeeID AUTOINCREMENT CONSTRAINT PK_Employees PRIMARY KEY,
    EmployeeCode TEXT(20) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    HireDate DATETIME NOT NULL,
    Department TEXT(100),
    Position TEXT(100),
    Salary CURRENCY,
    IsActive YESNO DEFAULT Yes,
    Address MEMO,
    EmergencyContact TEXT(100),
    EmergencyPhone TEXT(20),
    CreatedDate DATETIME DEFAULT Now(),
    CONSTRAINT UK_Employees_EmployeeCode UNIQUE (EmployeeCode)
);

-- 11. جدول عروض الأسعار - Quotations Table
CREATE TABLE tbl_Quotations (
    QuoteID AUTOINCREMENT CONSTRAINT PK_Quotations PRIMARY KEY,
    QuoteNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    QuoteDate DATETIME DEFAULT Now(),
    ExpiryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Quotations_QuoteNumber UNIQUE (QuoteNumber)
);

-- 12. جدول بنود عروض الأسعار - Quotation Items Table
CREATE TABLE tbl_QuotationItems (
    QuoteItemID AUTOINCREMENT CONSTRAINT PK_QuotationItems PRIMARY KEY,
    QuoteID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    DiscountPercentage SINGLE DEFAULT 0,
    LineTotal CURRENCY NOT NULL,
    Description TEXT(255)
);

-- 13. جدول أوامر البيع - Sales Orders Table
CREATE TABLE tbl_SalesOrders (
    OrderID AUTOINCREMENT CONSTRAINT PK_SalesOrders PRIMARY KEY,
    OrderNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    QuoteID LONG,
    OrderDate DATETIME DEFAULT Now(),
    DeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Pending",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    ShippingAddress MEMO,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_SalesOrders_OrderNumber UNIQUE (OrderNumber)
);

-- 14. جدول بنود أوامر البيع - Sales Order Items Table
CREATE TABLE tbl_SalesOrderItems (
    OrderItemID AUTOINCREMENT CONSTRAINT PK_SalesOrderItems PRIMARY KEY,
    OrderID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    DiscountPercentage SINGLE DEFAULT 0,
    LineTotal CURRENCY NOT NULL,
    QuantityShipped LONG DEFAULT 0,
    Description TEXT(255)
);

-- 15. جدول أوامر الشراء - Purchase Orders Table
CREATE TABLE tbl_PurchaseOrders (
    PurchaseOrderID AUTOINCREMENT CONSTRAINT PK_PurchaseOrders PRIMARY KEY,
    PONumber TEXT(50) NOT NULL,
    SupplierID LONG NOT NULL,
    OrderDate DATETIME DEFAULT Now(),
    ExpectedDeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_PurchaseOrders_PONumber UNIQUE (PONumber)
);

-- 16. جدول بنود أوامر الشراء - Purchase Order Items Table
CREATE TABLE tbl_PurchaseOrderItems (
    POItemID AUTOINCREMENT CONSTRAINT PK_PurchaseOrderItems PRIMARY KEY,
    PurchaseOrderID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitCost CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL,
    QuantityReceived LONG DEFAULT 0
);

-- 17. جدول دليل الحسابات - Chart of Accounts Table
CREATE TABLE tbl_Accounts (
    AccountID AUTOINCREMENT CONSTRAINT PK_Accounts PRIMARY KEY,
    AccountCode TEXT(20) NOT NULL,
    AccountName TEXT(200) NOT NULL,
    AccountType TEXT(50) NOT NULL,
    ParentAccountID LONG,
    IsActive YESNO DEFAULT Yes,
    Description TEXT(255),
    CONSTRAINT UK_Accounts_AccountCode UNIQUE (AccountCode)
);

-- 18. جدول المعاملات المالية - Financial Transactions Table
CREATE TABLE tbl_Transactions (
    TransactionID AUTOINCREMENT CONSTRAINT PK_Transactions PRIMARY KEY,
    TransactionNumber TEXT(50) NOT NULL,
    TransactionDate DATETIME DEFAULT Now(),
    Description TEXT(255) NOT NULL,
    ReferenceType TEXT(20),
    ReferenceID LONG,
    TotalAmount CURRENCY NOT NULL,
    CreatedBy LONG,
    CONSTRAINT UK_Transactions_TransactionNumber UNIQUE (TransactionNumber)
);

-- 19. جدول بنود المعاملات - Transaction Items Table
CREATE TABLE tbl_TransactionItems (
    TransactionItemID AUTOINCREMENT CONSTRAINT PK_TransactionItems PRIMARY KEY,
    TransactionID LONG NOT NULL,
    AccountID LONG NOT NULL,
    DebitAmount CURRENCY DEFAULT 0,
    CreditAmount CURRENCY DEFAULT 0,
    Description TEXT(255)
);

-- 20. جدول الفواتير - Invoices Table
CREATE TABLE tbl_Invoices (
    InvoiceID AUTOINCREMENT CONSTRAINT PK_Invoices PRIMARY KEY,
    InvoiceNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    SalesOrderID LONG,
    InvoiceDate DATETIME DEFAULT Now(),
    DueDate DATETIME NOT NULL,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    PaymentTerms LONG DEFAULT 30,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Invoices_InvoiceNumber UNIQUE (InvoiceNumber)
);

-- 21. جدول بنود الفواتير - Invoice Items Table
CREATE TABLE tbl_InvoiceItems (
    InvoiceItemID AUTOINCREMENT CONSTRAINT PK_InvoiceItems PRIMARY KEY,
    InvoiceID LONG NOT NULL,
    ProductID LONG,
    Description TEXT(255) NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL
);

-- 22. جدول المدفوعات - Payments Table
CREATE TABLE tbl_Payments (
    PaymentID AUTOINCREMENT CONSTRAINT PK_Payments PRIMARY KEY,
    PaymentNumber TEXT(50) NOT NULL,
    ClientID LONG,
    InvoiceID LONG,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentMethod TEXT(50) NOT NULL,
    Amount CURRENCY NOT NULL,
    ReferenceNumber TEXT(100),
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Payments_PaymentNumber UNIQUE (PaymentNumber)
);

-- 23. جدول إعدادات النظام - System Settings Table
CREATE TABLE tbl_SystemSettings (
    SettingID AUTOINCREMENT CONSTRAINT PK_SystemSettings PRIMARY KEY,
    SettingKey TEXT(100) NOT NULL,
    SettingValue MEMO,
    SettingType TEXT(20) DEFAULT "STRING",
    Description TEXT(255),
    IsSystem YESNO DEFAULT No,
    UpdatedDate DATETIME DEFAULT Now(),
    UpdatedBy LONG,
    CONSTRAINT UK_SystemSettings_SettingKey UNIQUE (SettingKey)
);

-- 24. جدول سجل النظام - System Log Table
CREATE TABLE tbl_SystemLog (
    LogID AUTOINCREMENT CONSTRAINT PK_SystemLog PRIMARY KEY,
    LogType TEXT(50) NOT NULL,
    Description MEMO,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG
);

-- ============================================================================
-- إدخال البيانات الأساسية - INSERT DEFAULT DATA
-- ============================================================================

-- إدخال الأدوار الافتراضية
INSERT INTO tbl_Roles (RoleName, Description) VALUES 
('مدير النظام', 'صلاحية كاملة على النظام'),
('مدير عام', 'صلاحيات إدارية عامة'),
('مدير مبيعات', 'إدارة المبيعات والعملاء'),
('مدير مشتريات', 'إدارة المشتريات والموردين'),
('أمين مخزن', 'إدارة المخزون'),
('محاسب', 'إدارة الحسابات والفواتير'),
('موظف إدخال بيانات', 'إدخال البيانات الأساسية');

-- إدخال المستخدم الافتراضي
INSERT INTO tbl_Users (Username, Password, FirstName, LastName, RoleID, IsActive) 
VALUES ('admin', '12345', 'مدير', 'النظام', 1, True);

-- إدخال إعدادات النظام الأساسية
INSERT INTO tbl_SystemSettings (SettingKey, SettingValue, SettingType, Description) VALUES
('CompanyName', 'شركة إدارة المؤسسات', 'STRING', 'اسم الشركة'),
('CompanyAddress', 'الرياض، المملكة العربية السعودية', 'STRING', 'عنوان الشركة'),
('CompanyPhone', '+966-11-1234567', 'STRING', 'هاتف الشركة'),
('CompanyEmail', '<EMAIL>', 'STRING', 'بريد الشركة الإلكتروني'),
('Currency', 'SAR', 'STRING', 'العملة الافتراضية'),
('TaxRate', '15', 'DECIMAL', 'معدل الضريبة المضافة'),
('Language', 'AR', 'STRING', 'لغة النظام الافتراضية'),
('BackupFrequency', '7', 'INTEGER', 'تكرار النسخ الاحتياطي بالأيام'),
('SessionTimeout', '30', 'INTEGER', 'انتهاء الجلسة بالدقائق');

-- إدخال فئات المنتجات الأساسية
INSERT INTO tbl_Categories (CategoryName, Description) VALUES
('إلكترونيات', 'الأجهزة الإلكترونية والكهربائية'),
('مكتبية', 'اللوازم المكتبية والقرطاسية'),
('أثاث', 'الأثاث المكتبي والمنزلي'),
('خدمات', 'الخدمات المختلفة'),
('قطع غيار', 'قطع الغيار والصيانة');

-- إدخال دليل الحسابات الأساسي
INSERT INTO tbl_Accounts (AccountCode, AccountName, AccountType, Description) VALUES
('1000', 'الأصول', 'Asset', 'مجموعة الأصول'),
('1100', 'الأصول المتداولة', 'Asset', 'الأصول قصيرة الأجل'),
('1110', 'النقدية', 'Asset', 'النقد في الصندوق والبنك'),
('1120', 'العملاء', 'Asset', 'مستحقات العملاء'),
('1130', 'المخزون', 'Asset', 'قيمة البضاعة'),
('2000', 'الخصوم', 'Liability', 'مجموعة الخصوم'),
('2100', 'الخصوم المتداولة', 'Liability', 'الخصوم قصيرة الأجل'),
('2110', 'الموردين', 'Liability', 'مستحقات الموردين'),
('3000', 'حقوق الملكية', 'Equity', 'رأس المال والأرباح'),
('4000', 'الإيرادات', 'Revenue', 'إيرادات المبيعات'),
('5000', 'المصروفات', 'Expense', 'مصروفات التشغيل');

-- إدخال بيانات تجريبية للاختبار
INSERT INTO tbl_Clients (ClientCode, CompanyName, ContactPerson, Phone, Email, ClientType, IsActive, CreatedBy)
VALUES 
('CL0001', 'شركة الاختبار المحدودة', 'أحمد محمد', '0501234567', '<EMAIL>', 'Customer', True, 1),
('CL0002', 'مؤسسة التجارة الحديثة', 'فاطمة أحمد', '0507654321', '<EMAIL>', 'Customer', True, 1),
('CL0003', 'شركة التقنية المتقدمة', 'محمد علي', '0551234567', '<EMAIL>', 'Prospect', True, 1);

INSERT INTO tbl_Suppliers (SupplierCode, CompanyName, ContactPerson, Phone, Email, IsActive)
VALUES 
('SP0001', 'مورد الإلكترونيات', 'سالم محمد', '0507654321', '<EMAIL>', True),
('SP0002', 'مورد المكتبيات', 'نورا أحمد', '0551234567', '<EMAIL>', True);

INSERT INTO tbl_Products (ProductCode, ProductName, CategoryID, UnitOfMeasure, CostPrice, SellingPrice, MinimumStock, ReorderPoint, IsActive, SupplierID, CreatedBy)
VALUES 
('PR0001', 'جهاز كمبيوتر محمول', 1, 'قطعة', 2000, 2500, 5, 3, True, 1, 1),
('PR0002', 'طابعة ليزر', 1, 'قطعة', 800, 1000, 3, 2, True, 1, 1),
('PR0003', 'ورق A4', 2, 'علبة', 25, 35, 50, 20, True, 2, 1),
('PR0004', 'مكتب خشبي', 3, 'قطعة', 1200, 1500, 2, 1, True, 2, 1);

-- إدخال مستويات المخزون للمنتجات التجريبية
INSERT INTO tbl_StockLevels (ProductID, CurrentStock, ReservedStock) VALUES
(1, 10, 2),
(2, 5, 1),
(3, 100, 10),
(4, 3, 0);

-- إدخال معاملة مخزون تجريبية
INSERT INTO tbl_InventoryTransactions (ProductID, TransactionType, Quantity, UnitCost, ReferenceType, TransactionDate, CreatedBy, Notes)
VALUES (1, 'IN', 10, 2000, 'INITIAL', Now(), 1, 'رصيد افتتاحي');

-- تسجيل إنشاء النظام في السجل
INSERT INTO tbl_SystemLog (LogType, Description, CreatedBy)
VALUES ('SYSTEM', 'تم إنشاء نظام إدارة المؤسسات بنجاح', 1);

-- ============================================================================
-- ملاحظات مهمة - IMPORTANT NOTES
-- ============================================================================

/*
بعد تنفيذ هذا السكريبت:

1. أنشئ العلاقات بين الجداول في نافذة Relationships:
   - tbl_Users.RoleID → tbl_Roles.RoleID
   - tbl_ClientContacts.ClientID → tbl_Clients.ClientID
   - tbl_Products.CategoryID → tbl_Categories.CategoryID
   - tbl_Products.SupplierID → tbl_Suppliers.SupplierID
   - tbl_StockLevels.ProductID → tbl_Products.ProductID
   - وباقي العلاقات المذكورة في الوثائق

2. بيانات تسجيل الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: 12345

3. تأكد من تفعيل Referential Integrity لجميع العلاقات

4. قم بإنشاء النماذج والتقارير حسب الوثائق المرفقة

5. اختبر النظام بالبيانات التجريبية المدخلة

6. قم بعمل نسخة احتياطية قبل البدء في الاستخدام الفعلي
*/
