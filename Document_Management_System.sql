-- نظام إدارة المستندات - Document Management System
-- إضافة جداول تحميل المستندات لجميع الإدارات
-- Adding document upload tables for all departments

-- ===================================================================
-- جدول أنواع المستندات - Document Types Table
-- ===================================================================
CREATE TABLE tbl_DocumentTypes (
    DocumentTypeID AUTOINCREMENT PRIMARY KEY,
    TypeName TEXT(100) NOT NULL,
    TypeNameEn TEXT(100),
    Description TEXT(255),
    AllowedExtensions TEXT(255) DEFAULT 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif,txt,zip,rar',
    MaxFileSize LONG DEFAULT 10485760, -- 10MB in bytes
    RequiresApproval YESNO DEFAULT No,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    ModifiedDate DATETIME,
    ModifiedBy LONG
);

-- إدخال أنواع المستندات الافتراضية
INSERT INTO tbl_DocumentTypes (TypeName, TypeNameEn, Description, AllowedExtensions, MaxFileSize) VALUES
('عقود العملاء', 'Client Contracts', 'عقود وإتفاقيات العملاء', 'pdf,doc,docx', 20971520),
('فواتير', 'Invoices', 'فواتير المبيعات والمشتريات', 'pdf,jpg,jpeg,png', 5242880),
('تقارير مالية', 'Financial Reports', 'التقارير المالية والمحاسبية', 'pdf,xls,xlsx', 15728640),
('مستندات الموظفين', 'Employee Documents', 'السير الذاتية والشهادات', 'pdf,doc,docx,jpg,jpeg', 10485760),
('شهادات المنتجات', 'Product Certificates', 'شهادات الجودة والمطابقة', 'pdf,jpg,jpeg,png', 10485760),
('مستندات الموردين', 'Supplier Documents', 'عقود ومستندات الموردين', 'pdf,doc,docx', 20971520),
('تقارير المخزون', 'Inventory Reports', 'تقارير الجرد والمخزون', 'pdf,xls,xlsx', 10485760),
('مستندات قانونية', 'Legal Documents', 'المستندات القانونية والتراخيص', 'pdf,doc,docx', 25165824),
('صور المنتجات', 'Product Images', 'صور وكتالوجات المنتجات', 'jpg,jpeg,png,gif,pdf', 5242880),
('مستندات أخرى', 'Other Documents', 'مستندات متنوعة', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png,txt,zip', 10485760);

-- ===================================================================
-- جدول المستندات الرئيسي - Main Documents Table
-- ===================================================================
CREATE TABLE tbl_Documents (
    DocumentID AUTOINCREMENT PRIMARY KEY,
    DocumentTypeID LONG NOT NULL,
    RelatedModule TEXT(50) NOT NULL, -- العملاء، المبيعات، المشتريات، إلخ
    RelatedRecordID LONG, -- معرف السجل المرتبط
    DocumentTitle TEXT(200) NOT NULL,
    DocumentDescription TEXT(500),
    OriginalFileName TEXT(255) NOT NULL,
    StoredFileName TEXT(255) NOT NULL,
    FilePath TEXT(500) NOT NULL,
    FileSize LONG NOT NULL,
    FileExtension TEXT(10) NOT NULL,
    MimeType TEXT(100),
    DocumentDate DATETIME DEFAULT Now(),
    ExpiryDate DATETIME,
    DocumentStatus TEXT(20) DEFAULT 'Active', -- Active, Archived, Deleted
    ApprovalStatus TEXT(20) DEFAULT 'Pending', -- Pending, Approved, Rejected
    ApprovedBy LONG,
    ApprovalDate DATETIME,
    ApprovalNotes TEXT(500),
    IsConfidential YESNO DEFAULT No,
    AccessLevel TEXT(20) DEFAULT 'Normal', -- Public, Normal, Restricted, Confidential
    Tags TEXT(500), -- كلمات مفتاحية للبحث
    UploadedBy LONG NOT NULL,
    UploadDate DATETIME DEFAULT Now(),
    DownloadCount LONG DEFAULT 0,
    LastAccessDate DATETIME,
    LastAccessBy LONG,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    ModifiedDate DATETIME,
    ModifiedBy LONG,
    
    CONSTRAINT FK_Documents_DocumentType FOREIGN KEY (DocumentTypeID) REFERENCES tbl_DocumentTypes(DocumentTypeID),
    CONSTRAINT FK_Documents_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES tbl_Users(UserID),
    CONSTRAINT FK_Documents_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول مشاركة المستندات - Document Sharing Table
-- ===================================================================
CREATE TABLE tbl_DocumentSharing (
    SharingID AUTOINCREMENT PRIMARY KEY,
    DocumentID LONG NOT NULL,
    SharedWithUserID LONG,
    SharedWithRole TEXT(50),
    SharedWithDepartment TEXT(50),
    PermissionType TEXT(20) DEFAULT 'Read', -- Read, Write, Delete, Full
    ShareDate DATETIME DEFAULT Now(),
    SharedBy LONG NOT NULL,
    ExpiryDate DATETIME,
    IsActive YESNO DEFAULT Yes,
    AccessCount LONG DEFAULT 0,
    LastAccessDate DATETIME,
    
    CONSTRAINT FK_DocumentSharing_Document FOREIGN KEY (DocumentID) REFERENCES tbl_Documents(DocumentID),
    CONSTRAINT FK_DocumentSharing_SharedWith FOREIGN KEY (SharedWithUserID) REFERENCES tbl_Users(UserID),
    CONSTRAINT FK_DocumentSharing_SharedBy FOREIGN KEY (SharedBy) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول إصدارات المستندات - Document Versions Table
-- ===================================================================
CREATE TABLE tbl_DocumentVersions (
    VersionID AUTOINCREMENT PRIMARY KEY,
    DocumentID LONG NOT NULL,
    VersionNumber TEXT(20) NOT NULL,
    VersionDescription TEXT(500),
    FileName TEXT(255) NOT NULL,
    FilePath TEXT(500) NOT NULL,
    FileSize LONG NOT NULL,
    UploadedBy LONG NOT NULL,
    UploadDate DATETIME DEFAULT Now(),
    IsCurrentVersion YESNO DEFAULT No,
    
    CONSTRAINT FK_DocumentVersions_Document FOREIGN KEY (DocumentID) REFERENCES tbl_Documents(DocumentID),
    CONSTRAINT FK_DocumentVersions_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول سجل الوصول للمستندات - Document Access Log Table
-- ===================================================================
CREATE TABLE tbl_DocumentAccessLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    DocumentID LONG NOT NULL,
    UserID LONG NOT NULL,
    AccessType TEXT(20) NOT NULL, -- View, Download, Edit, Delete, Share
    AccessDate DATETIME DEFAULT Now(),
    IPAddress TEXT(45),
    UserAgent TEXT(500),
    AccessResult TEXT(20) DEFAULT 'Success', -- Success, Failed, Denied
    Notes TEXT(500),
    
    CONSTRAINT FK_DocumentAccessLog_Document FOREIGN KEY (DocumentID) REFERENCES tbl_Documents(DocumentID),
    CONSTRAINT FK_DocumentAccessLog_User FOREIGN KEY (UserID) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول تعليقات المستندات - Document Comments Table
-- ===================================================================
CREATE TABLE tbl_DocumentComments (
    CommentID AUTOINCREMENT PRIMARY KEY,
    DocumentID LONG NOT NULL,
    UserID LONG NOT NULL,
    CommentText TEXT(1000) NOT NULL,
    CommentDate DATETIME DEFAULT Now(),
    ParentCommentID LONG, -- للردود على التعليقات
    IsActive YESNO DEFAULT Yes,
    
    CONSTRAINT FK_DocumentComments_Document FOREIGN KEY (DocumentID) REFERENCES tbl_Documents(DocumentID),
    CONSTRAINT FK_DocumentComments_User FOREIGN KEY (UserID) REFERENCES tbl_Users(UserID),
    CONSTRAINT FK_DocumentComments_Parent FOREIGN KEY (ParentCommentID) REFERENCES tbl_DocumentComments(CommentID)
);

-- ===================================================================
-- جدول مجلدات المستندات - Document Folders Table
-- ===================================================================
CREATE TABLE tbl_DocumentFolders (
    FolderID AUTOINCREMENT PRIMARY KEY,
    FolderName TEXT(100) NOT NULL,
    FolderDescription TEXT(255),
    ParentFolderID LONG, -- للمجلدات الفرعية
    RelatedModule TEXT(50), -- العملاء، المبيعات، إلخ
    FolderPath TEXT(500),
    AccessLevel TEXT(20) DEFAULT 'Normal',
    CreatedBy LONG NOT NULL,
    CreatedDate DATETIME DEFAULT Now(),
    ModifiedBy LONG,
    ModifiedDate DATETIME,
    IsActive YESNO DEFAULT Yes,
    
    CONSTRAINT FK_DocumentFolders_Parent FOREIGN KEY (ParentFolderID) REFERENCES tbl_DocumentFolders(FolderID),
    CONSTRAINT FK_DocumentFolders_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES tbl_Users(UserID)
);

-- إدخال المجلدات الافتراضية
INSERT INTO tbl_DocumentFolders (FolderName, FolderDescription, RelatedModule, FolderPath, CreatedBy) VALUES
('مستندات العملاء', 'جميع مستندات العملاء', 'Clients', '/Documents/Clients/', 1),
('مستندات المبيعات', 'عقود وفواتير المبيعات', 'Sales', '/Documents/Sales/', 1),
('مستندات المشتريات', 'أوامر الشراء والفواتير', 'Purchases', '/Documents/Purchases/', 1),
('مستندات المخزون', 'تقارير وشهادات المخزون', 'Inventory', '/Documents/Inventory/', 1),
('مستندات الموردين', 'عقود ومستندات الموردين', 'Suppliers', '/Documents/Suppliers/', 1),
('مستندات الموظفين', 'ملفات الموظفين الشخصية', 'Employees', '/Documents/Employees/', 1),
('مستندات الحسابات', 'التقارير المالية والمحاسبية', 'Accounts', '/Documents/Accounts/', 1),
('مستندات الفواتير', 'فواتير المبيعات والمشتريات', 'Invoices', '/Documents/Invoices/', 1),
('مستندات التقارير', 'التقارير الإدارية والتحليلية', 'Reports', '/Documents/Reports/', 1),
('مستندات النظام', 'مستندات إدارة النظام', 'System', '/Documents/System/', 1);

-- ===================================================================
-- جدول ربط المستندات بالمجلدات - Document Folder Mapping Table
-- ===================================================================
CREATE TABLE tbl_DocumentFolderMapping (
    MappingID AUTOINCREMENT PRIMARY KEY,
    DocumentID LONG NOT NULL,
    FolderID LONG NOT NULL,
    AddedDate DATETIME DEFAULT Now(),
    AddedBy LONG NOT NULL,
    
    CONSTRAINT FK_DocumentFolderMapping_Document FOREIGN KEY (DocumentID) REFERENCES tbl_Documents(DocumentID),
    CONSTRAINT FK_DocumentFolderMapping_Folder FOREIGN KEY (FolderID) REFERENCES tbl_DocumentFolders(FolderID),
    CONSTRAINT FK_DocumentFolderMapping_AddedBy FOREIGN KEY (AddedBy) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول قوالب المستندات - Document Templates Table
-- ===================================================================
CREATE TABLE tbl_DocumentTemplates (
    TemplateID AUTOINCREMENT PRIMARY KEY,
    TemplateName TEXT(100) NOT NULL,
    TemplateDescription TEXT(255),
    DocumentTypeID LONG NOT NULL,
    TemplateFile TEXT(255) NOT NULL,
    TemplateContent TEXT, -- للقوالب النصية
    IsActive YESNO DEFAULT Yes,
    CreatedBy LONG NOT NULL,
    CreatedDate DATETIME DEFAULT Now(),
    ModifiedBy LONG,
    ModifiedDate DATETIME,
    
    CONSTRAINT FK_DocumentTemplates_DocumentType FOREIGN KEY (DocumentTypeID) REFERENCES tbl_DocumentTypes(DocumentTypeID),
    CONSTRAINT FK_DocumentTemplates_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES tbl_Users(UserID)
);

-- ===================================================================
-- جدول إعدادات المستندات - Document Settings Table
-- ===================================================================
CREATE TABLE tbl_DocumentSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(100) NOT NULL,
    SettingValue TEXT(500),
    SettingDescription TEXT(255),
    SettingCategory TEXT(50) DEFAULT 'General',
    IsActive YESNO DEFAULT Yes,
    ModifiedBy LONG,
    ModifiedDate DATETIME DEFAULT Now()
);

-- إدخال الإعدادات الافتراضية
INSERT INTO tbl_DocumentSettings (SettingName, SettingValue, SettingDescription, SettingCategory) VALUES
('MaxFileSize', '10485760', 'الحد الأقصى لحجم الملف بالبايت (10MB)', 'Upload'),
('AllowedExtensions', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif,txt,zip,rar', 'امتدادات الملفات المسموحة', 'Upload'),
('DocumentStoragePath', 'C:\EnterpriseSystem\Documents\', 'مسار تخزين المستندات', 'Storage'),
('RequireApproval', 'No', 'هل يتطلب الموافقة على المستندات', 'Approval'),
('EnableVersioning', 'Yes', 'تفعيل نظام الإصدارات', 'Versioning'),
('AutoBackup', 'Yes', 'النسخ الاحتياطي التلقائي', 'Backup'),
('BackupInterval', '7', 'فترة النسخ الاحتياطي بالأيام', 'Backup'),
('EnableEncryption', 'Yes', 'تفعيل تشفير المستندات الحساسة', 'Security'),
('LogAccess', 'Yes', 'تسجيل عمليات الوصول للمستندات', 'Security'),
('DocumentRetention', '2555', 'فترة الاحتفاظ بالمستندات بالأيام (7 سنوات)', 'Retention');

-- ===================================================================
-- إنشاء الفهارس لتحسين الأداء - Create Indexes for Performance
-- ===================================================================

-- فهارس جدول المستندات
CREATE INDEX idx_Documents_RelatedModule ON tbl_Documents(RelatedModule);
CREATE INDEX idx_Documents_RelatedRecordID ON tbl_Documents(RelatedRecordID);
CREATE INDEX idx_Documents_DocumentTypeID ON tbl_Documents(DocumentTypeID);
CREATE INDEX idx_Documents_UploadedBy ON tbl_Documents(UploadedBy);
CREATE INDEX idx_Documents_UploadDate ON tbl_Documents(UploadDate);
CREATE INDEX idx_Documents_DocumentStatus ON tbl_Documents(DocumentStatus);
CREATE INDEX idx_Documents_ApprovalStatus ON tbl_Documents(ApprovalStatus);

-- فهارس جدول مشاركة المستندات
CREATE INDEX idx_DocumentSharing_DocumentID ON tbl_DocumentSharing(DocumentID);
CREATE INDEX idx_DocumentSharing_SharedWithUserID ON tbl_DocumentSharing(SharedWithUserID);
CREATE INDEX idx_DocumentSharing_SharedBy ON tbl_DocumentSharing(SharedBy);

-- فهارس جدول سجل الوصول
CREATE INDEX idx_DocumentAccessLog_DocumentID ON tbl_DocumentAccessLog(DocumentID);
CREATE INDEX idx_DocumentAccessLog_UserID ON tbl_DocumentAccessLog(UserID);
CREATE INDEX idx_DocumentAccessLog_AccessDate ON tbl_DocumentAccessLog(AccessDate);

-- فهارس جدول المجلدات
CREATE INDEX idx_DocumentFolders_RelatedModule ON tbl_DocumentFolders(RelatedModule);
CREATE INDEX idx_DocumentFolders_ParentFolderID ON tbl_DocumentFolders(ParentFolderID);

-- ===================================================================
-- إنشاء طرق العرض - Create Views
-- ===================================================================

-- عرض المستندات مع التفاصيل
CREATE VIEW vw_DocumentsWithDetails AS
SELECT 
    d.DocumentID,
    d.DocumentTitle,
    d.DocumentDescription,
    d.OriginalFileName,
    d.FileSize,
    d.FileExtension,
    d.DocumentDate,
    d.DocumentStatus,
    d.ApprovalStatus,
    dt.TypeName AS DocumentTypeName,
    u1.FirstName + ' ' + u1.LastName AS UploadedByName,
    u2.FirstName + ' ' + u2.LastName AS ApprovedByName,
    d.UploadDate,
    d.ApprovalDate,
    d.DownloadCount,
    d.RelatedModule,
    d.RelatedRecordID
FROM tbl_Documents d
LEFT JOIN tbl_DocumentTypes dt ON d.DocumentTypeID = dt.DocumentTypeID
LEFT JOIN tbl_Users u1 ON d.UploadedBy = u1.UserID
LEFT JOIN tbl_Users u2 ON d.ApprovedBy = u2.UserID
WHERE d.DocumentStatus = 'Active';

-- عرض إحصائيات المستندات
CREATE VIEW vw_DocumentStatistics AS
SELECT 
    RelatedModule,
    COUNT(*) AS TotalDocuments,
    SUM(FileSize) AS TotalSize,
    AVG(FileSize) AS AverageSize,
    COUNT(CASE WHEN ApprovalStatus = 'Approved' THEN 1 END) AS ApprovedDocuments,
    COUNT(CASE WHEN ApprovalStatus = 'Pending' THEN 1 END) AS PendingDocuments,
    COUNT(CASE WHEN IsConfidential = Yes THEN 1 END) AS ConfidentialDocuments
FROM tbl_Documents
WHERE DocumentStatus = 'Active'
GROUP BY RelatedModule;
