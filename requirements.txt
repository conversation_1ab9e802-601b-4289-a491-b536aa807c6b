# Enterprise Management System - Requirements
# Core GUI and Application Framework
customtkinter>=5.2.0
tkinter-tooltip>=2.0.0
Pillow>=10.0.0

# Database and Data Management
sqlite3  # Built-in with Python

# Data Validation and Processing
pydantic>=2.0.0
python-dateutil>=2.8.0

# Reporting and Visualization
matplotlib>=3.7.0
plotly>=5.15.0
seaborn>=0.12.0

# PDF and Document Generation
reportlab>=4.0.0
fpdf2>=2.7.0

# Excel Integration
openpyxl>=3.1.0
xlsxwriter>=3.1.0
pandas>=2.0.0

# Email Integration
secure-smtplib>=0.1.1

# Security and Encryption
cryptography>=41.0.0
bcrypt>=4.0.0

# Configuration and Logging
configparser  # Built-in with Python
python-dotenv>=1.0.0

# Development and Testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
isort>=5.12.0
pylint>=2.17.0
flake8>=6.0.0

# Additional Utilities
requests>=2.31.0
python-json-logger>=2.0.0
ttkbootstrap>=1.10.0
