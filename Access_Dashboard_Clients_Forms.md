# نماذج لوحة التحكم وإدارة العملاء - Dashboard & Client Management Forms

## نموذج لوحة التحكم (frm_Dashboard)

### خصائص النموذج:
```
Name: frm_Dashboard
Caption: لوحة التحكم - Dashboard
Modal: No
PopUp: No
AutoCenter: Yes
BorderStyle: Sizable
Width: 22 cm (12672 twips)
Height: 16 cm (9216 twips)
BackColor: RGB(248, 249, 250)
ScrollBars: Neither
RecordSelectors: No
NavigationButtons: No
```

### مؤشرات الأداء (KPIs) - الصف الأول:

```
1. Rectangle (إجمالي المبيعات):
   - Name: rect_TotalSales
   - Left: 1 cm, Top: 2 cm, Width: 5 cm, Height: 3 cm
   - BackColor: RGB(46, 134, 171)
   - BorderStyle: Solid
   - BorderColor: RGB(255, 255, 255)

2. Label (عنوان المبيعات):
   - Name: lbl_SalesTitle
   - Caption: إجمالي المبيعات
   - Left: 1.2 cm, Top: 2.2 cm, Width: 4.6 cm, Height: 0.6 cm
   - Font: Tahoma, 12pt, Bold, White
   - TextAlign: Center

3. Label (قيمة المبيعات):
   - Name: lbl_TotalSales
   - Caption: 0 ريال
   - Left: 1.2 cm, Top: 3 cm, Width: 4.6 cm, Height: 1 cm
   - Font: Tahoma, 18pt, Bold, White
   - TextAlign: Center

4. Label (نسبة التغيير):
   - Name: lbl_SalesChange
   - Caption: +0%
   - Left: 1.2 cm, Top: 4.2 cm, Width: 4.6 cm, Height: 0.6 cm
   - Font: Tahoma, 10pt, White
   - TextAlign: Center

5. Rectangle (عدد العملاء):
   - Name: rect_ClientCount
   - Left: 6.5 cm, Top: 2 cm, Width: 5 cm, Height: 3 cm
   - BackColor: RGB(76, 175, 80)

6. Label (عنوان العملاء):
   - Name: lbl_ClientTitle
   - Caption: إجمالي العملاء
   - Left: 6.7 cm, Top: 2.2 cm, Width: 4.6 cm, Height: 0.6 cm
   - Font: Tahoma, 12pt, Bold, White
   - TextAlign: Center

7. Label (عدد العملاء):
   - Name: lbl_ClientCount
   - Caption: 0 عميل
   - Left: 6.7 cm, Top: 3 cm, Width: 4.6 cm, Height: 1 cm
   - Font: Tahoma, 18pt, Bold, White
   - TextAlign: Center

8. Rectangle (قيمة المخزون):
   - Name: rect_InventoryValue
   - Left: 12 cm, Top: 2 cm, Width: 5 cm, Height: 3 cm
   - BackColor: RGB(255, 152, 0)

9. Label (عنوان المخزون):
   - Name: lbl_InventoryTitle
   - Caption: قيمة المخزون
   - Left: 12.2 cm, Top: 2.2 cm, Width: 4.6 cm, Height: 0.6 cm
   - Font: Tahoma, 12pt, Bold, White
   - TextAlign: Center

10. Label (قيمة المخزون):
    - Name: lbl_InventoryValue
    - Caption: 0 ريال
    - Left: 12.2 cm, Top: 3 cm, Width: 4.6 cm, Height: 1 cm
    - Font: Tahoma, 18pt, Bold, White
    - TextAlign: Center

11. Rectangle (المخزون المنخفض):
    - Name: rect_LowStock
    - Left: 17.5 cm, Top: 2 cm, Width: 4 cm, Height: 3 cm
    - BackColor: RGB(244, 67, 54)

12. Label (عنوان التحذير):
    - Name: lbl_LowStockTitle
    - Caption: مخزون منخفض
    - Left: 17.7 cm, Top: 2.2 cm, Width: 3.6 cm, Height: 0.6 cm
    - Font: Tahoma, 12pt, Bold, White
    - TextAlign: Center

13. Label (عدد الأصناف):
    - Name: lbl_LowStockCount
    - Caption: 0 صنف
    - Left: 17.7 cm, Top: 3 cm, Width: 3.6 cm, Height: 1 cm
    - Font: Tahoma, 18pt, Bold, White
    - TextAlign: Center
```

### منطقة الرسوم البيانية:

```
14. Rectangle (مخطط المبيعات):
    - Name: rect_SalesChart
    - Left: 1 cm, Top: 6 cm, Width: 10 cm, Height: 6 cm
    - BackColor: RGB(255, 255, 255)
    - BorderStyle: Solid
    - BorderColor: RGB(206, 212, 218)

15. Label (عنوان المخطط):
    - Name: lbl_SalesChartTitle
    - Caption: مبيعات آخر 6 أشهر
    - Left: 1.2 cm, Top: 6.2 cm, Width: 9.6 cm, Height: 0.6 cm
    - Font: Tahoma, 14pt, Bold
    - TextAlign: Center

16. Rectangle (مخطط العملاء):
    - Name: rect_ClientChart
    - Left: 11.5 cm, Top: 6 cm, Width: 10 cm, Height: 6 cm
    - BackColor: RGB(255, 255, 255)
    - BorderStyle: Solid
    - BorderColor: RGB(206, 212, 218)

17. Label (عنوان مخطط العملاء):
    - Name: lbl_ClientChartTitle
    - Caption: توزيع العملاء حسب النوع
    - Left: 11.7 cm, Top: 6.2 cm, Width: 9.6 cm, Height: 0.6 cm
    - Font: Tahoma, 14pt, Bold
    - TextAlign: Center
```

### أزرار التحكم:

```
18. Command Button (تحديث):
    - Name: btn_Refresh
    - Caption: 🔄 تحديث
    - Left: 1 cm, Top: 13 cm, Width: 3 cm, Height: 0.8 cm
    - Font: Tahoma, 11pt, Bold
    - BackColor: RGB(46, 134, 171)
    - ForeColor: White

19. Command Button (تقرير مفصل):
    - Name: btn_DetailedReport
    - Caption: 📊 تقرير مفصل
    - Left: 5 cm, Top: 13 cm, Width: 3 cm, Height: 0.8 cm
    - Font: Tahoma, 11pt, Bold
    - BackColor: RGB(76, 175, 80)
    - ForeColor: White

20. Command Button (إغلاق):
    - Name: btn_Close
    - Caption: ❌ إغلاق
    - Left: 18.5 cm, Top: 13 cm, Width: 3 cm, Height: 0.8 cm
    - Font: Tahoma, 11pt, Bold
    - BackColor: RGB(108, 117, 125)
    - ForeColor: White
```

### كود VBA للوحة التحكم:

```vba
Private Sub Form_Load()
    ' تحديث المؤشرات عند تحميل النموذج
    UpdateKPIs
    
    ' تشغيل التحديث التلقائي كل 5 دقائق
    Me.TimerInterval = 300000
End Sub

Private Sub Form_Timer()
    UpdateKPIs
End Sub

Private Sub UpdateKPIs()
    On Error GoTo ErrorHandler
    
    ' إجمالي المبيعات
    Me.lbl_TotalSales.Caption = FormatCurrency(GetTotalSalesThisMonth())
    
    ' عدد العملاء
    Me.lbl_ClientCount.Caption = GetActiveClientsCount() & " عميل"
    
    ' قيمة المخزون
    Me.lbl_InventoryValue.Caption = FormatCurrency(GetInventoryValue())
    
    ' المخزون المنخفض
    Dim LowStockCount As Long
    LowStockCount = GetLowStockCount()
    Me.lbl_LowStockCount.Caption = LowStockCount & " صنف"
    
    ' تغيير لون التحذير
    If LowStockCount > 0 Then
        Me.rect_LowStock.BackColor = RGB(244, 67, 54) ' أحمر
    Else
        Me.rect_LowStock.BackColor = RGB(76, 175, 80) ' أخضر
    End If
    
    ' تحديث عنوان النموذج
    Me.Caption = "لوحة التحكم - آخر تحديث: " & Format(Now(), "hh:nn:ss")
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في تحديث المؤشرات: " & Err.Description, vbExclamation
End Sub

Private Sub btn_Refresh_Click()
    UpdateKPIs
    MsgBox "تم تحديث البيانات بنجاح", vbInformation
End Sub

Private Sub btn_DetailedReport_Click()
    DoCmd.OpenReport "rpt_DashboardReport", acViewPreview
End Sub

Private Sub btn_Close_Click()
    DoCmd.Close acForm, Me.Name
End Sub

' النقر على المؤشرات لفتح التفاصيل
Private Sub lbl_TotalSales_Click()
    DoCmd.OpenReport "rpt_SalesReport", acViewPreview
End Sub

Private Sub lbl_LowStockCount_Click()
    DoCmd.OpenForm "frm_Inventory", acNormal, , "CurrentStock <= ReorderPoint"
End Sub
```

---

## نموذج إدارة العملاء (frm_Clients)

### خصائص النموذج:
```
Name: frm_Clients
Caption: إدارة العملاء - Client Management
RecordSource: tbl_Clients
DefaultView: Continuous Forms
AllowAdditions: Yes
AllowEdits: Yes
AllowDeletions: No
Width: 20 cm (11520 twips)
Height: 12 cm (6912 twips)
```

### قسم الرأس:

```
1. Rectangle (خلفية الرأس):
   - Left: 0, Top: 0, Width: 20 cm, Height: 1.5 cm
   - BackColor: RGB(46, 134, 171)

2. Label (عنوان النموذج):
   - Caption: إدارة العملاء
   - Left: 1 cm, Top: 0.3 cm, Width: 8 cm, Height: 0.8 cm
   - Font: Tahoma, 16pt, Bold, White

3. TextBox (البحث):
   - Name: txt_Search
   - Left: 10 cm, Top: 0.4 cm, Width: 4 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt

4. Label (تسمية البحث):
   - Caption: بحث:
   - Left: 9 cm, Top: 0.4 cm, Width: 1 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt, Bold, White

5. Command Button (بحث):
   - Name: btn_Search
   - Caption: 🔍
   - Left: 14.5 cm, Top: 0.4 cm, Width: 1 cm, Height: 0.6 cm

6. Command Button (إظهار الكل):
   - Name: btn_ShowAll
   - Caption: الكل
   - Left: 16 cm, Top: 0.4 cm, Width: 1.5 cm, Height: 0.6 cm
```

### أزرار التحكم:

```
7. Command Button (جديد):
   - Name: btn_New
   - Caption: ➕ جديد
   - Left: 1 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
   - BackColor: RGB(76, 175, 80)
   - ForeColor: White

8. Command Button (تعديل):
   - Name: btn_Edit
   - Caption: ✏️ تعديل
   - Left: 4 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
   - BackColor: RGB(255, 193, 7)

9. Command Button (حذف):
   - Name: btn_Delete
   - Caption: 🗑️ حذف
   - Left: 7 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
   - BackColor: RGB(220, 53, 69)
   - ForeColor: White

10. Command Button (طباعة):
    - Name: btn_Print
    - Caption: 🖨️ طباعة
    - Left: 10 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
    - BackColor: RGB(108, 117, 125)
    - ForeColor: White

11. Command Button (تصدير):
    - Name: btn_Export
    - Caption: 📤 تصدير
    - Left: 13 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
    - BackColor: RGB(23, 162, 184)
    - ForeColor: White

12. Command Button (إغلاق):
    - Name: btn_Close
    - Caption: ❌ إغلاق
    - Left: 16 cm, Top: 1.8 cm, Width: 2.5 cm, Height: 0.8 cm
    - BackColor: RGB(108, 117, 125)
    - ForeColor: White
```

### منطقة البيانات (Detail Section):

```
13. TextBox (رمز العميل):
    - Name: ClientCode
    - ControlSource: ClientCode
    - Left: 0.5 cm, Top: 0.2 cm, Width: 2 cm, Height: 0.6 cm
    - Locked: Yes
    - BackColor: RGB(248, 249, 250)

14. TextBox (اسم الشركة):
    - Name: CompanyName
    - ControlSource: CompanyName
    - Left: 3 cm, Top: 0.2 cm, Width: 4 cm, Height: 0.6 cm

15. TextBox (جهة الاتصال):
    - Name: ContactPerson
    - ControlSource: ContactPerson
    - Left: 7.5 cm, Top: 0.2 cm, Width: 3 cm, Height: 0.6 cm

16. TextBox (الهاتف):
    - Name: Phone
    - ControlSource: Phone
    - Left: 11 cm, Top: 0.2 cm, Width: 2.5 cm, Height: 0.6 cm

17. TextBox (البريد الإلكتروني):
    - Name: Email
    - ControlSource: Email
    - Left: 14 cm, Top: 0.2 cm, Width: 3.5 cm, Height: 0.6 cm

18. CheckBox (نشط):
    - Name: IsActive
    - ControlSource: IsActive
    - Left: 18 cm, Top: 0.2 cm, Width: 1 cm, Height: 0.6 cm
```

### عناوين الأعمدة (Header Section):

```
19. Label (رمز العميل):
    - Caption: رمز العميل
    - Left: 0.5 cm, Top: 0.1 cm, Width: 2 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold

20. Label (اسم الشركة):
    - Caption: اسم الشركة
    - Left: 3 cm, Top: 0.1 cm, Width: 4 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold

21. Label (جهة الاتصال):
    - Caption: جهة الاتصال
    - Left: 7.5 cm, Top: 0.1 cm, Width: 3 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold

22. Label (الهاتف):
    - Caption: الهاتف
    - Left: 11 cm, Top: 0.1 cm, Width: 2.5 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold

23. Label (البريد الإلكتروني):
    - Caption: البريد الإلكتروني
    - Left: 14 cm, Top: 0.1 cm, Width: 3.5 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold

24. Label (نشط):
    - Caption: نشط
    - Left: 18 cm, Top: 0.1 cm, Width: 1 cm, Height: 0.5 cm
    - Font: Tahoma, 10pt, Bold
```

### كود VBA لنموذج العملاء:

```vba
Private Sub Form_Load()
    ' تحديث عداد العملاء في العنوان
    UpdateRecordCount
End Sub

Private Sub UpdateRecordCount()
    Dim RecordCount As Long
    RecordCount = DCount("*", "tbl_Clients", "IsActive = True")
    Me.Caption = "إدارة العملاء - العدد: " & RecordCount
End Sub

Private Sub btn_New_Click()
    DoCmd.GoToRecord , , acNewRec
    Me.ClientCode = GenerateClientCode()
    Me.CreatedDate = Now()
    Me.CreatedBy = g_CurrentUserID
    Me.IsActive = True
    Me.CompanyName.SetFocus
End Sub

Private Sub btn_Edit_Click()
    If Not IsNull(Me.ClientID) Then
        Me.AllowEdits = True
        Me.CompanyName.SetFocus
    Else
        MsgBox "يرجى اختيار عميل للتعديل", vbExclamation
    End If
End Sub

Private Sub btn_Delete_Click()
    If Not IsNull(Me.ClientID) Then
        If MsgBox("هل تريد إلغاء تفعيل هذا العميل؟", vbYesNo + vbQuestion) = vbYes Then
            Me.IsActive = False
            DoCmd.RunCommand acCmdSaveRecord
            UpdateRecordCount
            MsgBox "تم إلغاء تفعيل العميل بنجاح", vbInformation
        End If
    Else
        MsgBox "يرجى اختيار عميل", vbExclamation
    End If
End Sub

Private Sub btn_Search_Click()
    Dim SearchTerm As String
    SearchTerm = Nz(Me.txt_Search, "")
    
    If SearchTerm <> "" Then
        Me.Filter = "CompanyName Like '*" & SearchTerm & "*' OR ClientCode Like '*" & SearchTerm & "*' OR ContactPerson Like '*" & SearchTerm & "*'"
        Me.FilterOn = True
    End If
End Sub

Private Sub btn_ShowAll_Click()
    Me.FilterOn = False
    Me.txt_Search = ""
End Sub

Private Sub btn_Print_Click()
    DoCmd.OpenReport "rpt_ClientList", acViewPreview
End Sub

Private Sub btn_Export_Click()
    Dim ExportPath As String
    ExportPath = CurrentProject.Path & "\قائمة_العملاء_" & Format(Now(), "yyyymmdd") & ".xlsx"
    
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, "tbl_Clients", ExportPath, True
    
    MsgBox "تم التصدير بنجاح إلى:" & vbCrLf & ExportPath, vbInformation
End Sub

Private Sub btn_Close_Click()
    DoCmd.Close acForm, Me.Name
End Sub

Private Sub Form_BeforeUpdate(Cancel As Integer)
    ' التحقق من صحة البيانات قبل الحفظ
    Dim ValidationError As String
    ValidationError = ValidateClientData(Nz(Me.CompanyName, ""), Nz(Me.ClientCode, ""), Nz(Me.Email, ""))
    
    If ValidationError <> "" Then
        MsgBox "يرجى تصحيح الأخطاء التالية:" & vbCrLf & ValidationError, vbExclamation
        Cancel = True
    End If
End Sub

Private Sub Form_AfterUpdate()
    UpdateRecordCount
    LogActivity "CLIENT_UPDATE", "تم تحديث بيانات العميل: " & Me.CompanyName
End Sub

Private Sub txt_Search_KeyPress(KeyAscii As Integer)
    If KeyAscii = 13 Then ' Enter key
        btn_Search_Click
    End If
End Sub
```

هذا التصميم يوفر واجهة شاملة وسهلة الاستخدام لإدارة العملاء مع جميع الوظائف المطلوبة.
