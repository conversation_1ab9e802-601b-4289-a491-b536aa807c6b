' Enterprise Management System - Dashboard Module
' VBA Module for dashboard functionality and KPI calculations
' Module Name: Module_Dashboard

Option Compare Database
Option Explicit

' Dashboard KPI Structure
Public Type DashboardKPI
    Title As String
    Value As Variant
    PreviousValue As Variant
    PercentageChange As Double
    TrendDirection As String ' UP, DOWN, STABLE
    Color As Long
    Icon As String
End Type

' ============================================================================
' DASHBOARD DATA LOADING FUNCTIONS
' ============================================================================

Public Function LoadDashboardData() As Collection
    ' Load all dashboard KPIs and return as collection
    On Error GoTo ErrorHandler
    
    Dim KPIs As New Collection
    Dim KPI As DashboardKPI
    
    ' Sales KPIs
    KPI = GetSalesKPIs()
    KPIs.Add KPI, "Sales"
    
    KPI = GetOrdersKPIs()
    KPIs.Add KPI, "Orders"
    
    ' Financial KPIs
    KPI = GetRevenueKPIs()
    KPIs.Add KPI, "Revenue"
    
    KPI = GetProfitKPIs()
    KPIs.Add KPI, "Profit"
    
    ' Inventory KPIs
    KPI = GetInventoryKPIs()
    KPIs.Add KPI, "Inventory"
    
    KPI = GetLowStockKPIs()
    KPIs.Add KPI, "LowStock"
    
    ' Customer KPIs
    KPI = GetCustomerKPIs()
    KPIs.Add KPI, "Customers"
    
    KPI = GetNewCustomersKPIs()
    KPIs.Add KPI, "NewCustomers"
    
    Set LoadDashboardData = KPIs
    Exit Function
    
ErrorHandler:
    MsgBox "Error loading dashboard data: " & Err.Description, vbCritical, "Dashboard Error"
    Set LoadDashboardData = New Collection
End Function

' ============================================================================
' SALES KPIs
' ============================================================================

Public Function GetSalesKPIs() As DashboardKPI
    ' Get sales KPIs for current month vs previous month
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim CurrentMonth As Double
    Dim PreviousMonth As Double
    
    ' Current month sales
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(TotalAmount), 0) AS TotalSales " & _
        "FROM tbl_SalesOrders " & _
        "WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date()) " & _
        "AND Status <> 'Cancelled'")
    
    CurrentMonth = Nz(rs!TotalSales, 0)
    rs.Close
    
    ' Previous month sales
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(TotalAmount), 0) AS TotalSales " & _
        "FROM tbl_SalesOrders " & _
        "WHERE MONTH(OrderDate) = MONTH(DateAdd('m', -1, Date())) " & _
        "AND YEAR(OrderDate) = YEAR(DateAdd('m', -1, Date())) " & _
        "AND Status <> 'Cancelled'")
    
    PreviousMonth = Nz(rs!TotalSales, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "إجمالي المبيعات - Total Sales"
        .Value = Format(CurrentMonth, "Currency")
        .PreviousValue = Format(PreviousMonth, "Currency")
        
        If PreviousMonth > 0 Then
            .PercentageChange = ((CurrentMonth - PreviousMonth) / PreviousMonth) * 100
        Else
            .PercentageChange = 0
        End If
        
        If .PercentageChange > 0 Then
            .TrendDirection = "UP"
            .Color = RGB(76, 175, 80) ' Green
            .Icon = "↗"
        ElseIf .PercentageChange < 0 Then
            .TrendDirection = "DOWN"
            .Color = RGB(244, 67, 54) ' Red
            .Icon = "↘"
        Else
            .TrendDirection = "STABLE"
            .Color = RGB(158, 158, 158) ' Gray
            .Icon = "→"
        End If
    End With
    
    GetSalesKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "إجمالي المبيعات - Total Sales"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetSalesKPIs = KPI
End Function

Public Function GetOrdersKPIs() As DashboardKPI
    ' Get orders count KPIs
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim CurrentMonth As Long
    Dim PreviousMonth As Long
    
    ' Current month orders
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT COUNT(*) AS OrderCount " & _
        "FROM tbl_SalesOrders " & _
        "WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date())")
    
    CurrentMonth = Nz(rs!OrderCount, 0)
    rs.Close
    
    ' Previous month orders
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT COUNT(*) AS OrderCount " & _
        "FROM tbl_SalesOrders " & _
        "WHERE MONTH(OrderDate) = MONTH(DateAdd('m', -1, Date())) " & _
        "AND YEAR(OrderDate) = YEAR(DateAdd('m', -1, Date()))")
    
    PreviousMonth = Nz(rs!OrderCount, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "عدد الطلبات - Orders Count"
        .Value = CurrentMonth
        .PreviousValue = PreviousMonth
        
        If PreviousMonth > 0 Then
            .PercentageChange = ((CurrentMonth - PreviousMonth) / PreviousMonth) * 100
        Else
            .PercentageChange = 0
        End If
        
        If .PercentageChange > 0 Then
            .TrendDirection = "UP"
            .Color = RGB(76, 175, 80)
            .Icon = "↗"
        ElseIf .PercentageChange < 0 Then
            .TrendDirection = "DOWN"
            .Color = RGB(244, 67, 54)
            .Icon = "↘"
        Else
            .TrendDirection = "STABLE"
            .Color = RGB(158, 158, 158)
            .Icon = "→"
        End If
    End With
    
    GetOrdersKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "عدد الطلبات - Orders Count"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetOrdersKPIs = KPI
End Function

' ============================================================================
' FINANCIAL KPIs
' ============================================================================

Public Function GetRevenueKPIs() As DashboardKPI
    ' Get revenue KPIs (paid invoices)
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim CurrentMonth As Double
    Dim PreviousMonth As Double
    
    ' Current month revenue (paid invoices)
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(PaidAmount), 0) AS Revenue " & _
        "FROM tbl_Invoices " & _
        "WHERE MONTH(InvoiceDate) = MONTH(Date()) AND YEAR(InvoiceDate) = YEAR(Date())")
    
    CurrentMonth = Nz(rs!Revenue, 0)
    rs.Close
    
    ' Previous month revenue
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(PaidAmount), 0) AS Revenue " & _
        "FROM tbl_Invoices " & _
        "WHERE MONTH(InvoiceDate) = MONTH(DateAdd('m', -1, Date())) " & _
        "AND YEAR(InvoiceDate) = YEAR(DateAdd('m', -1, Date()))")
    
    PreviousMonth = Nz(rs!Revenue, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "الإيرادات المحصلة - Collected Revenue"
        .Value = Format(CurrentMonth, "Currency")
        .PreviousValue = Format(PreviousMonth, "Currency")
        
        If PreviousMonth > 0 Then
            .PercentageChange = ((CurrentMonth - PreviousMonth) / PreviousMonth) * 100
        Else
            .PercentageChange = 0
        End If
        
        If .PercentageChange > 0 Then
            .TrendDirection = "UP"
            .Color = RGB(76, 175, 80)
            .Icon = "↗"
        ElseIf .PercentageChange < 0 Then
            .TrendDirection = "DOWN"
            .Color = RGB(244, 67, 54)
            .Icon = "↘"
        Else
            .TrendDirection = "STABLE"
            .Color = RGB(158, 158, 158)
            .Icon = "→"
        End If
    End With
    
    GetRevenueKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "الإيرادات المحصلة - Collected Revenue"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetRevenueKPIs = KPI
End Function

Public Function GetProfitKPIs() As DashboardKPI
    ' Get profit KPIs (simplified calculation)
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim Revenue As Double
    Dim COGS As Double ' Cost of Goods Sold
    Dim Profit As Double
    
    ' Current month revenue
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(PaidAmount), 0) AS Revenue " & _
        "FROM tbl_Invoices " & _
        "WHERE MONTH(InvoiceDate) = MONTH(Date()) AND YEAR(InvoiceDate) = YEAR(Date())")
    
    Revenue = Nz(rs!Revenue, 0)
    rs.Close
    
    ' Estimate COGS (70% of revenue - simplified)
    COGS = Revenue * 0.7
    Profit = Revenue - COGS
    
    ' Build KPI
    With KPI
        .Title = "الربح المقدر - Estimated Profit"
        .Value = Format(Profit, "Currency")
        .PreviousValue = Format(0, "Currency") ' Simplified
        .PercentageChange = 0
        .TrendDirection = "STABLE"
        
        If Profit > 0 Then
            .Color = RGB(76, 175, 80)
            .Icon = "↗"
        ElseIf Profit < 0 Then
            .Color = RGB(244, 67, 54)
            .Icon = "↘"
        Else
            .Color = RGB(158, 158, 158)
            .Icon = "→"
        End If
    End With
    
    GetProfitKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "الربح المقدر - Estimated Profit"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetProfitKPIs = KPI
End Function

' ============================================================================
' INVENTORY KPIs
' ============================================================================

Public Function GetInventoryKPIs() As DashboardKPI
    ' Get inventory value KPIs
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim InventoryValue As Double
    
    ' Total inventory value
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT ISNULL(SUM(s.CurrentStock * p.CostPrice), 0) AS InventoryValue " & _
        "FROM tbl_StockLevels s INNER JOIN tbl_Products p ON s.ProductID = p.ProductID " & _
        "WHERE p.IsActive = True")
    
    InventoryValue = Nz(rs!InventoryValue, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "قيمة المخزون - Inventory Value"
        .Value = Format(InventoryValue, "Currency")
        .PreviousValue = Format(0, "Currency")
        .PercentageChange = 0
        .TrendDirection = "STABLE"
        .Color = RGB(121, 85, 72) ' Brown
        .Icon = "📦"
    End With
    
    GetInventoryKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "قيمة المخزون - Inventory Value"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetInventoryKPIs = KPI
End Function

Public Function GetLowStockKPIs() As DashboardKPI
    ' Get low stock items count
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim LowStockCount As Long
    
    ' Count of low stock items
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT COUNT(*) AS LowStockCount " & _
        "FROM tbl_Products p INNER JOIN tbl_StockLevels s ON p.ProductID = s.ProductID " & _
        "WHERE p.IsActive = True AND s.CurrentStock <= p.ReorderPoint")
    
    LowStockCount = Nz(rs!LowStockCount, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "مخزون منخفض - Low Stock Items"
        .Value = LowStockCount
        .PreviousValue = 0
        .PercentageChange = 0
        .TrendDirection = "STABLE"
        
        If LowStockCount > 10 Then
            .Color = RGB(244, 67, 54) ' Red - Critical
        ElseIf LowStockCount > 5 Then
            .Color = RGB(255, 152, 0) ' Orange - Warning
        Else
            .Color = RGB(76, 175, 80) ' Green - Good
        End If
        
        .Icon = "⚠"
    End With
    
    GetLowStockKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "مخزون منخفض - Low Stock Items"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetLowStockKPIs = KPI
End Function

' ============================================================================
' CUSTOMER KPIs
' ============================================================================

Public Function GetCustomerKPIs() As DashboardKPI
    ' Get total active customers
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim CustomerCount As Long
    
    ' Total active customers
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT COUNT(*) AS CustomerCount " & _
        "FROM tbl_Clients " & _
        "WHERE IsActive = True")
    
    CustomerCount = Nz(rs!CustomerCount, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "إجمالي العملاء - Total Customers"
        .Value = CustomerCount
        .PreviousValue = 0
        .PercentageChange = 0
        .TrendDirection = "STABLE"
        .Color = RGB(76, 175, 80)
        .Icon = "👥"
    End With
    
    GetCustomerKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "إجمالي العملاء - Total Customers"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetCustomerKPIs = KPI
End Function

Public Function GetNewCustomersKPIs() As DashboardKPI
    ' Get new customers this month
    On Error GoTo ErrorHandler
    
    Dim KPI As DashboardKPI
    Dim rs As DAO.Recordset
    Dim NewCustomers As Long
    
    ' New customers this month
    Set rs = CurrentDb.OpenRecordset( _
        "SELECT COUNT(*) AS NewCustomers " & _
        "FROM tbl_Clients " & _
        "WHERE MONTH(CreatedDate) = MONTH(Date()) AND YEAR(CreatedDate) = YEAR(Date())")
    
    NewCustomers = Nz(rs!NewCustomers, 0)
    rs.Close
    
    ' Build KPI
    With KPI
        .Title = "عملاء جدد - New Customers"
        .Value = NewCustomers
        .PreviousValue = 0
        .PercentageChange = 0
        .TrendDirection = "UP"
        .Color = RGB(76, 175, 80)
        .Icon = "👤"
    End With
    
    GetNewCustomersKPIs = KPI
    Exit Function
    
ErrorHandler:
    With KPI
        .Title = "عملاء جدد - New Customers"
        .Value = "Error"
        .TrendDirection = "STABLE"
        .Color = RGB(158, 158, 158)
    End With
    GetNewCustomersKPIs = KPI
End Function

' ============================================================================
' CHART DATA FUNCTIONS
' ============================================================================

Public Function GetSalesChartData() As DAO.Recordset
    ' Get sales data for chart (last 12 months)
    On Error GoTo ErrorHandler
    
    Dim SQL As String
    
    SQL = "SELECT " & _
          "FORMAT(OrderDate, 'yyyy-mm') AS MonthYear, " & _
          "SUM(TotalAmount) AS TotalSales, " & _
          "COUNT(*) AS OrderCount " & _
          "FROM tbl_SalesOrders " & _
          "WHERE OrderDate >= DateAdd('m', -12, Date()) " & _
          "AND Status <> 'Cancelled' " & _
          "GROUP BY FORMAT(OrderDate, 'yyyy-mm') " & _
          "ORDER BY FORMAT(OrderDate, 'yyyy-mm')"
    
    Set GetSalesChartData = CurrentDb.OpenRecordset(SQL)
    Exit Function
    
ErrorHandler:
    Set GetSalesChartData = Nothing
End Function

Public Function GetTopProductsData() As DAO.Recordset
    ' Get top selling products data
    On Error GoTo ErrorHandler
    
    Dim SQL As String
    
    SQL = "SELECT TOP 10 " & _
          "p.ProductName, " & _
          "SUM(soi.Quantity) AS TotalQuantity, " & _
          "SUM(soi.LineTotal) AS TotalSales " & _
          "FROM tbl_Products p " & _
          "INNER JOIN tbl_SalesOrderItems soi ON p.ProductID = soi.ProductID " & _
          "INNER JOIN tbl_SalesOrders so ON soi.OrderID = so.OrderID " & _
          "WHERE so.OrderDate >= DateAdd('m', -3, Date()) " & _
          "AND so.Status <> 'Cancelled' " & _
          "GROUP BY p.ProductName " & _
          "ORDER BY SUM(soi.LineTotal) DESC"
    
    Set GetTopProductsData = CurrentDb.OpenRecordset(SQL)
    Exit Function
    
ErrorHandler:
    Set GetTopProductsData = Nothing
End Function
