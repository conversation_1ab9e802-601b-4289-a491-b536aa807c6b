# نظام إدارة المؤسسات الشامل 🏢
## Enterprise Management System - Microsoft Access

### 📋 نظرة عامة

نظام إدارة شامل ومتكامل مصمم خصيصاً للمؤسسات الصغيرة والمتوسطة باستخدام Microsoft Access. يوفر النظام واجهة مستخدم احترافية مع دعم كامل للغة العربية وأيقونات جذابة لجميع الوحدات.

**A comprehensive and integrated management system designed specifically for small to medium enterprises using Microsoft Access. The system provides a professional user interface with full Arabic language support and attractive icons for all modules.**

---

### ✨ المميزات الرئيسية

- 🔐 **نظام أمان متقدم** مع 7 أدوار مختلفة للمستخدمين
- 🎨 **واجهة احترافية** مع أيقونات ملونة وتصميم عصري
- 📊 **لوحة تحكم تفاعلية** مع مؤشرات الأداء الرئيسية
- 🌐 **دعم كامل للغة العربية** مع إمكانية التبديل للإنجليزية
- 📈 **تقارير شاملة** قابلة للطباعة والتصدير
- 💾 **نسخ احتياطي تلقائي** ونظام سجلات شامل
- 🔄 **تحديث تلقائي** للبيانات والمؤشرات
- 📱 **تصميم متجاوب** يتكيف مع أحجام الشاشات

---

### 🏗️ الوحدات المتاحة

| الوحدة | الحالة | الوصف |
|---------|--------|--------|
| 🏠 **لوحة التحكم** | ✅ مكتمل | مؤشرات الأداء والإحصائيات الرئيسية |
| 👥 **إدارة العملاء** | ✅ مكتمل | إدارة بيانات العملاء وجهات الاتصال |
| 💰 **إدارة المبيعات** | 🔄 جاهز | أوامر البيع وعروض الأسعار |
| 🛒 **إدارة المشتريات** | 🔄 جاهز | أوامر الشراء وإدارة الموردين |
| 📦 **إدارة المخازن** | 🔄 جاهز | تتبع المخزون وحركة البضائع |
| 🏭 **إدارة الموردين** | 🔄 جاهز | قاعدة بيانات الموردين والتقييمات |
| 👤 **إدارة الموظفين** | 🔄 جاهز | سجلات الموظفين والأقسام |
| 💳 **إدارة الحسابات** | 🔄 جاهز | الحسابات والمعاملات المالية |
| 📄 **إدارة الفواتير** | 🔄 جاهز | إنشاء وتتبع الفواتير والمدفوعات |
| 📊 **إدارة التقارير** | ✅ مكتمل | تقارير شاملة وتحليلات متقدمة |
| 🔐 **إدارة المستخدمين** | ✅ مكتمل | إدارة المستخدمين والصلاحيات |
| 🗄️ **إدارة قاعدة البيانات** | 🔄 جاهز | نسخ احتياطي وصيانة النظام |

---

### 💻 متطلبات النظام

#### الحد الأدنى:
- **نظام التشغيل:** Windows 10/11
- **Microsoft Access:** 2016 أو أحدث
- **الذاكرة:** 4 جيجابايت RAM
- **التخزين:** 2 جيجابايت مساحة فارغة
- **الشبكة:** اختيارية (للاستخدام متعدد المستخدمين)

#### المُوصى به:
- **المعالج:** Intel Core i5 أو أعلى
- **الذاكرة:** 8 جيجابايت RAM أو أكثر
- **التخزين:** 10 جيجابايت مساحة فارغة
- **الشبكة:** Gigabit Ethernet (للاستخدام متعدد المستخدمين)

---

### 📁 محتويات المشروع

```
Enterprise_Management_System/
├── 📄 README.md (هذا الملف)
├── 📄 تعليمات_التنفيذ_النهائية.md
├── 📄 Complete_Database_Script.sql
├── 📄 Access_Complete_VBA_Code.bas
├── 📄 Access_Forms_Complete_Design.md
├── 📄 Access_Dashboard_Clients_Forms.md
├── 📄 Enterprise_Management_System_Specifications.md
├── 📄 Implementation_Guide.md
├── 📄 SYSTEM_OVERVIEW.md
└── 📁 Access_Database_Creation/
    ├── Step1_CreateDatabase.md
    ├── Step2_CreateForms.md
    ├── Step3_CreateReports.md
    └── Step4_FinalSetup.md
```

---

### 🚀 التنفيذ السريع (5 دقائق)

#### 1. إنشاء قاعدة البيانات
```
• افتح Microsoft Access
• اختر "Blank Database"
• اسم الملف: "نظام_إدارة_المؤسسات.accdb"
• احفظ في المجلد المناسب
```

#### 2. تنفيذ السكريبت
```
• احذف الجدول الافتراضي
• Create > Query Design > SQL View
• انسخ محتوى "Complete_Database_Script.sql"
• اضغط F5 لتنفيذ السكريبت
```

#### 3. إنشاء العلاقات
```
• Database Tools > Relationships
• أضف جميع الجداول
• أنشئ العلاقات حسب التوثيق
• فعل Referential Integrity
```

#### 4. إضافة الكود البرمجي
```
• Create > Module
• انسخ محتوى "Access_Complete_VBA_Code.bas"
• احفظ باسم "Module_Main"
```

#### 5. إنشاء النماذج
```
• اتبع التعليمات في "Access_Forms_Complete_Design.md"
• أنشئ النماذج الأساسية:
  - frm_Login
  - frm_MainNavigation
  - frm_Dashboard
  - frm_Clients
```

#### 6. إعداد النظام
```
• File > Options > Current Database
• Display Form: "frm_Login"
• Application Title: "نظام إدارة المؤسسات"
• أعد تشغيل قاعدة البيانات
```

---

### 🔑 بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: 12345
```

**⚠️ تأكد من تغيير كلمة المرور بعد أول تسجيل دخول!**

---

### 📊 قاعدة البيانات

#### الجداول الأساسية (24 جدول):
- **إدارة المستخدمين:** tbl_Users, tbl_Roles
- **إدارة العملاء:** tbl_Clients, tbl_ClientContacts
- **إدارة المنتجات:** tbl_Products, tbl_Categories, tbl_StockLevels
- **إدارة المبيعات:** tbl_SalesOrders, tbl_SalesOrderItems, tbl_Quotations
- **إدارة المشتريات:** tbl_PurchaseOrders, tbl_PurchaseOrderItems
- **إدارة المالية:** tbl_Invoices, tbl_InvoiceItems, tbl_Payments, tbl_Accounts
- **النظام:** tbl_SystemSettings, tbl_SystemLog

#### العلاقات:
- **One-to-Many:** 15 علاقة أساسية
- **Many-to-Many:** 3 علاقات عبر جداول وسطية
- **Self-Referencing:** 2 علاقة للتصنيفات الهرمية

---

### 🎯 الاستخدام

#### للمديرين:
- 📈 مراقبة الأداء عبر لوحة التحكم
- 📊 عرض التقارير التنفيذية
- 👥 إدارة المستخدمين والصلاحيات
- 💼 اتخاذ القرارات بناءً على البيانات

#### لموظفي المبيعات:
- 👥 إدارة العملاء وجهات الاتصال
- 💰 إنشاء عروض الأسعار والطلبات
- 📄 إصدار الفواتير وتتبع المدفوعات
- 📊 عرض تقارير المبيعات

#### لأمناء المخازن:
- 📦 تتبع مستويات المخزون
- 🔄 تسجيل حركات الاستلام والصرف
- ⚠️ تنبيهات المخزون المنخفض
- 📋 تقارير الجرد والتقييم

---

### 🔧 الصيانة والدعم

#### النسخ الاحتياطي:
```
• تلقائي: كل 7 أيام (قابل للتخصيص)
• يدوي: من خلال وحدة إدارة قاعدة البيانات
• مسار الحفظ: مجلد Backups في نفس مجلد قاعدة البيانات
```

#### السجلات:
```
• تسجيل جميع العمليات في tbl_SystemLog
• تتبع تسجيل الدخول والخروج
• تسجيل التعديلات على البيانات الحساسة
• تسجيل الأخطاء والتحذيرات
```

#### الأداء:
```
• فهرسة تلقائية للحقول الأساسية
• ضغط قاعدة البيانات شهرياً
• تنظيف السجلات القديمة ربع سنوياً
• مراقبة حجم قاعدة البيانات
```

---

### 🆕 التحديثات المستقبلية

#### الإصدار 1.1 (مخطط):
- ✨ تحسين واجهة المستخدم
- 📊 إضافة المزيد من التقارير
- 🔄 تحسين الأداء والاستجابة
- 🌐 دعم أفضل للغة العربية

#### الإصدار 1.2 (مخطط):
- 📱 واجهة متجاوبة محسنة
- 🔗 تكامل مع البريد الإلكتروني
- 📈 تحليلات متقدمة ومؤشرات ذكية
- 🔐 تحسينات أمنية إضافية

#### الإصدار 2.0 (مستقبلي):
- 🌐 واجهة ويب كاملة
- 📱 تطبيق موبايل مصاحب
- 🤖 ذكاء اصطناعي للتحليلات
- ☁️ دعم التخزين السحابي

---

### 📞 الدعم الفني

```
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: +966-XX-XXXXXXX
🕐 ساعات العمل: 8:00 ص - 5:00 م (السبت - الخميس)
🌐 الموقع الإلكتروني: www.enterprise-system.com
```

#### الموارد المفيدة:
- 📖 دليل المستخدم الكامل
- 🎥 فيديوهات تعليمية
- ❓ الأسئلة الشائعة
- 💬 منتدى المجتمع

---

### 📄 الترخيص

هذا المشروع مُرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

### 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام والمجتمع الذي قدم الدعم والاقتراحات القيمة.

---

### 📈 إحصائيات المشروع

- **📅 تاريخ البدء:** يناير 2025
- **👨‍💻 المطورين:** فريق نظم إدارة المؤسسات
- **📊 عدد الجداول:** 24 جدول
- **🔗 عدد العلاقات:** 20 علاقة
- **📝 أسطر الكود:** 2000+ سطر VBA
- **📋 النماذج:** 12+ نموذج
- **📊 التقارير:** 10+ تقرير

---

**🎉 مبروك! نظام إدارة المؤسسات الشامل جاهز للاستخدام!**

*تم تطويره بعناية فائقة لخدمة المؤسسات العربية* 🇸🇦
