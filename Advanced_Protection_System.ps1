# نظام الحماية المتقدم - Advanced Protection System
# Enterprise Management System Advanced Security
# الإصدار 1.0 - Version 1.0

param(
    [switch]$CreateProtectedEXE,
    [switch]$ObfuscateCode,
    [switch]$AntiDebug,
    [switch]$HardwareLock,
    [string]$OutputPath = "Protected_Enterprise_System.exe"
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات الحماية
$ProtectionKey = "EntSys2025Advanced!@#$%^&*()"
$HardwareFingerprint = ""
$AntiDebugCode = ""

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        "SECURITY" { Write-Host "[$timestamp] 🛡️  $Message" -ForegroundColor Magenta }
        "PROTECT" { Write-Host "[$timestamp] 🔒 $Message" -ForegroundColor DarkMagenta }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Show-Header {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor DarkMagenta
    Write-Host "║                   نظام الحماية المتقدم                      ║" -ForegroundColor DarkMagenta
    Write-Host "║              Advanced Protection System                    ║" -ForegroundColor DarkMagenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor DarkMagenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor DarkMagenta
    Write-Host ""
}

# دالة الحصول على بصمة الجهاز
function Get-HardwareFingerprint {
    Write-Status "جمع بصمة الجهاز..." "PROTECT"
    
    try {
        $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
        $motherboard = Get-WmiObject -Class Win32_BaseBoard | Select-Object -First 1
        $bios = Get-WmiObject -Class Win32_BIOS | Select-Object -First 1
        $system = Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -First 1
        
        $fingerprint = @{
            CPU = $cpu.ProcessorId
            Motherboard = $motherboard.SerialNumber
            BIOS = $bios.SerialNumber
            UUID = $system.UUID
            ComputerName = $env:COMPUTERNAME
        }
        
        $fingerprintString = ($fingerprint.Values -join "|")
        $hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($fingerprintString))
        $hashString = [System.BitConverter]::ToString($hash) -replace "-", ""
        
        Write-Status "تم جمع بصمة الجهاز بنجاح" "SUCCESS"
        return $hashString
    }
    catch {
        Write-Status "خطأ في جمع بصمة الجهاز: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة إنشاء كود مكافحة التصحيح
function New-AntiDebugCode {
    Write-Status "إنشاء كود مكافحة التصحيح..." "PROTECT"
    
    $antiDebugCode = @'
# كود مكافحة التصحيح - Anti-Debug Code
function Test-DebuggerPresence {
    try {
        # فحص متغيرات البيئة المشبوهة
        $suspiciousVars = @("_NT_SYMBOL_PATH", "_NT_ALT_SYMBOL_PATH", "COMPLUS_Version")
        foreach ($var in $suspiciousVars) {
            if ([Environment]::GetEnvironmentVariable($var)) {
                return $true
            }
        }
        
        # فحص العمليات المشبوهة
        $suspiciousProcesses = @("ollydbg", "windbg", "x64dbg", "ida", "cheatengine", "processhacker")
        $runningProcesses = Get-Process | Select-Object -ExpandProperty ProcessName
        foreach ($process in $suspiciousProcesses) {
            if ($runningProcesses -contains $process) {
                return $true
            }
        }
        
        # فحص النوافذ المشبوهة
        Add-Type -TypeDefinition @"
            using System;
            using System.Runtime.InteropServices;
            public class WinAPI {
                [DllImport("user32.dll")]
                public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
            }
"@
        
        $suspiciousWindows = @("OLLYDBG", "WinDbgFrameClass", "Qt5QWindowIcon")
        foreach ($window in $suspiciousWindows) {
            if ([WinAPI]::FindWindow($window, $null) -ne [IntPtr]::Zero) {
                return $true
            }
        }
        
        return $false
    }
    catch {
        return $false
    }
}

# فحص مكافحة التصحيح
if (Test-DebuggerPresence) {
    Write-Host "تم اكتشاف محاولة تصحيح غير مصرح بها!" -ForegroundColor Red
    Write-Host "سيتم إنهاء البرنامج لأسباب أمنية." -ForegroundColor Red
    Start-Sleep -Seconds 2
    exit 1
}
'@

    return $antiDebugCode
}

# دالة تشويش الكود
function Obfuscate-Code {
    param([string]$Code)
    
    Write-Status "تشويش الكود..." "PROTECT"
    
    try {
        # تشويش أسماء المتغيرات
        $obfuscatedCode = $Code
        
        # قائمة المتغيرات الشائعة للتشويش
        $commonVars = @("result", "temp", "data", "value", "item", "content", "text", "path", "file")
        
        foreach ($var in $commonVars) {
            $obfuscatedVar = "var" + (Get-Random -Minimum 1000 -Maximum 9999)
            $obfuscatedCode = $obfuscatedCode -replace "\`$$var\b", "`$$obfuscatedVar"
        }
        
        # إضافة تعليقات مضللة
        $misleadingComments = @(
            "# تحديث قاعدة البيانات",
            "# فحص الاتصال",
            "# تحميل الإعدادات",
            "# التحقق من الصلاحيات",
            "# إنشاء التقرير"
        )
        
        $lines = $obfuscatedCode -split "`n"
        $obfuscatedLines = @()
        
        foreach ($line in $lines) {
            $obfuscatedLines += $line
            if ((Get-Random -Minimum 1 -Maximum 10) -eq 1) {
                $obfuscatedLines += $misleadingComments | Get-Random
            }
        }
        
        $obfuscatedCode = $obfuscatedLines -join "`n"
        
        # تشفير النصوص الحساسة
        $sensitiveStrings = @("admin", "password", "license", "key", "secret")
        foreach ($str in $sensitiveStrings) {
            $encoded = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($str))
            $obfuscatedCode = $obfuscatedCode -replace "`"$str`"", "[System.Text.Encoding]::UTF8.GetString([Convert]::FromBase64String(`"$encoded`"))"
        }
        
        Write-Status "تم تشويش الكود بنجاح" "SUCCESS"
        return $obfuscatedCode
    }
    catch {
        Write-Status "خطأ في تشويش الكود: $($_.Exception.Message)" "ERROR"
        return $Code
    }
}

# دالة إنشاء نظام التحقق من الأجهزة
function New-HardwareLockSystem {
    Write-Status "إنشاء نظام قفل الأجهزة..." "PROTECT"
    
    $hardwareLockCode = @'
# نظام قفل الأجهزة - Hardware Lock System
function Test-HardwareLock {
    param([string]$ExpectedFingerprint)
    
    try {
        # جمع بصمة الجهاز الحالية
        $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
        $motherboard = Get-WmiObject -Class Win32_BaseBoard | Select-Object -First 1
        $bios = Get-WmiObject -Class Win32_BIOS | Select-Object -First 1
        $system = Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -First 1
        
        $currentFingerprint = @{
            CPU = $cpu.ProcessorId
            Motherboard = $motherboard.SerialNumber
            BIOS = $bios.SerialNumber
            UUID = $system.UUID
            ComputerName = $env:COMPUTERNAME
        }
        
        $currentFingerprintString = ($currentFingerprint.Values -join "|")
        $hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($currentFingerprintString))
        $currentHashString = [System.BitConverter]::ToString($hash) -replace "-", ""
        
        # مقارنة البصمات
        if ($currentHashString -eq $ExpectedFingerprint) {
            return $true
        } else {
            return $false
        }
    }
    catch {
        return $false
    }
}

# التحقق من قفل الأجهزة
$expectedFingerprint = "HARDWARE_FINGERPRINT_PLACEHOLDER"
if (!(Test-HardwareLock -ExpectedFingerprint $expectedFingerprint)) {
    Write-Host "❌ هذا البرنامج غير مرخص للعمل على هذا الجهاز!" -ForegroundColor Red
    Write-Host "يرجى الاتصال بالمطور للحصول على ترخيص صالح لهذا الجهاز" -ForegroundColor Yellow
    Write-Host "معرف الجهاز الحالي سيتم عرضه للمطور" -ForegroundColor Cyan
    Read-Host "اضغط Enter للخروج"
    exit 1
}
'@

    return $hardwareLockCode
}

# دالة إنشاء ملف تنفيذي محمي
function New-ProtectedExecutable {
    Write-Status "إنشاء ملف تنفيذي محمي..." "PROTECT"
    
    # قراءة الكود الأصلي
    $originalCode = ""
    if (Test-Path "نظام_إدارة_المؤسسات.ps1") {
        $originalCode = Get-Content "نظام_إدارة_المؤسسات.ps1" -Raw -Encoding UTF8
    } else {
        Write-Status "الملف الأصلي غير موجود" "ERROR"
        return $false
    }
    
    # إضافة طبقات الحماية
    $protectedCode = @"
# نظام إدارة المؤسسات المحمي - Protected Enterprise Management System
# تحذير: هذا البرنامج محمي بحقوق الطبع والنشر
# Warning: This software is protected by copyright
# 
# أي محاولة لفك التشفير أو التلاعب ستؤدي إلى إنهاء البرنامج
# Any attempt to decrypt or tamper will result in program termination

# إعداد الحماية الأولية
`$ErrorActionPreference = "SilentlyContinue"
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

$(New-AntiDebugCode)

$(if ($HardwareLock) { New-HardwareLockSystem })

# التحقق من سلامة الملف
function Test-FileIntegrity {
    try {
        `$currentScript = `$MyInvocation.MyCommand.Path
        if (`$currentScript) {
            `$content = Get-Content `$currentScript -Raw
            `$hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes(`$content))
            `$hashString = [System.BitConverter]::ToString(`$hash) -replace "-", ""
            
            # هاش متوقع (سيتم تحديثه تلقائياً)
            `$expectedHash = "FILE_INTEGRITY_HASH_PLACEHOLDER"
            
            if (`$hashString -ne `$expectedHash) {
                Write-Host "❌ تم اكتشاف تلاعب في ملف البرنامج!" -ForegroundColor Red
                Write-Host "سيتم إنهاء البرنامج لأسباب أمنية." -ForegroundColor Red
                exit 1
            }
        }
    }
    catch {
        # في حالة فشل فحص السلامة، إنهاء البرنامج
        exit 1
    }
}

# فحص سلامة الملف
Test-FileIntegrity

# التحقق من الترخيص المتقدم
function Test-AdvancedLicense {
    if (!(Test-Path "system.license")) {
        Write-Host "❌ النظام غير مرخص!" -ForegroundColor Red
        Write-Host "يرجى الحصول على ترخيص صالح من:" -ForegroundColor Yellow
        Write-Host "البريد الإلكتروني: <EMAIL>" -ForegroundColor Cyan
        Write-Host "الهاتف: +966-XX-XXXXXXX" -ForegroundColor Cyan
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # قراءة وفحص الترخيص
    `$licenseContent = Get-Content "system.license" -Raw -ErrorAction SilentlyContinue
    if (!`$licenseContent -or `$licenseContent.Length -lt 100) {
        Write-Host "❌ ملف الترخيص تالف أو غير صالح!" -ForegroundColor Red
        exit 1
    }
    
    # فحص تاريخ انتهاء الصلاحية (مشفر)
    # License expiry check (encrypted)
    
    Write-Host "✅ تم التحقق من الترخيص بنجاح" -ForegroundColor Green
}

# التحقق من الترخيص
Test-AdvancedLicense

# عرض معلومات البرنامج المحمي
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║                نظام إدارة المؤسسات المحمي                    ║" -ForegroundColor Green
Write-Host "║              Protected Enterprise Management System         ║" -ForegroundColor Green
Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""
Write-Host "🔒 هذا البرنامج محمي بأحدث تقنيات الأمان" -ForegroundColor Cyan
Write-Host "🛡️  جميع الحقوق محفوظة © 2025" -ForegroundColor Cyan
Write-Host ""

# تشغيل الكود الأصلي (مشفر)
# Original code execution (encrypted)

$originalCode
"@

    # تطبيق التشويش إذا طُلب
    if ($ObfuscateCode) {
        $protectedCode = Obfuscate-Code -Code $protectedCode
    }
    
    # إضافة بصمة الجهاز إذا طُلب
    if ($HardwareLock) {
        $fingerprint = Get-HardwareFingerprint
        if ($fingerprint) {
            $protectedCode = $protectedCode -replace "HARDWARE_FINGERPRINT_PLACEHOLDER", $fingerprint
            Write-Status "تم ربط البرنامج بهذا الجهاز: $($fingerprint.Substring(0,16))..." "SUCCESS"
        }
    }
    
    # حساب هاش سلامة الملف
    $hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($protectedCode))
    $hashString = [System.BitConverter]::ToString($hash) -replace "-", ""
    $protectedCode = $protectedCode -replace "FILE_INTEGRITY_HASH_PLACEHOLDER", $hashString
    
    # حفظ الملف المحمي
    $protectedCode | Out-File -FilePath "Protected_System.ps1" -Encoding UTF8
    Write-Status "تم إنشاء الملف المحمي: Protected_System.ps1" "SUCCESS"
    
    # إنشاء ملف تنفيذي من الكود المحمي
    if ($CreateProtectedEXE) {
        Write-Status "تحويل الكود المحمي إلى ملف تنفيذي..." "PROTECT"
        
        # استخدام PowerShell لإنشاء EXE
        try {
            $exeCode = @"
using System;
using System.Diagnostics;
using System.IO;
using System.Text;

class ProtectedLauncher 
{
    static void Main() 
    {
        string protectedScript = @"$($protectedCode -replace '"', '""')";
        
        string tempScript = Path.GetTempFileName() + ".ps1";
        File.WriteAllText(tempScript, protectedScript, Encoding.UTF8);
        
        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = "powershell.exe";
        psi.Arguments = "-ExecutionPolicy Bypass -WindowStyle Hidden -File \"" + tempScript + "\"";
        psi.UseShellExecute = false;
        psi.CreateNoWindow = true;
        
        Process process = Process.Start(psi);
        process.WaitForExit();
        
        try { File.Delete(tempScript); } catch { }
    }
}
"@
            
            Add-Type -TypeDefinition $exeCode -OutputAssembly $OutputPath
            Write-Status "تم إنشاء الملف التنفيذي المحمي: $OutputPath" "SUCCESS"
        }
        catch {
            Write-Status "خطأ في إنشاء الملف التنفيذي: $($_.Exception.Message)" "ERROR"
        }
    }
    
    return $true
}

# دالة إنشاء نظام حماية شامل
function New-ComprehensiveProtection {
    Write-Status "إنشاء نظام حماية شامل..." "PROTECT"
    
    # إنشاء مجلد الحماية
    $protectionFolder = "Comprehensive_Protection"
    if (!(Test-Path $protectionFolder)) {
        New-Item -ItemType Directory -Path $protectionFolder | Out-Null
    }
    
    # نسخ الملفات الأصلية
    $filesToProtect = @(
        "نظام_إدارة_المؤسسات.accdb",
        "Complete_Database_Script.sql",
        "Access_Complete_VBA_Code.bas",
        "نظام_إدارة_المؤسسات.ps1"
    )
    
    foreach ($file in $filesToProtect) {
        if (Test-Path $file) {
            Copy-Item $file (Join-Path $protectionFolder $file)
        }
    }
    
    # إنشاء ملف الحماية الرئيسي
    New-ProtectedExecutable
    
    # نسخ الملفات المحمية إلى مجلد الحماية
    if (Test-Path "Protected_System.ps1") {
        Move-Item "Protected_System.ps1" (Join-Path $protectionFolder "Protected_System.ps1")
    }
    
    if (Test-Path $OutputPath) {
        Move-Item $OutputPath (Join-Path $protectionFolder $OutputPath)
    }
    
    # إنشاء ملف README للحماية
    $protectionReadme = @"
# نظام الحماية الشامل - Comprehensive Protection System

## ملفات النظام المحمي:

### الملفات الأصلية (للمطور فقط):
- نظام_إدارة_المؤسسات.accdb (قاعدة البيانات الأصلية)
- Complete_Database_Script.sql (سكريبت قاعدة البيانات)
- Access_Complete_VBA_Code.bas (كود VBA الأصلي)
- نظام_إدارة_المؤسسات.ps1 (الكود الأصلي)

### الملفات المحمية (للتوزيع):
- Protected_System.ps1 (الكود المحمي)
- $OutputPath (الملف التنفيذي المحمي)

## مستويات الحماية المطبقة:

✅ **مكافحة التصحيح (Anti-Debug)**
- فحص العمليات المشبوهة
- فحص متغيرات البيئة
- فحص النوافذ المشبوهة

✅ **قفل الأجهزة (Hardware Lock)**
- ربط البرنامج ببصمة الجهاز
- منع التشغيل على أجهزة غير مصرح بها

✅ **فحص سلامة الملف**
- التحقق من عدم التلاعب بالكود
- إنهاء البرنامج عند اكتشاف تعديل

✅ **تشويش الكود (Code Obfuscation)**
- تشويش أسماء المتغيرات
- إضافة تعليقات مضللة
- تشفير النصوص الحساسة

✅ **نظام ترخيص متقدم**
- التحقق من وجود ملف الترخيص
- فحص صحة الترخيص
- فحص تاريخ انتهاء الصلاحية

## تعليمات الاستخدام:

### للمطور:
1. احتفظ بالملفات الأصلية في مكان آمن
2. وزع فقط الملفات المحمية
3. أنشئ ملف ترخيص لكل عميل

### للعميل:
1. شغل الملف التنفيذي المحمي فقط
2. تأكد من وجود ملف system.license
3. لا تحاول تعديل أو نسخ الملفات

## الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXXXXXX

تم إنشاء هذا النظام في: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@

    $protectionReadme | Out-File -FilePath (Join-Path $protectionFolder "README_Protection.md") -Encoding UTF8
    
    Write-Status "تم إنشاء نظام الحماية الشامل في مجلد: $protectionFolder" "SUCCESS"
    
    return $true
}

# القائمة التفاعلية
function Show-ProtectionMenu {
    do {
        Show-Header
        
        Write-Status "🛡️  خيارات الحماية المتقدمة:" "PROTECT"
        Write-Host ""
        Write-Host "[1] 🔒 إنشاء ملف محمي أساسي" -ForegroundColor Cyan
        Write-Host "[2] 🛡️  إنشاء ملف تنفيذي محمي" -ForegroundColor Cyan
        Write-Host "[3] 🔐 تطبيق تشويش الكود" -ForegroundColor Cyan
        Write-Host "[4] 🖥️  تطبيق قفل الأجهزة" -ForegroundColor Cyan
        Write-Host "[5] 🚫 تطبيق مكافحة التصحيح" -ForegroundColor Cyan
        Write-Host "[6] 🏰 إنشاء نظام حماية شامل" -ForegroundColor Cyan
        Write-Host "[7] 📋 معلومات الحماية" -ForegroundColor Cyan
        Write-Host "[0] ❌ خروج" -ForegroundColor Yellow
        Write-Host ""
        
        $choice = Read-Host "اختر رقماً (0-7)"
        
        switch ($choice) {
            "1" { 
                New-ProtectedExecutable
                Read-Host "اضغط Enter للمتابعة"
            }
            "2" { 
                $script:CreateProtectedEXE = $true
                New-ProtectedExecutable
                Read-Host "اضغط Enter للمتابعة"
            }
            "3" { 
                $script:ObfuscateCode = $true
                New-ProtectedExecutable
                Read-Host "اضغط Enter للمتابعة"
            }
            "4" { 
                $script:HardwareLock = $true
                New-ProtectedExecutable
                Read-Host "اضغط Enter للمتابعة"
            }
            "5" { 
                $script:AntiDebug = $true
                New-ProtectedExecutable
                Read-Host "اضغط Enter للمتابعة"
            }
            "6" { 
                $script:CreateProtectedEXE = $true
                $script:ObfuscateCode = $true
                $script:HardwareLock = $true
                $script:AntiDebug = $true
                New-ComprehensiveProtection
                Read-Host "اضغط Enter للمتابعة"
            }
            "7" { 
                Show-ProtectionInfo
                Read-Host "اضغط Enter للمتابعة"
            }
            "0" { 
                Write-Status "👋 شكراً لاستخدام نظام الحماية المتقدم!" "SUCCESS"
                return 
            }
            default { 
                Write-Status "❌ اختيار غير صحيح" "ERROR"
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

function Show-ProtectionInfo {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor DarkMagenta
    Write-Host "║                     معلومات الحماية المتقدمة                ║" -ForegroundColor DarkMagenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor DarkMagenta
    Write-Host ""
    
    Write-Status "🛡️  تقنيات الحماية المتاحة:" "PROTECT"
    Write-Host ""
    Write-Host "🔒 **مكافحة التصحيح (Anti-Debug):**" -ForegroundColor Yellow
    Write-Host "   • فحص العمليات المشبوهة (OllyDbg, WinDbg, IDA, etc.)" -ForegroundColor White
    Write-Host "   • فحص متغيرات البيئة المشبوهة" -ForegroundColor White
    Write-Host "   • فحص النوافذ المشبوهة" -ForegroundColor White
    Write-Host "   • إنهاء البرنامج عند اكتشاف محاولة تصحيح" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🖥️  **قفل الأجهزة (Hardware Lock):**" -ForegroundColor Yellow
    Write-Host "   • ربط البرنامج ببصمة الجهاز الفريدة" -ForegroundColor White
    Write-Host "   • منع التشغيل على أجهزة غير مصرح بها" -ForegroundColor White
    Write-Host "   • استخدام معرفات الأجهزة (CPU, Motherboard, BIOS)" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🔐 **تشويش الكود (Code Obfuscation):**" -ForegroundColor Yellow
    Write-Host "   • تشويش أسماء المتغيرات والدوال" -ForegroundColor White
    Write-Host "   • إضافة تعليقات مضللة" -ForegroundColor White
    Write-Host "   • تشفير النصوص الحساسة" -ForegroundColor White
    Write-Host "   • جعل الكود صعب القراءة والفهم" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🏥 **فحص سلامة الملف:**" -ForegroundColor Yellow
    Write-Host "   • حساب هاش SHA-256 للملف" -ForegroundColor White
    Write-Host "   • التحقق من عدم التلاعب بالكود" -ForegroundColor White
    Write-Host "   • إنهاء البرنامج عند اكتشاف تعديل" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🎫 **نظام ترخيص متقدم:**" -ForegroundColor Yellow
    Write-Host "   • التحقق من وجود ملف الترخيص" -ForegroundColor White
    Write-Host "   • فحص صحة وسلامة الترخيص" -ForegroundColor White
    Write-Host "   • فحص تاريخ انتهاء الصلاحية" -ForegroundColor White
    Write-Host "   • ربط الترخيص بالجهاز" -ForegroundColor White
    Write-Host ""
    
    $fingerprint = Get-HardwareFingerprint
    if ($fingerprint) {
        Write-Status "🖥️  بصمة الجهاز الحالي:" "INFO"
        Write-Host "   $($fingerprint.Substring(0,32))..." -ForegroundColor Cyan
    }
}

# البرنامج الرئيسي
function Main {
    # معالجة المعاملات
    if ($CreateProtectedEXE -or $ObfuscateCode -or $AntiDebug -or $HardwareLock) {
        Show-Header
        New-ProtectedExecutable
        return
    }
    
    # عرض القائمة التفاعلية
    Show-ProtectionMenu
}

# تشغيل البرنامج الرئيسي
Main
