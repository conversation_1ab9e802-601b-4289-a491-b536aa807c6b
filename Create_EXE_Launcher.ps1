# إنشاء ملف EXE لنظام إدارة المؤسسات
# Create EXE Launcher for Enterprise Management System

param(
    [string]$OutputPath = "نظام_إدارة_المؤسسات.exe",
    [string]$IconPath = "",
    [switch]$CreateInstaller
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Show-Header {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                    إنشاء ملف EXE للنظام                     ║" -ForegroundColor Magenta
    Write-Host "║              Create EXE Launcher for System               ║" -ForegroundColor Magenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

function New-CSharpLauncher {
    Write-Status "إنشاء كود C# للانشر..."
    
    $csharpCode = @'
using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using System.Drawing;
using System.Runtime.InteropServices;

namespace EnterpriseManagementLauncher
{
    public partial class MainForm : Form
    {
        [DllImport("kernel32.dll")]
        static extern IntPtr GetConsoleWindow();

        [DllImport("user32.dll")]
        static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        const int SW_HIDE = 0;

        private Button btnQuickStart;
        private Button btnAdvancedStart;
        private Button btnSetup;
        private Button btnBackup;
        private Button btnExit;
        private Label lblTitle;
        private Label lblSubtitle;
        private Label lblCredentials;
        private PictureBox pictureBox;

        public MainForm()
        {
            InitializeComponent();
            
            // إخفاء نافذة الكونسول
            var handle = GetConsoleWindow();
            ShowWindow(handle, SW_HIDE);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النموذج الرئيسي
            this.Text = "نظام إدارة المؤسسات - Enterprise Management System";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(248, 249, 250);
            
            // العنوان الرئيسي
            lblTitle = new Label();
            lblTitle.Text = "نظام إدارة المؤسسات الشامل";
            lblTitle.Font = new Font("Tahoma", 16, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(46, 134, 171);
            lblTitle.Size = new Size(500, 30);
            lblTitle.Location = new Point(50, 30);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(lblTitle);
            
            // العنوان الفرعي
            lblSubtitle = new Label();
            lblSubtitle.Text = "Enterprise Management System";
            lblSubtitle.Font = new Font("Segoe UI", 12, FontStyle.Regular);
            lblSubtitle.ForeColor = Color.FromArgb(108, 117, 125);
            lblSubtitle.Size = new Size(500, 25);
            lblSubtitle.Location = new Point(50, 65);
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(lblSubtitle);
            
            // بيانات تسجيل الدخول
            lblCredentials = new Label();
            lblCredentials.Text = "بيانات تسجيل الدخول الافتراضية:\nاسم المستخدم: admin\nكلمة المرور: 12345";
            lblCredentials.Font = new Font("Tahoma", 10, FontStyle.Regular);
            lblCredentials.ForeColor = Color.FromArgb(220, 53, 69);
            lblCredentials.Size = new Size(500, 60);
            lblCredentials.Location = new Point(50, 100);
            lblCredentials.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(lblCredentials);
            
            // زر التشغيل السريع
            btnQuickStart = new Button();
            btnQuickStart.Text = "🚀 تشغيل سريع\nQuick Start";
            btnQuickStart.Font = new Font("Tahoma", 11, FontStyle.Bold);
            btnQuickStart.Size = new Size(200, 60);
            btnQuickStart.Location = new Point(50, 200);
            btnQuickStart.BackColor = Color.FromArgb(76, 175, 80);
            btnQuickStart.ForeColor = Color.White;
            btnQuickStart.FlatStyle = FlatStyle.Flat;
            btnQuickStart.FlatAppearance.BorderSize = 0;
            btnQuickStart.Click += BtnQuickStart_Click;
            this.Controls.Add(btnQuickStart);
            
            // زر التشغيل المتقدم
            btnAdvancedStart = new Button();
            btnAdvancedStart.Text = "🔧 تشغيل متقدم\nAdvanced Start";
            btnAdvancedStart.Font = new Font("Tahoma", 11, FontStyle.Bold);
            btnAdvancedStart.Size = new Size(200, 60);
            btnAdvancedStart.Location = new Point(270, 200);
            btnAdvancedStart.BackColor = Color.FromArgb(46, 134, 171);
            btnAdvancedStart.ForeColor = Color.White;
            btnAdvancedStart.FlatStyle = FlatStyle.Flat;
            btnAdvancedStart.FlatAppearance.BorderSize = 0;
            btnAdvancedStart.Click += BtnAdvancedStart_Click;
            this.Controls.Add(btnAdvancedStart);
            
            // زر الإعداد
            btnSetup = new Button();
            btnSetup.Text = "⚙️ إعداد النظام\nSystem Setup";
            btnSetup.Font = new Font("Tahoma", 11, FontStyle.Bold);
            btnSetup.Size = new Size(200, 60);
            btnSetup.Location = new Point(50, 280);
            btnSetup.BackColor = Color.FromArgb(255, 152, 0);
            btnSetup.ForeColor = Color.White;
            btnSetup.FlatStyle = FlatStyle.Flat;
            btnSetup.FlatAppearance.BorderSize = 0;
            btnSetup.Click += BtnSetup_Click;
            this.Controls.Add(btnSetup);
            
            // زر النسخ الاحتياطي
            btnBackup = new Button();
            btnBackup.Text = "💾 نسخة احتياطية\nBackup";
            btnBackup.Font = new Font("Tahoma", 11, FontStyle.Bold);
            btnBackup.Size = new Size(200, 60);
            btnBackup.Location = new Point(270, 280);
            btnBackup.BackColor = Color.FromArgb(156, 39, 176);
            btnBackup.ForeColor = Color.White;
            btnBackup.FlatStyle = FlatStyle.Flat;
            btnBackup.FlatAppearance.BorderSize = 0;
            btnBackup.Click += BtnBackup_Click;
            this.Controls.Add(btnBackup);
            
            // زر الخروج
            btnExit = new Button();
            btnExit.Text = "❌ خروج - Exit";
            btnExit.Font = new Font("Tahoma", 10, FontStyle.Regular);
            btnExit.Size = new Size(150, 40);
            btnExit.Location = new Point(225, 380);
            btnExit.BackColor = Color.FromArgb(108, 117, 125);
            btnExit.ForeColor = Color.White;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.FlatAppearance.BorderSize = 0;
            btnExit.Click += BtnExit_Click;
            this.Controls.Add(btnExit);
            
            this.ResumeLayout(false);
        }

        private void BtnQuickStart_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists("تشغيل_سريع.cmd"))
                {
                    Process.Start("تشغيل_سريع.cmd");
                }
                else if (File.Exists("نظام_إدارة_المؤسسات.accdb"))
                {
                    Process.Start("msaccess.exe", "\"نظام_إدارة_المؤسسات.accdb\"");
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملفات النظام!\nيرجى التأكد من وجود الملفات في نفس المجلد.", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                this.WindowState = FormWindowState.Minimized;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام:\n{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdvancedStart_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists("Start-EnterpriseSystem.ps1"))
                {
                    ProcessStartInfo psi = new ProcessStartInfo();
                    psi.FileName = "powershell.exe";
                    psi.Arguments = "-ExecutionPolicy Bypass -File \"Start-EnterpriseSystem.ps1\"";
                    psi.UseShellExecute = true;
                    Process.Start(psi);
                }
                else
                {
                    BtnQuickStart_Click(sender, e);
                }
                
                this.WindowState = FormWindowState.Minimized;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشغيل المتقدم:\n{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSetup_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists("Setup-System.ps1"))
                {
                    ProcessStartInfo psi = new ProcessStartInfo();
                    psi.FileName = "powershell.exe";
                    psi.Arguments = "-ExecutionPolicy Bypass -File \"Setup-System.ps1\" -FullSetup";
                    psi.UseShellExecute = true;
                    psi.Verb = "runas"; // تشغيل كمدير
                    Process.Start(psi);
                }
                else
                {
                    MessageBox.Show("ملف الإعداد غير موجود!\nيرجى التأكد من وجود ملف Setup-System.ps1", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد النظام:\n{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists("نظام_إدارة_المؤسسات.accdb"))
                {
                    string backupDir = "Backups";
                    if (!Directory.Exists(backupDir))
                        Directory.CreateDirectory(backupDir);
                    
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    string backupPath = Path.Combine(backupDir, $"نظام_إدارة_المؤسسات_backup_{timestamp}.accdb");
                    
                    File.Copy("نظام_إدارة_المؤسسات.accdb", backupPath);
                    
                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح!\n\nالمسار:\n{backupPath}", 
                                  "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملف قاعدة البيانات!", 
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية:\n{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }

    class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }
    }
}
'@

    $csharpPath = "EnterpriseManagementLauncher.cs"
    $csharpCode | Out-File -FilePath $csharpPath -Encoding UTF8
    Write-Status "تم إنشاء ملف C#: $csharpPath" "SUCCESS"
    
    return $csharpPath
}

function Compile-CSharpToExe {
    param([string]$CSharpFile, [string]$OutputExe)
    
    Write-Status "تجميع ملف C# إلى EXE..."
    
    try {
        # البحث عن مجمع C#
        $cscPaths = @(
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\*\MSBuild\Current\Bin\Roslyn\csc.exe",
            "${env:ProgramFiles}\Microsoft Visual Studio\2022\*\MSBuild\Current\Bin\Roslyn\csc.exe",
            "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2022\*\MSBuild\Current\Bin\Roslyn\csc.exe",
            "${env:WINDIR}\Microsoft.NET\Framework64\v4.0.30319\csc.exe",
            "${env:WINDIR}\Microsoft.NET\Framework\v4.0.30319\csc.exe"
        )
        
        $cscPath = $null
        foreach ($path in $cscPaths) {
            $found = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($found) {
                $cscPath = $found.FullName
                break
            }
        }
        
        if (-not $cscPath) {
            Write-Status "لم يتم العثور على مجمع C#. جاري استخدام Add-Type..." "WARNING"
            
            # استخدام Add-Type كبديل
            $source = Get-Content $CSharpFile -Raw
            Add-Type -TypeDefinition $source -ReferencedAssemblies System.Windows.Forms,System.Drawing -OutputAssembly $OutputExe
            Write-Status "تم تجميع الملف باستخدام Add-Type" "SUCCESS"
            return $true
        }
        
        # تجميع الملف
        $arguments = @(
            "/target:winexe",
            "/reference:System.Windows.Forms.dll",
            "/reference:System.Drawing.dll",
            "/out:$OutputExe",
            $CSharpFile
        )
        
        $process = Start-Process -FilePath $cscPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0 -and (Test-Path $OutputExe)) {
            Write-Status "تم تجميع الملف بنجاح: $OutputExe" "SUCCESS"
            return $true
        } else {
            Write-Status "فشل في تجميع الملف. رمز الخطأ: $($process.ExitCode)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Status "خطأ في التجميع: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-BatchLauncher {
    Write-Status "إنشاء ملف Batch كبديل..."
    
    $batchContent = @'
@echo off
chcp 65001 >nul
title نظام إدارة المؤسسات - Enterprise Management System

:MAIN_MENU
cls
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                نظام إدارة المؤسسات الشامل                ║
echo     ║              Enterprise Management System               ║
echo     ║                     الإصدار 1.0                        ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.
echo     🔑 بيانات تسجيل الدخول الافتراضية:
echo        اسم المستخدم: admin
echo        كلمة المرور: 12345
echo.
echo     🚀 اختر طريقة التشغيل:
echo.
echo     [1] تشغيل سريع (مُوصى به)
echo     [2] تشغيل متقدم مع فحص النظام  
echo     [3] إعداد النظام الكامل
echo     [4] إنشاء نسخة احتياطية
echo     [5] معلومات النظام
echo     [0] خروج
echo.
set /p choice="    اختر رقماً (0-5): "

if "%choice%"=="1" goto QUICK_START
if "%choice%"=="2" goto ADVANCED_START
if "%choice%"=="3" goto SETUP_SYSTEM
if "%choice%"=="4" goto CREATE_BACKUP
if "%choice%"=="5" goto SHOW_INFO
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:QUICK_START
cls
echo.
echo     🚀 تشغيل سريع للنظام...
echo.

if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ✅ تم العثور على ملف قاعدة البيانات
    echo     🔄 جاري تشغيل النظام...
    start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb"
    echo     ✅ تم تشغيل النظام بنجاح!
) else (
    echo     ❌ لم يتم العثور على ملف قاعدة البيانات!
    echo     يرجى استخدام الخيار [3] لإعداد النظام أولاً
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:ADVANCED_START
cls
echo.
echo     🔧 تشغيل متقدم مع فحص النظام...
echo.

if exist "Start-EnterpriseSystem.ps1" (
    powershell -ExecutionPolicy Bypass -File "Start-EnterpriseSystem.ps1"
) else (
    echo     ⚠️ ملف التشغيل المتقدم غير موجود
    echo     🔄 التبديل للتشغيل العادي...
    goto QUICK_START
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:SETUP_SYSTEM
cls
echo.
echo     ⚙️ إعداد النظام الكامل...
echo.

if exist "Setup-System.ps1" (
    echo     🔄 جاري تشغيل الإعداد التلقائي...
    powershell -ExecutionPolicy Bypass -File "Setup-System.ps1" -FullSetup
) else (
    echo     ❌ ملف الإعداد غير موجود!
    echo     يرجى التأكد من وجود ملف Setup-System.ps1
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:CREATE_BACKUP
cls
echo.
echo     💾 إنشاء نسخة احتياطية...
echo.

if not exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ❌ لا يمكن العثور على ملف قاعدة البيانات!
    goto BACKUP_END
)

if not exist "Backups" mkdir "Backups"

for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set mytime=%mytime: =%
set backup_name=نظام_إدارة_المؤسسات_backup_%mydate%_%mytime%.accdb

echo     🔄 جاري إنشاء النسخة الاحتياطية...
copy "نظام_إدارة_المؤسسات.accdb" "Backups\%backup_name%" >nul

if exist "Backups\%backup_name%" (
    echo     ✅ تم إنشاء النسخة الاحتياطية بنجاح!
    echo     📁 اسم الملف: %backup_name%
    echo     📂 المجلد: Backups\
) else (
    echo     ❌ فشل في إنشاء النسخة الاحتياطية!
)

:BACKUP_END
echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:SHOW_INFO
cls
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                    معلومات النظام                       ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.
echo     📋 تفاصيل النظام:
echo     ├─ الاسم: نظام إدارة المؤسسات الشامل
echo     ├─ الإصدار: 1.0
echo     ├─ التاريخ: %date%
echo     └─ الوقت: %time%
echo.
echo     🔑 بيانات تسجيل الدخول الافتراضية:
echo     ├─ اسم المستخدم: admin
echo     └─ كلمة المرور: 12345
echo.
echo     📁 ملفات النظام:
if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ├─ قاعدة البيانات: ✅ موجودة
) else (
    echo     ├─ قاعدة البيانات: ❌ غير موجودة
)

if exist "Complete_Database_Script.sql" (
    echo     ├─ سكريبت قاعدة البيانات: ✅ موجود
) else (
    echo     ├─ سكريبت قاعدة البيانات: ❌ غير موجود
)

echo     └─ ملف التشغيل: ✅ موجود
echo.
echo     📞 الدعم الفني:
echo     ├─ البريد الإلكتروني: <EMAIL>
echo     ├─ الهاتف: +966-XX-XXXXXXX
echo     └─ الموقع: www.enterprise-system.com
echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo     👋 شكراً لاستخدام نظام إدارة المؤسسات!
echo.
timeout /t 2 >nul
exit /b 0
'@

    $batchPath = "نظام_إدارة_المؤسسات_Launcher.bat"
    $batchContent | Out-File -FilePath $batchPath -Encoding UTF8
    Write-Status "تم إنشاء ملف Batch: $batchPath" "SUCCESS"
    
    return $batchPath
}

function Convert-BatchToExe {
    param([string]$BatchFile, [string]$OutputExe)
    
    Write-Status "تحويل ملف Batch إلى EXE..."
    
    # إنشاء ملف PowerShell لتحويل Batch إلى EXE
    $converterScript = @"
# تحويل Batch إلى EXE باستخدام PowerShell
`$batchContent = Get-Content '$BatchFile' -Raw -Encoding UTF8

`$exeTemplate = @'
using System;
using System.Diagnostics;
using System.IO;
using System.Text;

class Program 
{
    static void Main() 
    {
        string batchContent = @"BATCH_CONTENT_PLACEHOLDER";
        
        string tempBatch = Path.GetTempFileName() + ".bat";
        File.WriteAllText(tempBatch, batchContent, Encoding.UTF8);
        
        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = tempBatch;
        psi.UseShellExecute = true;
        psi.WindowStyle = ProcessWindowStyle.Normal;
        
        Process process = Process.Start(psi);
        process.WaitForExit();
        
        try { File.Delete(tempBatch); } catch { }
    }
}
'@

`$exeCode = `$exeTemplate.Replace('BATCH_CONTENT_PLACEHOLDER', `$batchContent.Replace('"', '""'))
`$exeCode | Out-File -FilePath 'temp_converter.cs' -Encoding UTF8

# تجميع الملف
Add-Type -TypeDefinition `$exeCode -OutputAssembly '$OutputExe'
Remove-Item 'temp_converter.cs' -ErrorAction SilentlyContinue
"@

    $converterScript | Out-File -FilePath "temp_converter.ps1" -Encoding UTF8
    
    try {
        powershell -ExecutionPolicy Bypass -File "temp_converter.ps1"
        Remove-Item "temp_converter.ps1" -ErrorAction SilentlyContinue
        
        if (Test-Path $OutputExe) {
            Write-Status "تم تحويل Batch إلى EXE بنجاح" "SUCCESS"
            return $true
        } else {
            Write-Status "فشل في تحويل Batch إلى EXE" "ERROR"
            return $false
        }
    }
    catch {
        Write-Status "خطأ في التحويل: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-InstallationPackage {
    Write-Status "إنشاء حزمة التثبيت..."
    
    $installerScript = @'
@echo off
chcp 65001 >nul
title نظام إدارة المؤسسات - معالج التثبيت

echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║              معالج تثبيت نظام إدارة المؤسسات              ║
echo     ║            Enterprise Management System Installer       ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.

set "INSTALL_DIR=%USERPROFILE%\Documents\نظام_إدارة_المؤسسات"

echo     📁 مجلد التثبيت الافتراضي:
echo        %INSTALL_DIR%
echo.
set /p "custom_dir=    أدخل مجلد مختلف أو اضغط Enter للمتابعة: "

if not "%custom_dir%"=="" set "INSTALL_DIR=%custom_dir%"

echo.
echo     🔄 جاري التثبيت في: %INSTALL_DIR%
echo.

REM إنشاء مجلد التثبيت
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM نسخ الملفات
echo     📋 نسخ ملفات النظام...
copy /Y "*.accdb" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.sql" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.ps1" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.cmd" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.bat" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.exe" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.md" "%INSTALL_DIR%\" >nul 2>&1
copy /Y "*.txt" "%INSTALL_DIR%\" >nul 2>&1

REM إنشاء المجلدات
echo     📁 إنشاء هيكل المجلدات...
mkdir "%INSTALL_DIR%\Backups" >nul 2>&1
mkdir "%INSTALL_DIR%\Reports" >nul 2>&1
mkdir "%INSTALL_DIR%\Logs" >nul 2>&1
mkdir "%INSTALL_DIR%\Temp" >nul 2>&1

REM إنشاء اختصار سطح المكتب
echo     🖥️ إنشاء اختصار سطح المكتب...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة المؤسسات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\نظام_إدارة_المؤسسات.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة المؤسسات الشامل'; $Shortcut.Save()" >nul 2>&1

echo.
echo     ✅ تم التثبيت بنجاح!
echo.
echo     📋 تفاصيل التثبيت:
echo     ├─ مجلد التثبيت: %INSTALL_DIR%
echo     ├─ اختصار سطح المكتب: تم إنشاؤه
echo     └─ ملفات النظام: تم نسخها
echo.
echo     🚀 لتشغيل النظام:
echo     • انقر نقراً مزدوجاً على اختصار سطح المكتب
echo     • أو شغل الملف من مجلد التثبيت
echo.
echo     🔑 بيانات تسجيل الدخول الافتراضية:
echo     • اسم المستخدم: admin
echo     • كلمة المرور: 12345
echo.

set /p "run_now=    هل تريد تشغيل النظام الآن؟ (Y/N): "
if /i "%run_now%"=="Y" (
    cd /d "%INSTALL_DIR%"
    start "" "نظام_إدارة_المؤسسات.exe"
)

echo.
echo     شكراً لاستخدام نظام إدارة المؤسسات!
pause
'@

    $installerScript | Out-File -FilePath "Install_Enterprise_System.bat" -Encoding UTF8
    Write-Status "تم إنشاء معالج التثبيت: Install_Enterprise_System.bat" "SUCCESS"
}

# البرنامج الرئيسي
function Main {
    Show-Header
    
    Write-Status "بدء عملية إنشاء ملف EXE..."
    
    # إنشاء ملف C# وتجميعه
    $csharpFile = New-CSharpLauncher
    
    if (Compile-CSharpToExe -CSharpFile $csharpFile -OutputExe $OutputPath) {
        Write-Status "تم إنشاء ملف EXE بنجاح: $OutputPath" "SUCCESS"
        
        # حذف ملف C# المؤقت
        Remove-Item $csharpFile -ErrorAction SilentlyContinue
    }
    else {
        Write-Status "فشل في إنشاء ملف EXE. جاري إنشاء بديل..." "WARNING"
        
        # إنشاء ملف Batch كبديل
        $batchFile = New-BatchLauncher
        
        # محاولة تحويل Batch إلى EXE
        if (!(Convert-BatchToExe -BatchFile $batchFile -OutputExe $OutputPath)) {
            Write-Status "سيتم استخدام ملف Batch كبديل: $batchFile" "INFO"
        }
    }
    
    # إنشاء حزمة التثبيت إذا طُلب ذلك
    if ($CreateInstaller) {
        New-InstallationPackage
    }
    
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                    تم إكمال العملية بنجاح!                   ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    
    if (Test-Path $OutputPath) {
        Write-Status "ملف EXE جاهز: $OutputPath" "SUCCESS"
        Write-Status "يمكنك الآن تشغيل النظام بالنقر المزدوج على الملف" "INFO"
    }
    
    if ($CreateInstaller -and (Test-Path "Install_Enterprise_System.bat")) {
        Write-Status "معالج التثبيت جاهز: Install_Enterprise_System.bat" "SUCCESS"
    }
    
    Write-Host ""
    Write-Status "🎉 نظام إدارة المؤسسات جاهز للتوزيع والاستخدام!" "SUCCESS"
    
    Read-Host "اضغط Enter للخروج"
}

# دالة إنشاء EXE مبسط باستخدام IExpress
function New-SimpleExe {
    Write-Status "إنشاء ملف EXE مبسط باستخدام IExpress..."

    # إنشاء ملف SED لـ IExpress
    $sedContent = @"
[Version]
Class=IEXPRESS
SEDVersion=3
[Options]
PackagePurpose=InstallApp
ShowInstallProgramWindow=0
HideExtractAnimation=1
UseLongFileName=1
InsideCompressed=0
CAB_FixedSize=0
CAB_ResvCodeSigning=0
RebootMode=N
InstallPrompt=%InstallPrompt%
DisplayLicense=%DisplayLicense%
FinishMessage=%FinishMessage%
TargetName=%TargetName%
FriendlyName=%FriendlyName%
AppLaunched=%AppLaunched%
PostInstallCmd=%PostInstallCmd%
AdminQuietInstCmd=%AdminQuietInstCmd%
UserQuietInstCmd=%UserQuietInstCmd%
SourceFiles=SourceFiles
[Strings]
InstallPrompt=هل تريد تشغيل نظام إدارة المؤسسات؟
DisplayLicense=
FinishMessage=تم تشغيل نظام إدارة المؤسسات بنجاح!
TargetName=نظام_إدارة_المؤسسات_Simple.exe
FriendlyName=نظام إدارة المؤسسات الشامل
AppLaunched=تشغيل_سريع.cmd
PostInstallCmd=<None>
AdminQuietInstCmd=
UserQuietInstCmd=
FILE0="تشغيل_سريع.cmd"
[SourceFiles]
SourceFiles0=.
[SourceFiles0]
%FILE0%=
"@

    $sedPath = "enterprise_system.sed"
    $sedContent | Out-File -FilePath $sedPath -Encoding ASCII

    try {
        # تشغيل IExpress
        $process = Start-Process -FilePath "iexpress.exe" -ArgumentList "/N $sedPath" -Wait -PassThru -NoNewWindow

        if ($process.ExitCode -eq 0 -and (Test-Path "نظام_إدارة_المؤسسات_Simple.exe")) {
            Write-Status "تم إنشاء ملف EXE مبسط بنجاح" "SUCCESS"
            Remove-Item $sedPath -ErrorAction SilentlyContinue
            return $true
        }
    }
    catch {
        Write-Status "خطأ في استخدام IExpress: $($_.Exception.Message)" "WARNING"
    }

    Remove-Item $sedPath -ErrorAction SilentlyContinue
    return $false
}

# دالة إنشاء ملف تنفيذي باستخدام PowerShell
function New-PowerShellExe {
    Write-Status "إنشاء ملف تنفيذي باستخدام PowerShell..."

    $psScript = @'
# نظام إدارة المؤسسات - ملف التشغيل الرئيسي
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

[System.Windows.Forms.Application]::EnableVisualStyles()

$form = New-Object System.Windows.Forms.Form
$form.Text = "نظام إدارة المؤسسات - Enterprise Management System"
$form.Size = New-Object System.Drawing.Size(600, 500)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false
$form.BackColor = [System.Drawing.Color]::FromArgb(248, 249, 250)

# العنوان الرئيسي
$lblTitle = New-Object System.Windows.Forms.Label
$lblTitle.Text = "نظام إدارة المؤسسات الشامل"
$lblTitle.Font = New-Object System.Drawing.Font("Tahoma", 16, [System.Drawing.FontStyle]::Bold)
$lblTitle.ForeColor = [System.Drawing.Color]::FromArgb(46, 134, 171)
$lblTitle.Size = New-Object System.Drawing.Size(500, 30)
$lblTitle.Location = New-Object System.Drawing.Point(50, 30)
$lblTitle.TextAlign = "MiddleCenter"
$form.Controls.Add($lblTitle)

# العنوان الفرعي
$lblSubtitle = New-Object System.Windows.Forms.Label
$lblSubtitle.Text = "Enterprise Management System"
$lblSubtitle.Font = New-Object System.Drawing.Font("Segoe UI", 12)
$lblSubtitle.ForeColor = [System.Drawing.Color]::FromArgb(108, 117, 125)
$lblSubtitle.Size = New-Object System.Drawing.Size(500, 25)
$lblSubtitle.Location = New-Object System.Drawing.Point(50, 65)
$lblSubtitle.TextAlign = "MiddleCenter"
$form.Controls.Add($lblSubtitle)

# بيانات تسجيل الدخول
$lblCredentials = New-Object System.Windows.Forms.Label
$lblCredentials.Text = "بيانات تسجيل الدخول الافتراضية:`nاسم المستخدم: admin`nكلمة المرور: 12345"
$lblCredentials.Font = New-Object System.Drawing.Font("Tahoma", 10)
$lblCredentials.ForeColor = [System.Drawing.Color]::FromArgb(220, 53, 69)
$lblCredentials.Size = New-Object System.Drawing.Size(500, 60)
$lblCredentials.Location = New-Object System.Drawing.Point(50, 100)
$lblCredentials.TextAlign = "MiddleCenter"
$form.Controls.Add($lblCredentials)

# زر التشغيل السريع
$btnQuickStart = New-Object System.Windows.Forms.Button
$btnQuickStart.Text = "🚀 تشغيل سريع`nQuick Start"
$btnQuickStart.Font = New-Object System.Drawing.Font("Tahoma", 11, [System.Drawing.FontStyle]::Bold)
$btnQuickStart.Size = New-Object System.Drawing.Size(200, 60)
$btnQuickStart.Location = New-Object System.Drawing.Point(50, 200)
$btnQuickStart.BackColor = [System.Drawing.Color]::FromArgb(76, 175, 80)
$btnQuickStart.ForeColor = [System.Drawing.Color]::White
$btnQuickStart.FlatStyle = "Flat"
$btnQuickStart.Add_Click({
    try {
        if (Test-Path "نظام_إدارة_المؤسسات.accdb") {
            Start-Process "msaccess.exe" -ArgumentList "`"نظام_إدارة_المؤسسات.accdb`""
            $form.WindowState = "Minimized"
        } elseif (Test-Path "تشغيل_سريع.cmd") {
            Start-Process "تشغيل_سريع.cmd"
            $form.WindowState = "Minimized"
        } else {
            [System.Windows.Forms.MessageBox]::Show("لم يتم العثور على ملفات النظام!`nيرجى التأكد من وجود الملفات في نفس المجلد.", "خطأ", "OK", "Error")
        }
    } catch {
        [System.Windows.Forms.MessageBox]::Show("خطأ في تشغيل النظام:`n$($_.Exception.Message)", "خطأ", "OK", "Error")
    }
})
$form.Controls.Add($btnQuickStart)

# زر التشغيل المتقدم
$btnAdvancedStart = New-Object System.Windows.Forms.Button
$btnAdvancedStart.Text = "🔧 تشغيل متقدم`nAdvanced Start"
$btnAdvancedStart.Font = New-Object System.Drawing.Font("Tahoma", 11, [System.Drawing.FontStyle]::Bold)
$btnAdvancedStart.Size = New-Object System.Drawing.Size(200, 60)
$btnAdvancedStart.Location = New-Object System.Drawing.Point(270, 200)
$btnAdvancedStart.BackColor = [System.Drawing.Color]::FromArgb(46, 134, 171)
$btnAdvancedStart.ForeColor = [System.Drawing.Color]::White
$btnAdvancedStart.FlatStyle = "Flat"
$btnAdvancedStart.Add_Click({
    try {
        if (Test-Path "Start-EnterpriseSystem.ps1") {
            Start-Process "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File `"Start-EnterpriseSystem.ps1`""
            $form.WindowState = "Minimized"
        } else {
            $btnQuickStart.PerformClick()
        }
    } catch {
        [System.Windows.Forms.MessageBox]::Show("خطأ في التشغيل المتقدم:`n$($_.Exception.Message)", "خطأ", "OK", "Error")
    }
})
$form.Controls.Add($btnAdvancedStart)

# زر الإعداد
$btnSetup = New-Object System.Windows.Forms.Button
$btnSetup.Text = "⚙️ إعداد النظام`nSystem Setup"
$btnSetup.Font = New-Object System.Drawing.Font("Tahoma", 11, [System.Drawing.FontStyle]::Bold)
$btnSetup.Size = New-Object System.Drawing.Size(200, 60)
$btnSetup.Location = New-Object System.Drawing.Point(50, 280)
$btnSetup.BackColor = [System.Drawing.Color]::FromArgb(255, 152, 0)
$btnSetup.ForeColor = [System.Drawing.Color]::White
$btnSetup.FlatStyle = "Flat"
$btnSetup.Add_Click({
    try {
        if (Test-Path "Setup-System.ps1") {
            Start-Process "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -File `"Setup-System.ps1`" -FullSetup" -Verb RunAs
        } else {
            [System.Windows.Forms.MessageBox]::Show("ملف الإعداد غير موجود!`nيرجى التأكد من وجود ملف Setup-System.ps1", "خطأ", "OK", "Error")
        }
    } catch {
        [System.Windows.Forms.MessageBox]::Show("خطأ في إعداد النظام:`n$($_.Exception.Message)", "خطأ", "OK", "Error")
    }
})
$form.Controls.Add($btnSetup)

# زر النسخ الاحتياطي
$btnBackup = New-Object System.Windows.Forms.Button
$btnBackup.Text = "💾 نسخة احتياطية`nBackup"
$btnBackup.Font = New-Object System.Drawing.Font("Tahoma", 11, [System.Drawing.FontStyle]::Bold)
$btnBackup.Size = New-Object System.Drawing.Size(200, 60)
$btnBackup.Location = New-Object System.Drawing.Point(270, 280)
$btnBackup.BackColor = [System.Drawing.Color]::FromArgb(156, 39, 176)
$btnBackup.ForeColor = [System.Drawing.Color]::White
$btnBackup.FlatStyle = "Flat"
$btnBackup.Add_Click({
    try {
        if (Test-Path "نظام_إدارة_المؤسسات.accdb") {
            $backupDir = "Backups"
            if (!(Test-Path $backupDir)) { New-Item -ItemType Directory -Path $backupDir | Out-Null }

            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupPath = Join-Path $backupDir "نظام_إدارة_المؤسسات_backup_$timestamp.accdb"

            Copy-Item "نظام_إدارة_المؤسسات.accdb" $backupPath
            [System.Windows.Forms.MessageBox]::Show("تم إنشاء النسخة الاحتياطية بنجاح!`n`nالمسار:`n$backupPath", "نجح الحفظ", "OK", "Information")
        } else {
            [System.Windows.Forms.MessageBox]::Show("لم يتم العثور على ملف قاعدة البيانات!", "خطأ", "OK", "Error")
        }
    } catch {
        [System.Windows.Forms.MessageBox]::Show("خطأ في إنشاء النسخة الاحتياطية:`n$($_.Exception.Message)", "خطأ", "OK", "Error")
    }
})
$form.Controls.Add($btnBackup)

# زر الخروج
$btnExit = New-Object System.Windows.Forms.Button
$btnExit.Text = "❌ خروج - Exit"
$btnExit.Font = New-Object System.Drawing.Font("Tahoma", 10)
$btnExit.Size = New-Object System.Drawing.Size(150, 40)
$btnExit.Location = New-Object System.Drawing.Point(225, 380)
$btnExit.BackColor = [System.Drawing.Color]::FromArgb(108, 117, 125)
$btnExit.ForeColor = [System.Drawing.Color]::White
$btnExit.FlatStyle = "Flat"
$btnExit.Add_Click({ $form.Close() })
$form.Controls.Add($btnExit)

# عرض النموذج
$form.ShowDialog() | Out-Null
'@

    $psPath = "EnterpriseSystemLauncher.ps1"
    $psScript | Out-File -FilePath $psPath -Encoding UTF8
    Write-Status "تم إنشاء ملف PowerShell: $psPath" "SUCCESS"

    return $psPath
}

# تشغيل البرنامج الرئيسي
Main
