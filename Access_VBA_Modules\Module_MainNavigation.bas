' Enterprise Management System - Main Navigation Module
' VBA Module for handling main navigation and system functions
' Module Name: Module_MainNavigation

Option Compare Database
Option Explicit

' Global Variables
Public g_CurrentUser As String
Public g_CurrentUserID As Long
Public g_CurrentUserRole As String
Public g_SystemSettings As Collection

' ============================================================================
' SYSTEM INITIALIZATION FUNCTIONS
' ============================================================================

Public Function InitializeSystem() As Boolean
    ' Initialize the system on startup
    On Error GoTo ErrorHandler
    
    ' Load system settings
    LoadSystemSettings
    
    ' Check database integrity
    If Not CheckDatabaseIntegrity() Then
        MsgBox "Database integrity check failed. Please contact system administrator.", vbCritical, "System Error"
        InitializeSystem = False
        Exit Function
    End If
    
    ' Show login form
    DoCmd.OpenForm "frm_Login", acNormal, , , , acDialog
    
    ' Check if user logged in successfully
    If g_CurrentUserID > 0 Then
        ' Log user login
        LogUserActivity "LOGIN", "User logged in successfully"
        
        ' Show main navigation
        DoCmd.OpenForm "frm_MainNavigation", acNormal
        InitializeSystem = True
    Else
        ' User cancelled login or failed authentication
        InitializeSystem = False
    End If
    
    Exit Function
    
ErrorHandler:
    MsgBox "Error initializing system: " & Err.Description, vbCritical, "System Error"
    InitializeSystem = False
End Function

Public Sub LoadSystemSettings()
    ' Load system settings into global collection
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Set g_SystemSettings = New Collection
    
    Set rs = CurrentDb.OpenRecordset("SELECT SettingKey, SettingValue FROM tbl_SystemSettings")
    
    Do While Not rs.EOF
        g_SystemSettings.Add rs!SettingValue, rs!SettingKey
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error loading system settings: " & Err.Description, vbCritical, "System Error"
    If Not rs Is Nothing Then rs.Close
End Sub

Public Function GetSystemSetting(SettingKey As String, Optional DefaultValue As String = "") As String
    ' Get system setting value
    On Error GoTo ErrorHandler
    
    GetSystemSetting = g_SystemSettings(SettingKey)
    Exit Function
    
ErrorHandler:
    GetSystemSetting = DefaultValue
End Function

' ============================================================================
' USER AUTHENTICATION FUNCTIONS
' ============================================================================

Public Function AuthenticateUser(Username As String, Password As String) As Boolean
    ' Authenticate user credentials
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    Dim HashedPassword As String
    
    ' Hash the provided password (simple hash for demo - use stronger hashing in production)
    HashedPassword = HashPassword(Password)
    
    SQL = "SELECT u.UserID, u.Username, u.FirstName, u.LastName, r.RoleName " & _
          "FROM tbl_Users u INNER JOIN tbl_UserRoles ur ON u.UserID = ur.UserID " & _
          "INNER JOIN tbl_Roles r ON ur.RoleID = r.RoleID " & _
          "WHERE u.Username = '" & Username & "' AND u.Password = '" & HashedPassword & "' " & _
          "AND u.IsActive = True AND r.IsActive = True"
    
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        ' Authentication successful
        g_CurrentUserID = rs!UserID
        g_CurrentUser = rs!Username
        g_CurrentUserRole = rs!RoleName
        
        ' Update last login
        CurrentDb.Execute "UPDATE tbl_Users SET LastLogin = Now() WHERE UserID = " & g_CurrentUserID
        
        AuthenticateUser = True
    Else
        ' Authentication failed
        AuthenticateUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    MsgBox "Error during authentication: " & Err.Description, vbCritical, "Authentication Error"
    AuthenticateUser = False
    If Not rs Is Nothing Then rs.Close
End Function

Public Function HashPassword(Password As String) As String
    ' Simple password hashing (use stronger hashing in production)
    ' This is a basic implementation - consider using bcrypt or similar
    Dim i As Integer
    Dim HashValue As Long
    
    HashValue = 0
    For i = 1 To Len(Password)
        HashValue = HashValue + Asc(Mid(Password, i, 1)) * i
    Next i
    
    HashPassword = CStr(HashValue)
End Function

Public Function HasPermission(ModuleName As String, Action As String) As Boolean
    ' Check if current user has permission for specific action
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    SQL = "SELECT COUNT(*) AS PermissionCount " & _
          "FROM tbl_Users u " & _
          "INNER JOIN tbl_UserRoles ur ON u.UserID = ur.UserID " & _
          "INNER JOIN tbl_RolePermissions rp ON ur.RoleID = rp.RoleID " & _
          "INNER JOIN tbl_Permissions p ON rp.PermissionID = p.PermissionID " & _
          "WHERE u.UserID = " & g_CurrentUserID & " " & _
          "AND p.ModuleName = '" & ModuleName & "' " & _
          "AND p.Action = '" & Action & "'"
    
    Set rs = CurrentDb.OpenRecordset(SQL)
    HasPermission = (rs!PermissionCount > 0)
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    HasPermission = False
    If Not rs Is Nothing Then rs.Close
End Function

' ============================================================================
' NAVIGATION FUNCTIONS
' ============================================================================

Public Sub OpenModule(ModuleName As String, FormName As String, ModuleTitle As String)
    ' Open a specific module with permission check
    On Error GoTo ErrorHandler
    
    ' Check permissions
    If Not HasPermission(ModuleName, "Read") Then
        MsgBox "You don't have permission to access " & ModuleTitle & ".", vbExclamation, "Access Denied"
        Exit Sub
    End If
    
    ' Close any open module forms (except main navigation)
    CloseModuleForms
    
    ' Open the requested form
    DoCmd.OpenForm FormName, acNormal
    
    ' Log activity
    LogUserActivity "MODULE_ACCESS", "Opened " & ModuleTitle
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error opening " & ModuleTitle & ": " & Err.Description, vbCritical, "Navigation Error"
End Sub

Public Sub CloseModuleForms()
    ' Close all module forms except main navigation
    On Error Resume Next
    
    Dim frm As AccessObject
    
    For Each frm In CurrentProject.AllForms
        If frm.IsLoaded And frm.Name <> "frm_MainNavigation" And frm.Name <> "frm_Login" Then
            DoCmd.Close acForm, frm.Name
        End If
    Next frm
End Sub

' ============================================================================
' MODULE OPENING FUNCTIONS
' ============================================================================

Public Sub OpenDashboard()
    OpenModule "Dashboard", "frm_Dashboard", "Dashboard"
End Sub

Public Sub OpenClientManagement()
    OpenModule "Client Management", "frm_Clients", "Client Management"
End Sub

Public Sub OpenPurchaseManagement()
    OpenModule "Purchase Management", "frm_PurchaseOrders", "Purchase Management"
End Sub

Public Sub OpenSalesManagement()
    OpenModule "Sales Management", "frm_SalesOrders", "Sales Management"
End Sub

Public Sub OpenInventoryManagement()
    OpenModule "Inventory Management", "frm_Inventory", "Inventory Management"
End Sub

Public Sub OpenSupplierManagement()
    OpenModule "Supplier Management", "frm_Suppliers", "Supplier Management"
End Sub

Public Sub OpenEmployeeManagement()
    OpenModule "Employee Management", "frm_Employees", "Employee Management"
End Sub

Public Sub OpenAccountingManagement()
    OpenModule "Accounting Management", "frm_Accounts", "Accounting Management"
End Sub

Public Sub OpenInvoiceManagement()
    OpenModule "Invoice Management", "frm_Invoices", "Invoice Management"
End Sub

Public Sub OpenReportingManagement()
    OpenModule "Reporting Management", "frm_Reports", "Reporting Management"
End Sub

Public Sub OpenUserManagement()
    OpenModule "User Management", "frm_Users", "User Management"
End Sub

Public Sub OpenDatabaseManagement()
    OpenModule "Database Management", "frm_DatabaseManagement", "Database Management"
End Sub

' ============================================================================
' UTILITY FUNCTIONS
' ============================================================================

Public Function GenerateNextNumber(TableName As String, FieldName As String, Prefix As String) As String
    ' Generate next sequential number for codes
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    Dim NextNumber As Long
    
    SQL = "SELECT MAX(Val(Mid(" & FieldName & ", " & (Len(Prefix) + 1) & "))) AS MaxNumber " & _
          "FROM " & TableName & " WHERE " & FieldName & " LIKE '" & Prefix & "*'"
    
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If IsNull(rs!MaxNumber) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNumber + 1
    End If
    
    GenerateNextNumber = Prefix & Format(NextNumber, "0000")
    
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GenerateNextNumber = Prefix & "0001"
    If Not rs Is Nothing Then rs.Close
End Function

Public Sub LogUserActivity(ActivityType As String, Description As String)
    ' Log user activity for audit trail
    On Error GoTo ErrorHandler
    
    Dim SQL As String
    
    SQL = "INSERT INTO tbl_AuditLog (TableName, RecordID, Action, NewValues, ChangedBy) " & _
          "VALUES ('USER_ACTIVITY', " & g_CurrentUserID & ", '" & ActivityType & "', '" & Description & "', " & g_CurrentUserID & ")"
    
    CurrentDb.Execute SQL
    
    Exit Sub
    
ErrorHandler:
    ' Silently fail for logging errors
End Sub

Public Function CheckDatabaseIntegrity() As Boolean
    ' Basic database integrity check
    On Error GoTo ErrorHandler
    
    ' Check if essential tables exist
    Dim tdf As DAO.TableDef
    Dim EssentialTables As Variant
    Dim i As Integer
    
    EssentialTables = Array("tbl_Users", "tbl_Roles", "tbl_Clients", "tbl_Products", "tbl_SystemSettings")
    
    For i = 0 To UBound(EssentialTables)
        Set tdf = Nothing
        Set tdf = CurrentDb.TableDefs(EssentialTables(i))
        If tdf Is Nothing Then
            CheckDatabaseIntegrity = False
            Exit Function
        End If
    Next i
    
    CheckDatabaseIntegrity = True
    Exit Function
    
ErrorHandler:
    CheckDatabaseIntegrity = False
End Function

Public Sub LogoutUser()
    ' Logout current user
    On Error GoTo ErrorHandler
    
    ' Log logout activity
    LogUserActivity "LOGOUT", "User logged out"
    
    ' Clear global variables
    g_CurrentUser = ""
    g_CurrentUserID = 0
    g_CurrentUserRole = ""
    
    ' Close all forms except login
    CloseModuleForms
    DoCmd.Close acForm, "frm_MainNavigation"
    
    ' Show login form
    DoCmd.OpenForm "frm_Login", acNormal, , , , acDialog
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error during logout: " & Err.Description, vbCritical, "Logout Error"
End Sub

Public Sub ExitApplication()
    ' Exit the application
    On Error GoTo ErrorHandler
    
    If MsgBox("Are you sure you want to exit the application?", vbYesNo + vbQuestion, "Exit Application") = vbYes Then
        ' Log exit activity
        LogUserActivity "EXIT", "Application closed"
        
        ' Close all forms and exit
        DoCmd.Quit acQuitSaveAll
    End If
    
    Exit Sub
    
ErrorHandler:
    DoCmd.Quit acQuitSaveAll
End Sub
