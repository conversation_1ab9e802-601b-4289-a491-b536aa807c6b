# نظام إدارة المؤسسات - ملف الإعداد التلقائي
# Enterprise Management System - Automatic Setup Script

param(
    [switch]$FullSetup,
    [switch]$DatabaseOnly,
    [switch]$FormsOnly,
    [string]$InstallPath = (Get-Location).Path
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

$SystemName = "نظام إدارة المؤسسات الشامل"
$DatabaseName = "نظام_إدارة_المؤسسات.accdb"

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Show-Welcome {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                    إعداد النظام التلقائي                    ║" -ForegroundColor Magenta
    Write-Host "║              Enterprise Management System Setup             ║" -ForegroundColor Magenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
    Write-Status "بدء عملية الإعداد التلقائي للنظام..."
}

function Test-Prerequisites {
    Write-Status "فحص المتطلبات الأساسية..."
    
    # فحص Microsoft Access
    try {
        $accessApp = New-Object -ComObject Access.Application
        $version = $accessApp.Version
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        Write-Status "Microsoft Access $version مثبت ومتوافق" "SUCCESS"
        return $true
    }
    catch {
        Write-Status "Microsoft Access غير مثبت أو غير متاح" "ERROR"
        Write-Status "يرجى تثبيت Microsoft Access 2016 أو أحدث قبل المتابعة" "WARNING"
        return $false
    }
}

function New-SystemDirectories {
    Write-Status "إنشاء هيكل المجلدات..."
    
    $directories = @(
        "Backups",
        "Reports", 
        "Logs",
        "Temp",
        "Assets",
        "Documentation",
        "Scripts",
        "Forms",
        "Reports_Design"
    )
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $InstallPath $dir
        if (!(Test-Path $fullPath)) {
            New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
            Write-Status "تم إنشاء مجلد: $dir" "SUCCESS"
        } else {
            Write-Status "مجلد موجود مسبقاً: $dir" "INFO"
        }
    }
}

function New-DatabaseStructure {
    Write-Status "إنشاء قاعدة البيانات وهيكل الجداول..."
    
    $databasePath = Join-Path $InstallPath $DatabaseName
    
    try {
        # إنشاء قاعدة البيانات
        $accessApp = New-Object -ComObject Access.Application
        $accessApp.NewCurrentDatabase($databasePath)
        Write-Status "تم إنشاء ملف قاعدة البيانات" "SUCCESS"
        
        # قراءة وتنفيذ سكريبت الجداول
        $scriptPath = Join-Path $InstallPath "Complete_Database_Script.sql"
        if (Test-Path $scriptPath) {
            Write-Status "تنفيذ سكريبت إنشاء الجداول..."
            
            $sqlContent = Get-Content $scriptPath -Raw -Encoding UTF8
            $sqlCommands = $sqlContent -split ";\s*(?=CREATE|INSERT|ALTER)" | Where-Object { $_.Trim() -ne "" }
            
            $successCount = 0
            $errorCount = 0
            
            foreach ($command in $sqlCommands) {
                $command = $command.Trim()
                if ($command -ne "" -and $command -ne ";") {
                    try {
                        $accessApp.DoCmd.RunSQL($command + ";")
                        $successCount++
                    }
                    catch {
                        $errorCount++
                        Write-Status "خطأ في تنفيذ الأمر: $($_.Exception.Message)" "WARNING"
                    }
                }
            }
            
            Write-Status "تم تنفيذ $successCount أمر بنجاح، $errorCount خطأ" "INFO"
        }
        else {
            Write-Status "لم يتم العثور على ملف السكريبت: $scriptPath" "WARNING"
            Write-Status "سيتم إنشاء جداول أساسية فقط..." "INFO"
            
            # إنشاء جداول أساسية
            $basicTables = @"
CREATE TABLE tbl_SystemInfo (
    ID AUTOINCREMENT PRIMARY KEY,
    SystemName TEXT(100),
    Version TEXT(20),
    InstallDate DATETIME DEFAULT Now(),
    LastUpdate DATETIME
);

CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now()
);

CREATE TABLE tbl_Clients (
    ClientID AUTOINCREMENT PRIMARY KEY,
    ClientCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now()
);
"@
            
            $basicCommands = $basicTables -split ";" | Where-Object { $_.Trim() -ne "" }
            foreach ($command in $basicCommands) {
                try {
                    $accessApp.DoCmd.RunSQL($command.Trim() + ";")
                }
                catch {
                    Write-Status "خطأ في إنشاء جدول أساسي: $($_.Exception.Message)" "WARNING"
                }
            }
        }
        
        # إدخال بيانات النظام الأساسية
        $systemInfoSql = "INSERT INTO tbl_SystemInfo (SystemName, Version, InstallDate) VALUES ('$SystemName', '1.0', Now());"
        try {
            $accessApp.DoCmd.RunSQL($systemInfoSql)
            Write-Status "تم إدخال معلومات النظام الأساسية" "SUCCESS"
        }
        catch {
            Write-Status "خطأ في إدخال معلومات النظام: $($_.Exception.Message)" "WARNING"
        }
        
        # إدخال المستخدم الافتراضي
        $defaultUserSql = "INSERT INTO tbl_Users (Username, Password, FirstName, LastName, IsActive) VALUES ('admin', '12345', 'مدير', 'النظام', True);"
        try {
            $accessApp.DoCmd.RunSQL($defaultUserSql)
            Write-Status "تم إنشاء المستخدم الافتراضي (admin/12345)" "SUCCESS"
        }
        catch {
            Write-Status "خطأ في إنشاء المستخدم الافتراضي: $($_.Exception.Message)" "WARNING"
        }
        
        $accessApp.CloseCurrentDatabase()
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        
        Write-Status "تم إنشاء قاعدة البيانات بنجاح" "SUCCESS"
        return $true
    }
    catch {
        Write-Status "فشل في إنشاء قاعدة البيانات: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-ConfigurationFiles {
    Write-Status "إنشاء ملفات الإعداد..."
    
    # ملف إعدادات النظام
    $configContent = @"
# إعدادات نظام إدارة المؤسسات
# Enterprise Management System Configuration

[System]
Name=$SystemName
Version=1.0
InstallDate=$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
DatabasePath=$DatabaseName

[Security]
DefaultUsername=admin
PasswordPolicy=Enabled
SessionTimeout=30

[Backup]
AutoBackup=Enabled
BackupInterval=7
BackupPath=Backups\
MaxBackupFiles=10

[Logging]
LogLevel=INFO
LogPath=Logs\
MaxLogSize=10MB

[UI]
Language=Arabic
Theme=Professional
ShowWelcome=True
"@
    
    $configPath = Join-Path $InstallPath "system.config"
    $configContent | Out-File -FilePath $configPath -Encoding UTF8
    Write-Status "تم إنشاء ملف الإعدادات: system.config" "SUCCESS"
    
    # ملف معلومات التثبيت
    $installInfo = @"
نظام إدارة المؤسسات الشامل
Enterprise Management System

تاريخ التثبيت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
مسار التثبيت: $InstallPath
إصدار النظام: 1.0

بيانات تسجيل الدخول الافتراضية:
اسم المستخدم: admin
كلمة المرور: 12345

ملاحظات مهمة:
- تأكد من تغيير كلمة المرور بعد أول دخول
- يتم حفظ النسخ الاحتياطية في مجلد Backups
- التقارير تُحفظ في مجلد Reports
- سجلات النظام في مجلد Logs

للدعم الفني:
البريد الإلكتروني: <EMAIL>
الهاتف: +966-XX-XXXXXXX
"@
    
    $infoPath = Join-Path $InstallPath "معلومات_التثبيت.txt"
    $installInfo | Out-File -FilePath $infoPath -Encoding UTF8
    Write-Status "تم إنشاء ملف معلومات التثبيت" "SUCCESS"
}

function New-DesktopShortcut {
    Write-Status "إنشاء اختصار على سطح المكتب..."
    
    try {
        $WshShell = New-Object -ComObject WScript.Shell
        $desktopPath = $WshShell.SpecialFolders("Desktop")
        $shortcutPath = Join-Path $desktopPath "نظام إدارة المؤسسات.lnk"
        
        $shortcut = $WshShell.CreateShortcut($shortcutPath)
        $shortcut.TargetPath = Join-Path $InstallPath "تشغيل_سريع.cmd"
        $shortcut.WorkingDirectory = $InstallPath
        $shortcut.Description = "نظام إدارة المؤسسات الشامل"
        $shortcut.IconLocation = "shell32.dll,21"
        $shortcut.Save()
        
        Write-Status "تم إنشاء اختصار على سطح المكتب" "SUCCESS"
    }
    catch {
        Write-Status "فشل في إنشاء اختصار سطح المكتب: $($_.Exception.Message)" "WARNING"
    }
}

function Test-Installation {
    Write-Status "اختبار التثبيت..."
    
    $databasePath = Join-Path $InstallPath $DatabaseName
    
    if (Test-Path $databasePath) {
        try {
            $accessApp = New-Object -ComObject Access.Application
            $accessApp.OpenCurrentDatabase($databasePath)
            
            # اختبار الاتصال بقاعدة البيانات
            $tableCount = $accessApp.CurrentDb.TableDefs.Count
            Write-Status "قاعدة البيانات تحتوي على $tableCount جدول" "INFO"
            
            # اختبار المستخدم الافتراضي
            try {
                $userTest = $accessApp.DLookup("Username", "tbl_Users", "Username='admin'")
                if ($userTest -eq "admin") {
                    Write-Status "المستخدم الافتراضي موجود ومتاح" "SUCCESS"
                }
            }
            catch {
                Write-Status "تحذير: لم يتم العثور على المستخدم الافتراضي" "WARNING"
            }
            
            $accessApp.CloseCurrentDatabase()
            $accessApp.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
            
            Write-Status "اختبار قاعدة البيانات مكتمل بنجاح" "SUCCESS"
            return $true
        }
        catch {
            Write-Status "خطأ في اختبار قاعدة البيانات: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    else {
        Write-Status "ملف قاعدة البيانات غير موجود" "ERROR"
        return $false
    }
}

function Show-CompletionSummary {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                    تم إكمال الإعداد بنجاح!                   ║" -ForegroundColor Green
    Write-Host "║                 Setup Completed Successfully!              ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    
    Write-Status "ملخص التثبيت:" "SUCCESS"
    Write-Status "• تم إنشاء قاعدة البيانات: $DatabaseName" "INFO"
    Write-Status "• تم إنشاء هيكل المجلدات" "INFO"
    Write-Status "• تم إنشاء ملفات الإعداد" "INFO"
    Write-Status "• تم إنشاء اختصار سطح المكتب" "INFO"
    
    Write-Host ""
    Write-Host "🔑 بيانات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "   اسم المستخدم: admin" -ForegroundColor White
    Write-Host "   كلمة المرور: 12345" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🚀 لتشغيل النظام:" -ForegroundColor Cyan
    Write-Host "   • انقر نقراً مزدوجاً على اختصار سطح المكتب" -ForegroundColor White
    Write-Host "   • أو شغل ملف 'تشغيل_سريع.cmd'" -ForegroundColor White
    Write-Host ""
    
    Write-Host "⚠️  ملاحظات مهمة:" -ForegroundColor Yellow
    Write-Host "   • تأكد من تغيير كلمة المرور بعد أول دخول" -ForegroundColor White
    Write-Host "   • راجع ملف 'معلومات_التثبيت.txt' للتفاصيل" -ForegroundColor White
    Write-Host ""
}

# البرنامج الرئيسي
function Main {
    Show-Welcome
    
    if (!(Test-Prerequisites)) {
        Write-Status "فشل في فحص المتطلبات الأساسية" "ERROR"
        Read-Host "اضغط Enter للخروج"
        return
    }
    
    New-SystemDirectories
    
    if ($DatabaseOnly -or $FullSetup -or (!$FormsOnly)) {
        if (!(New-DatabaseStructure)) {
            Write-Status "فشل في إنشاء قاعدة البيانات" "ERROR"
            Read-Host "اضغط Enter للخروج"
            return
        }
    }
    
    New-ConfigurationFiles
    New-DesktopShortcut
    
    if (Test-Installation) {
        Show-CompletionSummary
    }
    else {
        Write-Status "فشل في اختبار التثبيت" "ERROR"
    }
    
    Read-Host "اضغط Enter للخروج"
}

# تشغيل البرنامج الرئيسي
Main
