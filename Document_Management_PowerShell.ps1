# نظام إدارة المستندات المتكامل - Integrated Document Management System
# Enterprise Management System - Document Management Module
# الإصدار 1.0 - Version 1.0

param(
    [switch]$SetupDocumentSystem,
    [switch]$CreateFolders,
    [switch]$BackupDocuments,
    [switch]$CleanupOldFiles,
    [string]$DocumentPath = "C:\EnterpriseSystem\Documents"
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات النظام
$DocumentBasePath = $DocumentPath
$BackupPath = "$DocumentPath\Backups"
$TempPath = "$DocumentPath\Temp"
$LogPath = "$DocumentPath\Logs"

# إعدادات النظام
$MaxFileSize = 10485760  # 10MB
$AllowedExtensions = @('pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'zip', 'rar')
$RetentionDays = 2555    # 7 years

function Write-DocumentLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    if (!(Test-Path $LogPath)) {
        New-Item -ItemType Directory -Path $LogPath -Force | Out-Null
    }
    
    # كتابة السجل
    $logFile = Join-Path $LogPath "DocumentSystem_$(Get-Date -Format 'yyyyMM').log"
    Add-Content -Path $logFile -Value $logEntry -Encoding UTF8
    
    # عرض الرسالة
    switch ($Level) {
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "ERROR"   { Write-Host $logEntry -ForegroundColor Red }
        "WARNING" { Write-Host $logEntry -ForegroundColor Yellow }
        "INFO"    { Write-Host $logEntry -ForegroundColor Cyan }
        default   { Write-Host $logEntry }
    }
}

function Show-DocumentHeader {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                نظام إدارة المستندات المتكامل                ║" -ForegroundColor Magenta
    Write-Host "║            Integrated Document Management System           ║" -ForegroundColor Magenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

# دالة إعداد نظام المستندات
function Initialize-DocumentSystem {
    Write-DocumentLog "بدء إعداد نظام إدارة المستندات..." "INFO"
    
    try {
        # إنشاء المجلدات الأساسية
        $folders = @(
            $DocumentBasePath,
            "$DocumentBasePath\Clients",
            "$DocumentBasePath\Sales", 
            "$DocumentBasePath\Purchases",
            "$DocumentBasePath\Inventory",
            "$DocumentBasePath\Suppliers",
            "$DocumentBasePath\Employees",
            "$DocumentBasePath\Accounts",
            "$DocumentBasePath\Invoices",
            "$DocumentBasePath\Reports",
            "$DocumentBasePath\System",
            "$DocumentBasePath\Versions",
            "$DocumentBasePath\Templates",
            $BackupPath,
            $TempPath,
            $LogPath
        )
        
        foreach ($folder in $folders) {
            if (!(Test-Path $folder)) {
                New-Item -ItemType Directory -Path $folder -Force | Out-Null
                Write-DocumentLog "تم إنشاء المجلد: $folder" "SUCCESS"
            }
        }
        
        # إنشاء ملف الإعدادات
        $configFile = Join-Path $DocumentBasePath "config.json"
        $config = @{
            MaxFileSize = $MaxFileSize
            AllowedExtensions = $AllowedExtensions
            RetentionDays = $RetentionDays
            BackupEnabled = $true
            EncryptionEnabled = $true
            LoggingEnabled = $true
            CreatedDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        } | ConvertTo-Json -Depth 3
        
        $config | Out-File -FilePath $configFile -Encoding UTF8
        Write-DocumentLog "تم إنشاء ملف الإعدادات: $configFile" "SUCCESS"
        
        # إنشاء ملف README
        $readmeContent = @"
# نظام إدارة المستندات - Document Management System

## معلومات النظام - System Information
- تاريخ الإنشاء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
- المسار الأساسي: $DocumentBasePath
- الحد الأقصى لحجم الملف: $([math]::Round($MaxFileSize / 1MB, 2)) MB
- الامتدادات المسموحة: $($AllowedExtensions -join ', ')

## هيكل المجلدات - Folder Structure
- Clients: مستندات العملاء
- Sales: مستندات المبيعات
- Purchases: مستندات المشتريات
- Inventory: مستندات المخزون
- Suppliers: مستندات الموردين
- Employees: مستندات الموظفين
- Accounts: مستندات الحسابات
- Invoices: مستندات الفواتير
- Reports: مستندات التقارير
- System: مستندات النظام
- Versions: إصدارات المستندات
- Templates: قوالب المستندات
- Backups: النسخ الاحتياطية
- Temp: الملفات المؤقتة
- Logs: سجلات النظام

## ملاحظات مهمة - Important Notes
- لا تحذف أو تعدل هذه المجلدات يدوياً
- استخدم نظام إدارة المستندات للتعامل مع الملفات
- يتم إنشاء نسخ احتياطية تلقائية كل 7 أيام
- الملفات القديمة يتم أرشفتها تلقائياً بعد 7 سنوات
"@
        
        $readmeFile = Join-Path $DocumentBasePath "README.txt"
        $readmeContent | Out-File -FilePath $readmeFile -Encoding UTF8
        Write-DocumentLog "تم إنشاء ملف README: $readmeFile" "SUCCESS"
        
        Write-DocumentLog "تم إعداد نظام إدارة المستندات بنجاح!" "SUCCESS"
        return $true
        
    } catch {
        Write-DocumentLog "خطأ في إعداد نظام المستندات: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة النسخ الاحتياطي للمستندات
function Backup-Documents {
    param([string]$BackupType = "Full")
    
    Write-DocumentLog "بدء عملية النسخ الاحتياطي ($BackupType)..." "INFO"
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFolder = Join-Path $BackupPath "Backup_$timestamp"
        
        # إنشاء مجلد النسخة الاحتياطية
        New-Item -ItemType Directory -Path $backupFolder -Force | Out-Null
        
        # تحديد المجلدات للنسخ الاحتياطي
        $foldersToBackup = @(
            "Clients", "Sales", "Purchases", "Inventory", "Suppliers",
            "Employees", "Accounts", "Invoices", "Reports", "System", "Templates"
        )
        
        $totalFiles = 0
        $totalSize = 0
        
        foreach ($folder in $foldersToBackup) {
            $sourcePath = Join-Path $DocumentBasePath $folder
            $destPath = Join-Path $backupFolder $folder
            
            if (Test-Path $sourcePath) {
                # نسخ المجلد بالكامل
                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                
                # حساب الإحصائيات
                $files = Get-ChildItem -Path $destPath -Recurse -File
                $totalFiles += $files.Count
                $totalSize += ($files | Measure-Object -Property Length -Sum).Sum
                
                Write-DocumentLog "تم نسخ مجلد: $folder ($($files.Count) ملف)" "SUCCESS"
            }
        }
        
        # إنشاء ملف معلومات النسخة الاحتياطية
        $backupInfo = @{
            BackupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            BackupType = $BackupType
            TotalFiles = $totalFiles
            TotalSize = $totalSize
            BackupPath = $backupFolder
        } | ConvertTo-Json -Depth 2
        
        $infoFile = Join-Path $backupFolder "backup_info.json"
        $backupInfo | Out-File -FilePath $infoFile -Encoding UTF8
        
        # ضغط النسخة الاحتياطية
        $zipFile = "$backupFolder.zip"
        Compress-Archive -Path $backupFolder -DestinationPath $zipFile -Force
        
        # حذف المجلد غير المضغوط
        Remove-Item -Path $backupFolder -Recurse -Force
        
        $zipSize = (Get-Item $zipFile).Length
        Write-DocumentLog "تم إنشاء النسخة الاحتياطية: $zipFile" "SUCCESS"
        Write-DocumentLog "إجمالي الملفات: $totalFiles | الحجم المضغوط: $([math]::Round($zipSize / 1MB, 2)) MB" "INFO"
        
        return $true
        
    } catch {
        Write-DocumentLog "خطأ في النسخ الاحتياطي: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة تنظيف الملفات القديمة
function Clean-OldDocuments {
    Write-DocumentLog "بدء عملية تنظيف الملفات القديمة..." "INFO"
    
    try {
        $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
        $archivedCount = 0
        $deletedCount = 0
        
        # البحث عن الملفات القديمة
        $oldFiles = Get-ChildItem -Path $DocumentBasePath -Recurse -File | 
                   Where-Object { $_.LastWriteTime -lt $cutoffDate -and $_.Directory.Name -ne "Backups" }
        
        foreach ($file in $oldFiles) {
            try {
                # نقل الملفات القديمة إلى مجلد الأرشيف
                $archivePath = Join-Path $DocumentBasePath "Archive"
                if (!(Test-Path $archivePath)) {
                    New-Item -ItemType Directory -Path $archivePath -Force | Out-Null
                }
                
                $relativePath = $file.FullName.Replace($DocumentBasePath, "").TrimStart("\")
                $archiveFile = Join-Path $archivePath $relativePath
                $archiveDir = Split-Path $archiveFile -Parent
                
                if (!(Test-Path $archiveDir)) {
                    New-Item -ItemType Directory -Path $archiveDir -Force | Out-Null
                }
                
                Move-Item -Path $file.FullName -Destination $archiveFile -Force
                $archivedCount++
                
            } catch {
                Write-DocumentLog "خطأ في أرشفة الملف $($file.Name): $($_.Exception.Message)" "WARNING"
            }
        }
        
        # تنظيف الملفات المؤقتة
        $tempFiles = Get-ChildItem -Path $TempPath -File -ErrorAction SilentlyContinue
        foreach ($tempFile in $tempFiles) {
            try {
                Remove-Item -Path $tempFile.FullName -Force
                $deletedCount++
            } catch {
                Write-DocumentLog "خطأ في حذف الملف المؤقت $($tempFile.Name): $($_.Exception.Message)" "WARNING"
            }
        }
        
        Write-DocumentLog "تم أرشفة $archivedCount ملف وحذف $deletedCount ملف مؤقت" "SUCCESS"
        return $true
        
    } catch {
        Write-DocumentLog "خطأ في تنظيف الملفات القديمة: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة فحص سلامة النظام
function Test-DocumentSystemHealth {
    Write-DocumentLog "بدء فحص سلامة نظام المستندات..." "INFO"
    
    $healthReport = @{
        SystemStatus = "Healthy"
        Issues = @()
        Recommendations = @()
        Statistics = @{}
    }
    
    try {
        # فحص وجود المجلدات الأساسية
        $requiredFolders = @(
            $DocumentBasePath, "$DocumentBasePath\Clients", "$DocumentBasePath\Sales",
            "$DocumentBasePath\Purchases", "$DocumentBasePath\Inventory", "$DocumentBasePath\Suppliers",
            "$DocumentBasePath\Employees", "$DocumentBasePath\Accounts", "$DocumentBasePath\Invoices",
            "$DocumentBasePath\Reports", "$DocumentBasePath\System", $BackupPath, $TempPath, $LogPath
        )
        
        foreach ($folder in $requiredFolders) {
            if (!(Test-Path $folder)) {
                $healthReport.Issues += "مجلد مفقود: $folder"
                $healthReport.SystemStatus = "Warning"
            }
        }
        
        # حساب إحصائيات النظام
        $allFiles = Get-ChildItem -Path $DocumentBasePath -Recurse -File -ErrorAction SilentlyContinue
        $totalFiles = $allFiles.Count
        $totalSize = ($allFiles | Measure-Object -Property Length -Sum).Sum
        
        $healthReport.Statistics = @{
            TotalFiles = $totalFiles
            TotalSize = $totalSize
            TotalSizeMB = [math]::Round($totalSize / 1MB, 2)
            LastBackup = (Get-ChildItem -Path $BackupPath -Filter "*.zip" -ErrorAction SilentlyContinue | 
                         Sort-Object LastWriteTime -Descending | Select-Object -First 1).LastWriteTime
        }
        
        # فحص النسخ الاحتياطية
        $lastBackup = $healthReport.Statistics.LastBackup
        if ($lastBackup -and (Get-Date) - $lastBackup -gt (New-TimeSpan -Days 7)) {
            $healthReport.Issues += "آخر نسخة احتياطية قديمة (أكثر من 7 أيام)"
            $healthReport.Recommendations += "قم بإنشاء نسخة احتياطية جديدة"
        }
        
        # فحص مساحة القرص
        $drive = (Get-Item $DocumentBasePath).PSDrive
        $freeSpaceGB = [math]::Round($drive.Free / 1GB, 2)
        
        if ($freeSpaceGB -lt 1) {
            $healthReport.Issues += "مساحة القرص منخفضة: $freeSpaceGB GB"
            $healthReport.SystemStatus = "Critical"
            $healthReport.Recommendations += "قم بتنظيف الملفات القديمة أو زيادة مساحة القرص"
        }
        
        # عرض التقرير
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
        Write-Host "║                    تقرير سلامة النظام                       ║" -ForegroundColor Cyan
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "حالة النظام: " -NoNewline
        switch ($healthReport.SystemStatus) {
            "Healthy" { Write-Host $healthReport.SystemStatus -ForegroundColor Green }
            "Warning" { Write-Host $healthReport.SystemStatus -ForegroundColor Yellow }
            "Critical" { Write-Host $healthReport.SystemStatus -ForegroundColor Red }
        }
        
        Write-Host ""
        Write-Host "📊 الإحصائيات:" -ForegroundColor Yellow
        Write-Host "   إجمالي الملفات: $($healthReport.Statistics.TotalFiles)" -ForegroundColor White
        Write-Host "   الحجم الإجمالي: $($healthReport.Statistics.TotalSizeMB) MB" -ForegroundColor White
        Write-Host "   آخر نسخة احتياطية: $($healthReport.Statistics.LastBackup)" -ForegroundColor White
        Write-Host "   مساحة القرص المتاحة: $freeSpaceGB GB" -ForegroundColor White
        
        if ($healthReport.Issues.Count -gt 0) {
            Write-Host ""
            Write-Host "⚠️  المشاكل المكتشفة:" -ForegroundColor Red
            foreach ($issue in $healthReport.Issues) {
                Write-Host "   • $issue" -ForegroundColor Red
            }
        }
        
        if ($healthReport.Recommendations.Count -gt 0) {
            Write-Host ""
            Write-Host "💡 التوصيات:" -ForegroundColor Cyan
            foreach ($recommendation in $healthReport.Recommendations) {
                Write-Host "   • $recommendation" -ForegroundColor Cyan
            }
        }
        
        Write-DocumentLog "تم إكمال فحص سلامة النظام" "SUCCESS"
        return $healthReport
        
    } catch {
        Write-DocumentLog "خطأ في فحص سلامة النظام: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة إنشاء تقرير استخدام المستندات
function New-DocumentUsageReport {
    Write-DocumentLog "إنشاء تقرير استخدام المستندات..." "INFO"
    
    try {
        $reportPath = Join-Path $DocumentBasePath "Reports\Usage_Report_$(Get-Date -Format 'yyyyMMdd').html"
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        $reportsDir = Split-Path $reportPath -Parent
        if (!(Test-Path $reportsDir)) {
            New-Item -ItemType Directory -Path $reportsDir -Force | Out-Null
        }
        
        # جمع البيانات
        $modules = @("Clients", "Sales", "Purchases", "Inventory", "Suppliers", "Employees", "Accounts", "Invoices", "Reports", "System")
        $moduleStats = @()
        
        foreach ($module in $modules) {
            $modulePath = Join-Path $DocumentBasePath $module
            if (Test-Path $modulePath) {
                $files = Get-ChildItem -Path $modulePath -Recurse -File
                $moduleStats += [PSCustomObject]@{
                    Module = $module
                    FileCount = $files.Count
                    TotalSize = ($files | Measure-Object -Property Length -Sum).Sum
                    TotalSizeMB = [math]::Round(($files | Measure-Object -Property Length -Sum).Sum / 1MB, 2)
                    LastModified = ($files | Sort-Object LastWriteTime -Descending | Select-Object -First 1).LastWriteTime
                }
            }
        }
        
        # إنشاء HTML للتقرير
        $html = @"
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير استخدام المستندات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .stats-container { display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; min-width: 200px; }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #eee; }
        th { background-color: #667eea; color: white; }
        tr:hover { background-color: #f8f9ff; }
        .footer { text-align: center; margin-top: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>تقرير استخدام المستندات</h1>
        <p>تاريخ التقرير: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
    </div>
    
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number">$($moduleStats | Measure-Object -Property FileCount -Sum | Select-Object -ExpandProperty Sum)</div>
            <div class="stat-label">إجمالي الملفات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$([math]::Round(($moduleStats | Measure-Object -Property TotalSize -Sum | Select-Object -ExpandProperty Sum) / 1MB, 2))</div>
            <div class="stat-label">الحجم الإجمالي (MB)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$($modules.Count)</div>
            <div class="stat-label">عدد الوحدات</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>الوحدة</th>
                <th>عدد الملفات</th>
                <th>الحجم (MB)</th>
                <th>آخر تعديل</th>
            </tr>
        </thead>
        <tbody>
"@
        
        foreach ($stat in $moduleStats | Sort-Object FileCount -Descending) {
            $html += @"
            <tr>
                <td>$($stat.Module)</td>
                <td>$($stat.FileCount)</td>
                <td>$($stat.TotalSizeMB)</td>
                <td>$($stat.LastModified)</td>
            </tr>
"@
        }
        
        $html += @"
        </tbody>
    </table>
    
    <div class="footer">
        <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة المستندات</p>
        <p>Enterprise Management System - Document Management Module</p>
    </div>
</body>
</html>
"@
        
        # حفظ التقرير
        $html | Out-File -FilePath $reportPath -Encoding UTF8
        Write-DocumentLog "تم إنشاء تقرير الاستخدام: $reportPath" "SUCCESS"
        
        # فتح التقرير في المتصفح
        Start-Process $reportPath
        
        return $reportPath
        
    } catch {
        Write-DocumentLog "خطأ في إنشاء تقرير الاستخدام: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# القائمة التفاعلية الرئيسية
function Show-DocumentMenu {
    do {
        Show-DocumentHeader

        Write-Host "🗂️  خيارات إدارة المستندات:" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "[1] ⚙️  إعداد نظام المستندات" -ForegroundColor Green
        Write-Host "[2] 📁 إنشاء هيكل المجلدات" -ForegroundColor Green
        Write-Host "[3] 💾 إنشاء نسخة احتياطية" -ForegroundColor Yellow
        Write-Host "[4] 🧹 تنظيف الملفات القديمة" -ForegroundColor Yellow
        Write-Host "[5] 🔍 فحص سلامة النظام" -ForegroundColor Cyan
        Write-Host "[6] 📊 تقرير استخدام المستندات" -ForegroundColor Cyan
        Write-Host "[7] 📂 فتح مجلد المستندات" -ForegroundColor White
        Write-Host "[8] 📋 عرض إعدادات النظام" -ForegroundColor White
        Write-Host "[9] 🔧 أدوات متقدمة" -ForegroundColor Magenta
        Write-Host "[0] ❌ خروج" -ForegroundColor Red
        Write-Host ""

        $choice = Read-Host "اختر رقماً (0-9)"

        switch ($choice) {
            "1" {
                Write-Host ""
                Write-Host "⚙️  إعداد نظام المستندات..." -ForegroundColor Green

                if (Initialize-DocumentSystem) {
                    Write-Host ""
                    Write-Host "✅ تم إعداد نظام المستندات بنجاح!" -ForegroundColor Green
                    Write-Host "📁 المسار الأساسي: $DocumentBasePath" -ForegroundColor Cyan
                    Write-Host "📊 تم إنشاء جميع المجلدات والملفات المطلوبة" -ForegroundColor Cyan
                } else {
                    Write-Host ""
                    Write-Host "❌ فشل في إعداد نظام المستندات!" -ForegroundColor Red
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "2" {
                Write-Host ""
                Write-Host "📁 إنشاء هيكل المجلدات..." -ForegroundColor Green

                $additionalFolders = @(
                    "$DocumentBasePath\Clients\Contracts",
                    "$DocumentBasePath\Clients\Correspondence",
                    "$DocumentBasePath\Clients\Complaints",
                    "$DocumentBasePath\Sales\Quotations",
                    "$DocumentBasePath\Sales\Orders",
                    "$DocumentBasePath\Purchases\PurchaseOrders",
                    "$DocumentBasePath\Purchases\SupplierInvoices",
                    "$DocumentBasePath\Inventory\StockReports",
                    "$DocumentBasePath\Inventory\Certificates",
                    "$DocumentBasePath\Employees\CVs",
                    "$DocumentBasePath\Employees\Certificates",
                    "$DocumentBasePath\Employees\Contracts"
                )

                $createdCount = 0
                foreach ($folder in $additionalFolders) {
                    if (!(Test-Path $folder)) {
                        New-Item -ItemType Directory -Path $folder -Force | Out-Null
                        Write-Host "✅ تم إنشاء: $($folder.Split('\')[-2])\$($folder.Split('\')[-1])" -ForegroundColor Green
                        $createdCount++
                    }
                }

                if ($createdCount -gt 0) {
                    Write-Host ""
                    Write-Host "✅ تم إنشاء $createdCount مجلد فرعي جديد!" -ForegroundColor Green
                } else {
                    Write-Host ""
                    Write-Host "ℹ️  جميع المجلدات موجودة بالفعل" -ForegroundColor Cyan
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "3" {
                Write-Host ""
                Write-Host "💾 إنشاء نسخة احتياطية..." -ForegroundColor Yellow
                Write-Host ""
                Write-Host "اختر نوع النسخة الاحتياطية:" -ForegroundColor Cyan
                Write-Host "[1] نسخة كاملة (Full Backup)" -ForegroundColor White
                Write-Host "[2] نسخة تزايدية (Incremental Backup)" -ForegroundColor White

                $backupChoice = Read-Host "اختر نوع النسخة (1-2)"
                $backupType = if ($backupChoice -eq "2") { "Incremental" } else { "Full" }

                Write-Host ""
                Write-Host "🔄 جاري إنشاء النسخة الاحتياطية..." -ForegroundColor Yellow

                if (Backup-Documents -BackupType $backupType) {
                    Write-Host ""
                    Write-Host "✅ تم إنشاء النسخة الاحتياطية بنجاح!" -ForegroundColor Green
                    Write-Host "📁 مجلد النسخ الاحتياطية: $BackupPath" -ForegroundColor Cyan
                } else {
                    Write-Host ""
                    Write-Host "❌ فشل في إنشاء النسخة الاحتياطية!" -ForegroundColor Red
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "4" {
                Write-Host ""
                Write-Host "🧹 تنظيف الملفات القديمة..." -ForegroundColor Yellow
                Write-Host ""
                Write-Host "⚠️  تحذير: سيتم أرشفة الملفات الأقدم من $RetentionDays يوم" -ForegroundColor Red
                $confirm = Read-Host "هل تريد المتابعة؟ (y/n)"

                if ($confirm -eq "y" -or $confirm -eq "Y") {
                    Write-Host ""
                    Write-Host "🔄 جاري تنظيف الملفات القديمة..." -ForegroundColor Yellow

                    if (Clean-OldDocuments) {
                        Write-Host ""
                        Write-Host "✅ تم تنظيف الملفات القديمة بنجاح!" -ForegroundColor Green
                    } else {
                        Write-Host ""
                        Write-Host "❌ فشل في تنظيف الملفات القديمة!" -ForegroundColor Red
                    }
                } else {
                    Write-Host "تم إلغاء العملية" -ForegroundColor Yellow
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "5" {
                Write-Host ""
                Write-Host "🔍 فحص سلامة النظام..." -ForegroundColor Cyan

                $healthReport = Test-DocumentSystemHealth

                Read-Host "اضغط Enter للمتابعة"
            }
            "6" {
                Write-Host ""
                Write-Host "📊 إنشاء تقرير استخدام المستندات..." -ForegroundColor Cyan

                $reportPath = New-DocumentUsageReport

                if ($reportPath) {
                    Write-Host ""
                    Write-Host "✅ تم إنشاء التقرير بنجاح!" -ForegroundColor Green
                    Write-Host "📄 مسار التقرير: $reportPath" -ForegroundColor Cyan
                    Write-Host "🌐 تم فتح التقرير في المتصفح" -ForegroundColor Cyan
                } else {
                    Write-Host ""
                    Write-Host "❌ فشل في إنشاء التقرير!" -ForegroundColor Red
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "7" {
                Write-Host ""
                Write-Host "📂 فتح مجلد المستندات..." -ForegroundColor White

                if (Test-Path $DocumentBasePath) {
                    Start-Process "explorer.exe" -ArgumentList $DocumentBasePath
                    Write-Host "✅ تم فتح مجلد المستندات في مستكشف الملفات" -ForegroundColor Green
                } else {
                    Write-Host "❌ مجلد المستندات غير موجود!" -ForegroundColor Red
                    Write-Host "يرجى تشغيل إعداد النظام أولاً (الخيار 1)" -ForegroundColor Yellow
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "8" {
                Write-Host ""
                Write-Host "📋 إعدادات نظام المستندات:" -ForegroundColor Cyan
                Write-Host ""

                $configFile = Join-Path $DocumentBasePath "config.json"
                if (Test-Path $configFile) {
                    $config = Get-Content $configFile -Raw | ConvertFrom-Json

                    Write-Host "📁 المسار الأساسي: $DocumentBasePath" -ForegroundColor White
                    Write-Host "📏 الحد الأقصى لحجم الملف: $([math]::Round($config.MaxFileSize / 1MB, 2)) MB" -ForegroundColor White
                    Write-Host "📎 الامتدادات المسموحة: $($config.AllowedExtensions -join ', ')" -ForegroundColor White
                    Write-Host "📅 فترة الاحتفاظ: $($config.RetentionDays) يوم" -ForegroundColor White
                    Write-Host "💾 النسخ الاحتياطي: $($config.BackupEnabled)" -ForegroundColor White
                    Write-Host "🔒 التشفير: $($config.EncryptionEnabled)" -ForegroundColor White
                    Write-Host "📝 السجلات: $($config.LoggingEnabled)" -ForegroundColor White
                    Write-Host "📅 تاريخ الإنشاء: $($config.CreatedDate)" -ForegroundColor White
                } else {
                    Write-Host "❌ ملف الإعدادات غير موجود!" -ForegroundColor Red
                    Write-Host "يرجى تشغيل إعداد النظام أولاً (الخيار 1)" -ForegroundColor Yellow
                }

                Read-Host "اضغط Enter للمتابعة"
            }
            "9" {
                Write-Host ""
                Write-Host "🔧 الأدوات المتقدمة قيد التطوير..." -ForegroundColor Magenta
                Write-Host "ℹ️  ستتوفر قريباً مميزات إضافية مثل:" -ForegroundColor Cyan
                Write-Host "   • البحث المتقدم في المستندات" -ForegroundColor White
                Write-Host "   • تشفير المستندات الحساسة" -ForegroundColor White
                Write-Host "   • تنظيف الملفات المكررة" -ForegroundColor White
                Write-Host "   • مزامنة المستندات" -ForegroundColor White

                Read-Host "اضغط Enter للمتابعة"
            }
            "0" {
                Write-Host ""
                Write-Host "👋 شكراً لاستخدام نظام إدارة المستندات!" -ForegroundColor Green
                Write-Host "🎉 نتمنى لك تجربة ممتازة مع النظام" -ForegroundColor Cyan
                return
            }
            default {
                Write-Host ""
                Write-Host "❌ اختيار غير صحيح. يرجى المحاولة مرة أخرى." -ForegroundColor Red
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

# البرنامج الرئيسي
function Main {
    # معالجة المعاملات
    if ($SetupDocumentSystem) {
        Show-DocumentHeader
        Initialize-DocumentSystem
        return
    }

    if ($CreateFolders) {
        Show-DocumentHeader
        Initialize-DocumentSystem
        return
    }

    if ($BackupDocuments) {
        Show-DocumentHeader
        Backup-Documents
        return
    }

    if ($CleanupOldFiles) {
        Show-DocumentHeader
        Clean-OldDocuments
        return
    }

    # عرض القائمة التفاعلية
    Show-DocumentMenu
}

# تشغيل البرنامج الرئيسي
Main
