# نظام إدارة المؤسسات - تشغيل مبسط
# Enterprise Management System - Simple Launcher

Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
Write-Host "║                نظام إدارة المؤسسات الشامل                    ║" -ForegroundColor Magenta
Write-Host "║              Enterprise Management System                   ║" -ForegroundColor Magenta
Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
Write-Host ""

Write-Host "🔑 بيانات تسجيل الدخول الافتراضية:" -ForegroundColor Yellow
Write-Host "   اسم المستخدم: admin" -ForegroundColor Cyan
Write-Host "   كلمة المرور: 12345" -ForegroundColor Cyan
Write-Host ""

Write-Host "🎯 اختر العملية المطلوبة:" -ForegroundColor Green
Write-Host ""
Write-Host "[1] 🚀 تشغيل سريع للنظام" -ForegroundColor Cyan
Write-Host "[2] 🔧 تشغيل متقدم مع فحص شامل" -ForegroundColor Cyan
Write-Host "[3] ⚙️ إعداد النظام الكامل" -ForegroundColor Cyan
Write-Host "[4] 💾 إنشاء نسخة احتياطية" -ForegroundColor Cyan
Write-Host "[5] 🔒 نظام التشفير والحماية" -ForegroundColor Cyan
Write-Host "[6] 🎫 إدارة التراخيص" -ForegroundColor Cyan
Write-Host "[7] 📋 معلومات النظام" -ForegroundColor Cyan
Write-Host "[0] ❌ خروج" -ForegroundColor Yellow
Write-Host ""

$choice = Read-Host "اختر رقماً (0-7)"

switch ($choice) {
    "1" { 
        Write-Host ""
        Write-Host "🚀 تشغيل سريع للنظام..." -ForegroundColor Green
        Write-Host ""
        
        # التحقق من وجود قاعدة البيانات
        if (Test-Path "نظام_إدارة_المؤسسات.accdb") {
            Write-Host "✅ تم العثور على ملف قاعدة البيانات" -ForegroundColor Green
            Write-Host "🔄 جاري تشغيل Microsoft Access..." -ForegroundColor Cyan
            
            try {
                Start-Process "msaccess.exe" -ArgumentList "`"نظام_إدارة_المؤسسات.accdb`"" -ErrorAction Stop
                Write-Host "✅ تم تشغيل النظام بنجاح!" -ForegroundColor Green
            }
            catch {
                Write-Host "❌ خطأ في تشغيل Microsoft Access" -ForegroundColor Red
                Write-Host "تأكد من تثبيت Microsoft Access على النظام" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "❌ لم يتم العثور على ملف قاعدة البيانات!" -ForegroundColor Red
            Write-Host "يرجى استخدام الخيار [3] لإعداد النظام أولاً" -ForegroundColor Yellow
        }
    }
    "2" { 
        Write-Host ""
        Write-Host "🔧 تشغيل متقدم مع فحص شامل..." -ForegroundColor Green
        
        if (Test-Path "Start-EnterpriseSystem.ps1") {
            & ".\Start-EnterpriseSystem.ps1"
        } else {
            Write-Host "❌ ملف التشغيل المتقدم غير موجود" -ForegroundColor Red
        }
    }
    "3" { 
        Write-Host ""
        Write-Host "⚙️ إعداد النظام الكامل..." -ForegroundColor Green
        
        if (Test-Path "Setup-System.ps1") {
            & ".\Setup-System.ps1" -FullSetup
        } else {
            Write-Host "❌ ملف الإعداد غير موجود" -ForegroundColor Red
        }
    }
    "4" { 
        Write-Host ""
        Write-Host "💾 إنشاء نسخة احتياطية..." -ForegroundColor Green
        
        if (Test-Path "نظام_إدارة_المؤسسات.accdb") {
            $backupDir = "Backups"
            if (!(Test-Path $backupDir)) {
                New-Item -ItemType Directory -Path $backupDir | Out-Null
                Write-Host "📁 تم إنشاء مجلد النسخ الاحتياطية" -ForegroundColor Cyan
            }
            
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupPath = Join-Path $backupDir "نظام_إدارة_المؤسسات_backup_$timestamp.accdb"
            
            try {
                Copy-Item "نظام_إدارة_المؤسسات.accdb" $backupPath
                Write-Host "✅ تم إنشاء النسخة الاحتياطية بنجاح!" -ForegroundColor Green
                Write-Host "📁 المسار: $backupPath" -ForegroundColor Cyan
            }
            catch {
                Write-Host "❌ فشل في إنشاء النسخة الاحتياطية: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ لا يمكن العثور على ملف قاعدة البيانات!" -ForegroundColor Red
        }
    }
    "5" { 
        Write-Host ""
        Write-Host "🔒 تشغيل نظام التشفير والحماية..." -ForegroundColor Green
        
        if (Test-Path "Security_Encryption_System.ps1") {
            & ".\Security_Encryption_System.ps1"
        } else {
            Write-Host "❌ ملف نظام التشفير غير موجود" -ForegroundColor Red
        }
    }
    "6" { 
        Write-Host ""
        Write-Host "🎫 تشغيل نظام إدارة التراخيص..." -ForegroundColor Green
        
        if (Test-Path "License_Management_System.ps1") {
            & ".\License_Management_System.ps1"
        } else {
            Write-Host "❌ ملف إدارة التراخيص غير موجود" -ForegroundColor Red
        }
    }
    "7" { 
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
        Write-Host "║                      معلومات النظام                         ║" -ForegroundColor Cyan
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "📋 تفاصيل النظام:" -ForegroundColor Yellow
        Write-Host "   • الاسم: نظام إدارة المؤسسات الشامل" -ForegroundColor White
        Write-Host "   • الإصدار: 1.0" -ForegroundColor White
        Write-Host "   • تاريخ التشغيل: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
        Write-Host "   • مسار النظام: $(Get-Location)" -ForegroundColor White
        Write-Host ""
        
        Write-Host "📁 ملفات النظام:" -ForegroundColor Yellow
        $systemFiles = @(
            "نظام_إدارة_المؤسسات.accdb",
            "نظام_إدارة_المؤسسات.ps1",
            "Start-EnterpriseSystem.ps1",
            "Setup-System.ps1",
            "Security_Encryption_System.ps1",
            "License_Management_System.ps1"
        )
        
        foreach ($file in $systemFiles) {
            if (Test-Path $file) {
                Write-Host "   ✅ $file: موجود" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $file: غير موجود" -ForegroundColor Red
            }
        }
        
        Write-Host ""
        Write-Host "💻 معلومات النظام:" -ForegroundColor Yellow
        Write-Host "   • نظام التشغيل: $($env:OS)" -ForegroundColor White
        Write-Host "   • اسم الكمبيوتر: $($env:COMPUTERNAME)" -ForegroundColor White
        Write-Host "   • المستخدم الحالي: $($env:USERNAME)" -ForegroundColor White
        Write-Host "   • PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor White
    }
    "0" { 
        Write-Host ""
        Write-Host "👋 شكراً لاستخدام نظام إدارة المؤسسات!" -ForegroundColor Green
        Write-Host "🎉 نتمنى لك تجربة ممتازة مع النظام" -ForegroundColor Cyan
        return 
    }
    default { 
        Write-Host ""
        Write-Host "❌ اختيار غير صحيح. يرجى المحاولة مرة أخرى." -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
