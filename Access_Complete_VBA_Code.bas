' ============================================================================
' نظام إدارة المؤسسات الشامل - كود VBA الكامل
' Enterprise Management System - Complete VBA Code
' ============================================================================

Option Compare Database
Option Explicit

' ============================================================================
' المتغيرات العامة - Global Variables
' ============================================================================

Public g_CurrentUserID As Long
Public g_CurrentUsername As String
Public g_CurrentUserRole As String
Public g_CompanyName As String
Public g_SystemVersion As String

' ============================================================================
' دوال النظام الأساسية - Core System Functions
' ============================================================================

Public Sub InitializeSystem()
    ' تهيئة النظام عند البدء
    On Error GoTo ErrorHandler
    
    g_SystemVersion = "1.0"
    g_CompanyName = "نظام إدارة المؤسسات"
    
    ' تحميل إعدادات النظام
    LoadSystemSettings
    
    ' فتح نموذج تسجيل الدخول
    DoCmd.OpenForm "frm_Login", acDialog
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في تهيئة النظام: " & Err.Description, vbCritical
End Sub

Public Sub LoadSystemSettings()
    ' تحميل إعدادات النظام من قاعدة البيانات
    On Error Resume Next
    
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT SettingKey, SettingValue FROM tbl_SystemSettings")
    
    Do While Not rs.EOF
        Select Case rs!SettingKey
            Case "CompanyName"
                g_CompanyName = Nz(rs!SettingValue, "نظام إدارة المؤسسات")
        End Select
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
End Sub

' ============================================================================
' دوال المصادقة - Authentication Functions
' ============================================================================

Public Function AuthenticateUser(Username As String, Password As String) As Boolean
    ' التحقق من صحة بيانات المستخدم
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    SQL = "SELECT u.UserID, u.Username, u.FirstName, u.LastName, r.RoleName " & _
          "FROM tbl_Users u INNER JOIN tbl_Roles r ON u.RoleID = r.RoleID " & _
          "WHERE u.Username = '" & Username & "' AND u.Password = '" & Password & "' " & _
          "AND u.IsActive = True"
    
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        ' تسجيل دخول ناجح
        g_CurrentUserID = rs!UserID
        g_CurrentUsername = rs!FirstName & " " & rs!LastName
        g_CurrentUserRole = rs!RoleName
        
        ' تحديث آخر دخول
        CurrentDb.Execute "UPDATE tbl_Users SET LastLogin = Now() WHERE UserID = " & g_CurrentUserID
        
        ' تسجيل العملية
        LogActivity "LOGIN", "تسجيل دخول ناجح للمستخدم: " & Username
        
        AuthenticateUser = True
    Else
        AuthenticateUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    AuthenticateUser = False
    If Not rs Is Nothing Then rs.Close
End Function

Public Sub LogActivity(ActivityType As String, Description As String)
    ' تسجيل نشاط المستخدم
    On Error Resume Next
    
    CurrentDb.Execute "INSERT INTO tbl_SystemLog (LogType, Description, CreatedBy) " & _
                     "VALUES ('" & ActivityType & "', '" & Description & "', " & g_CurrentUserID & ")"
End Sub

' ============================================================================
' دوال إدارة العملاء - Client Management Functions
' ============================================================================

Public Function GenerateClientCode() As String
    ' توليد رمز عميل جديد
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim NextNumber As Long
    
    Set rs = CurrentDb.OpenRecordset("SELECT MAX(Val(Mid(ClientCode, 3))) AS MaxNumber FROM tbl_Clients WHERE ClientCode LIKE 'CL*'")
    
    If IsNull(rs!MaxNumber) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNumber + 1
    End If
    
    GenerateClientCode = "CL" & Format(NextNumber, "0000")
    
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GenerateClientCode = "CL0001"
    If Not rs Is Nothing Then rs.Close
End Function

Public Function ValidateClientData(CompanyName As String, ClientCode As String, Email As String) As String
    ' التحقق من صحة بيانات العميل
    Dim ErrorMsg As String
    ErrorMsg = ""
    
    ' التحقق من الحقول المطلوبة
    If Trim(CompanyName) = "" Then
        ErrorMsg = ErrorMsg & "- اسم الشركة مطلوب" & vbCrLf
    End If
    
    If Trim(ClientCode) = "" Then
        ErrorMsg = ErrorMsg & "- رمز العميل مطلوب" & vbCrLf
    End If
    
    ' التحقق من البريد الإلكتروني
    If Email <> "" And InStr(Email, "@") = 0 Then
        ErrorMsg = ErrorMsg & "- البريد الإلكتروني غير صحيح" & vbCrLf
    End If
    
    ' التحقق من عدم تكرار رمز العميل
    If DCount("*", "tbl_Clients", "ClientCode = '" & ClientCode & "'") > 0 Then
        ErrorMsg = ErrorMsg & "- رمز العميل موجود مسبقاً" & vbCrLf
    End If
    
    ValidateClientData = ErrorMsg
End Function

' ============================================================================
' دوال لوحة التحكم - Dashboard Functions
' ============================================================================

Public Function GetTotalSalesThisMonth() As Currency
    ' إجمالي المبيعات هذا الشهر
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT ISNULL(SUM(TotalAmount), 0) AS TotalSales FROM tbl_SalesOrders WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date()) AND Status <> 'Cancelled'")
    
    GetTotalSalesThisMonth = rs!TotalSales
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GetTotalSalesThisMonth = 0
    If Not rs Is Nothing Then rs.Close
End Function

Public Function GetActiveClientsCount() As Long
    ' عدد العملاء النشطين
    On Error GoTo ErrorHandler
    
    GetActiveClientsCount = DCount("*", "tbl_Clients", "IsActive = True")
    
    Exit Function
    
ErrorHandler:
    GetActiveClientsCount = 0
End Function

Public Function GetInventoryValue() As Currency
    ' قيمة المخزون الإجمالية
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT ISNULL(SUM(s.CurrentStock * p.CostPrice), 0) AS InventoryValue FROM tbl_StockLevels s INNER JOIN tbl_Products p ON s.ProductID = p.ProductID WHERE p.IsActive = True")
    
    GetInventoryValue = rs!InventoryValue
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GetInventoryValue = 0
    If Not rs Is Nothing Then rs.Close
End Function

Public Function GetLowStockCount() As Long
    ' عدد الأصناف منخفضة المخزون
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS LowStockCount FROM tbl_Products p INNER JOIN tbl_StockLevels s ON p.ProductID = s.ProductID WHERE p.IsActive = True AND s.CurrentStock <= p.ReorderPoint")
    
    GetLowStockCount = rs!LowStockCount
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GetLowStockCount = 0
    If Not rs Is Nothing Then rs.Close
End Function

' ============================================================================
' دوال إدارة المخزون - Inventory Management Functions
' ============================================================================

Public Function GenerateProductCode() As String
    ' توليد رمز منتج جديد
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim NextNumber As Long
    
    Set rs = CurrentDb.OpenRecordset("SELECT MAX(Val(Mid(ProductCode, 3))) AS MaxNumber FROM tbl_Products WHERE ProductCode LIKE 'PR*'")
    
    If IsNull(rs!MaxNumber) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNumber + 1
    End If
    
    GenerateProductCode = "PR" & Format(NextNumber, "0000")
    
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    GenerateProductCode = "PR0001"
    If Not rs Is Nothing Then rs.Close
End Function

Public Sub UpdateStockLevel(ProductID As Long, Quantity As Long, TransactionType As String, Optional ReferenceType As String = "", Optional ReferenceID As Long = 0)
    ' تحديث مستوى المخزون
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    ' تحديث مستوى المخزون
    If TransactionType = "IN" Then
        db.Execute "UPDATE tbl_StockLevels SET CurrentStock = CurrentStock + " & Quantity & ", LastUpdated = Now() WHERE ProductID = " & ProductID
    ElseIf TransactionType = "OUT" Then
        db.Execute "UPDATE tbl_StockLevels SET CurrentStock = CurrentStock - " & Quantity & ", LastUpdated = Now() WHERE ProductID = " & ProductID
    End If
    
    ' تسجيل حركة المخزون
    db.Execute "INSERT INTO tbl_InventoryTransactions (ProductID, TransactionType, Quantity, ReferenceType, ReferenceID, CreatedBy) " & _
               "VALUES (" & ProductID & ", '" & TransactionType & "', " & Quantity & ", '" & ReferenceType & "', " & ReferenceID & ", " & g_CurrentUserID & ")"
    
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not db Is Nothing Then Set db = Nothing
    MsgBox "خطأ في تحديث المخزون: " & Err.Description, vbCritical
End Sub

' ============================================================================
' دوال النسخ الاحتياطي - Backup Functions
' ============================================================================

Public Sub CreateBackup()
    ' إنشاء نسخة احتياطية
    On Error GoTo ErrorHandler
    
    Dim BackupPath As String
    Dim SourcePath As String
    Dim BackupFolder As String
    
    ' تحديد مسار النسخ الاحتياطي
    BackupFolder = CurrentProject.Path & "\Backups\"
    
    ' إنشاء مجلد النسخ الاحتياطي
    If Dir(BackupFolder, vbDirectory) = "" Then
        MkDir BackupFolder
    End If
    
    ' تحديد اسم الملف
    BackupPath = BackupFolder & "نظام_إدارة_المؤسسات_" & Format(Now(), "yyyymmdd_hhnnss") & ".accdb"
    SourcePath = CurrentProject.FullName
    
    ' نسخ الملف
    FileCopy SourcePath, BackupPath
    
    ' تسجيل العملية
    LogActivity "BACKUP", "تم إنشاء نسخة احتياطية: " & BackupPath
    
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح في:" & vbCrLf & BackupPath, vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في إنشاء النسخة الاحتياطية: " & Err.Description, vbCritical
End Sub

' ============================================================================
' دوال التقارير - Report Functions
' ============================================================================

Public Sub OpenClientReport()
    ' فتح تقرير العملاء
    DoCmd.OpenReport "rpt_ClientList", acViewPreview
End Sub

Public Sub OpenInventoryReport()
    ' فتح تقرير المخزون
    DoCmd.OpenReport "rpt_InventoryReport", acViewPreview
End Sub

Public Sub OpenSalesReport()
    ' فتح تقرير المبيعات
    DoCmd.OpenReport "rpt_SalesReport", acViewPreview
End Sub

Public Sub ExportToExcel(QueryName As String, ExportPath As String)
    ' تصدير إلى Excel
    On Error GoTo ErrorHandler
    
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, QueryName, ExportPath, True
    
    MsgBox "تم التصدير بنجاح إلى: " & ExportPath, vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في التصدير: " & Err.Description, vbCritical
End Sub

' ============================================================================
' دوال المساعدة - Utility Functions
' ============================================================================

Public Function IsValidEmail(Email As String) As Boolean
    ' التحقق من صحة البريد الإلكتروني
    If InStr(Email, "@") > 0 And InStr(Email, ".") > 0 Then
        IsValidEmail = True
    Else
        IsValidEmail = False
    End If
End Function

Public Function FormatCurrency(Amount As Currency) As String
    ' تنسيق العملة
    FormatCurrency = Format(Amount, "#,##0.00") & " ريال"
End Function

Public Function GetCurrentDate() As String
    ' الحصول على التاريخ الحالي مُنسق
    GetCurrentDate = Format(Now(), "dd/mm/yyyy")
End Function

Public Function GetCurrentTime() As String
    ' الحصول على الوقت الحالي مُنسق
    GetCurrentTime = Format(Now(), "hh:nn AM/PM")
End Function

' ============================================================================
' دوال إدارة الأخطاء - Error Handling Functions
' ============================================================================

Public Sub LogError(FunctionName As String, ErrorDescription As String)
    ' تسجيل الأخطاء
    On Error Resume Next
    
    CurrentDb.Execute "INSERT INTO tbl_SystemLog (LogType, Description, CreatedBy) " & _
                     "VALUES ('ERROR', 'خطأ في " & FunctionName & ": " & ErrorDescription & "', " & g_CurrentUserID & ")"
End Sub

Public Sub ShowError(FunctionName As String, ErrorDescription As String)
    ' عرض رسالة خطأ للمستخدم
    LogError FunctionName, ErrorDescription
    MsgBox "حدث خطأ في " & FunctionName & ":" & vbCrLf & ErrorDescription, vbCritical, "خطأ في النظام"
End Sub

' ============================================================================
' دوال التهيئة التلقائية - Auto-Initialization Functions
' ============================================================================

Public Sub Auto_Open()
    ' تشغيل تلقائي عند فتح قاعدة البيانات
    InitializeSystem
End Sub

Public Sub Auto_Close()
    ' تشغيل تلقائي عند إغلاق قاعدة البيانات
    If g_CurrentUserID > 0 Then
        LogActivity "LOGOUT", "تسجيل خروج للمستخدم: " & g_CurrentUsername
    End If
End Sub
