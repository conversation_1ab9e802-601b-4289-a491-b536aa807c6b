# تصميم النماذج الكامل - Complete Forms Design
## نظام إدارة المؤسسات - Enterprise Management System

### نموذج تسجيل الدخول (frm_Login)

#### خصائص النموذج:
```
Name: frm_Login
Caption: تسجيل الدخول - نظام إدارة المؤسسات
Modal: Yes
PopUp: Yes
AutoCenter: Yes
BorderStyle: Dialog
RecordSelectors: No
NavigationButtons: No
MinMaxButtons: None
Width: 12 cm (6912 twips)
Height: 8 cm (4608 twips)
BackColor: RGB(248, 249, 250)
```

#### العناصر:
```
1. Rectangle (خلفية العنوان):
   - Name: rect_Header
   - Left: 0, Top: 0, Width: 12 cm, Height: 2 cm
   - BackColor: RGB(46, 134, 171)
   - BorderStyle: Transparent

2. Label (عنوان النظام):
   - Name: lbl_Title
   - Caption: نظام إدارة المؤسسات
   - Left: 1 cm, Top: 0.3 cm, Width: 10 cm, Height: 0.8 cm
   - Font: <PERSON><PERSON><PERSON>, 18pt, Bold, White
   - TextAlign: Center

3. Label (العنوان الإنجليزي):
   - Name: lbl_SubTitle
   - Caption: Enterprise Management System
   - Left: 1 cm, Top: 1.1 cm, Width: 10 cm, Height: 0.6 cm
   - Font: Segoe UI, 12pt, White
   - TextAlign: Center

4. Label (اسم المستخدم):
   - Name: lbl_Username
   - Caption: اسم المستخدم:
   - Left: 2 cm, Top: 3 cm, Width: 2.5 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt, Bold

5. TextBox (مربع اسم المستخدم):
   - Name: txt_Username
   - Left: 5 cm, Top: 3 cm, Width: 5 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt
   - BorderStyle: Solid
   - BorderColor: RGB(206, 212, 218)

6. Label (كلمة المرور):
   - Name: lbl_Password
   - Caption: كلمة المرور:
   - Left: 2 cm, Top: 4 cm, Width: 2.5 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt, Bold

7. TextBox (مربع كلمة المرور):
   - Name: txt_Password
   - InputMask: Password
   - Left: 5 cm, Top: 4 cm, Width: 5 cm, Height: 0.6 cm
   - Font: Tahoma, 11pt
   - BorderStyle: Solid
   - BorderColor: RGB(206, 212, 218)

8. Command Button (دخول):
   - Name: btn_Login
   - Caption: دخول
   - Left: 3 cm, Top: 5.5 cm, Width: 2.5 cm, Height: 0.8 cm
   - Font: Tahoma, 11pt, Bold, White
   - BackColor: RGB(46, 134, 171)
   - BorderStyle: Solid
   - BorderColor: RGB(46, 134, 171)

9. Command Button (إلغاء):
   - Name: btn_Cancel
   - Caption: إلغاء
   - Left: 6.5 cm, Top: 5.5 cm, Width: 2.5 cm, Height: 0.8 cm
   - Font: Tahoma, 11pt, Bold
   - BackColor: RGB(248, 249, 250)
   - BorderStyle: Solid
   - BorderColor: RGB(206, 212, 218)

10. Label (معلومات النظام):
    - Name: lbl_Version
    - Caption: الإصدار 1.0 - © 2025
    - Left: 1 cm, Top: 7 cm, Width: 10 cm, Height: 0.4 cm
    - Font: Tahoma, 8pt, Gray
    - TextAlign: Center
```

#### كود VBA لنموذج تسجيل الدخول:
```vba
Private Sub Form_Load()
    ' تعيين قيم افتراضية للاختبار
    Me.txt_Username = "admin"
    Me.txt_Password = "12345"
    Me.txt_Username.SetFocus
End Sub

Private Sub btn_Login_Click()
    Dim Username As String
    Dim Password As String
    
    Username = Nz(Me.txt_Username, "")
    Password = Nz(Me.txt_Password, "")
    
    If Username = "" Or Password = "" Then
        MsgBox "يرجى إدخال اسم المستخدم وكلمة المرور", vbExclamation, "بيانات ناقصة"
        Exit Sub
    End If
    
    If AuthenticateUser(Username, Password) Then
        ' إغلاق نموذج الدخول وفتح النموذج الرئيسي
        DoCmd.Close acForm, Me.Name
        DoCmd.OpenForm "frm_MainNavigation"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbCritical, "خطأ في تسجيل الدخول"
        Me.txt_Password = ""
        Me.txt_Username.SetFocus
    End If
End Sub

Private Sub btn_Cancel_Click()
    Application.Quit
End Sub

Private Sub txt_Password_KeyPress(KeyAscii As Integer)
    If KeyAscii = 13 Then ' Enter key
        btn_Login_Click
    End If
End Sub
```

---

### النموذج الرئيسي (frm_MainNavigation)

#### خصائص النموذج:
```
Name: frm_MainNavigation
Caption: نظام إدارة المؤسسات - الصفحة الرئيسية
Modal: No
PopUp: No
AutoCenter: Yes
BorderStyle: Sizable
Width: 25 cm (14400 twips)
Height: 18 cm (10368 twips)
BackColor: RGB(248, 249, 250)
MinMaxButtons: Min Enabled
```

#### قسم الرأس:
```
1. Rectangle (خلفية الرأس):
   - Name: rect_Header
   - Left: 0, Top: 0, Width: 25 cm, Height: 2.5 cm
   - BackColor: RGB(46, 134, 171)
   - BorderStyle: Transparent

2. Label (عنوان النظام):
   - Name: lbl_SystemTitle
   - Caption: نظام إدارة المؤسسات الشامل
   - Left: 1 cm, Top: 0.3 cm, Width: 12 cm, Height: 0.8 cm
   - Font: Tahoma, 20pt, Bold, White

3. Label (العنوان الإنجليزي):
   - Name: lbl_SystemTitleEn
   - Caption: Enterprise Management System
   - Left: 1 cm, Top: 1.2 cm, Width: 10 cm, Height: 0.6 cm
   - Font: Segoe UI, 14pt, White

4. Label (معلومات المستخدم):
   - Name: lbl_UserInfo
   - Caption: مرحباً، [اسم المستخدم]
   - Left: 18 cm, Top: 0.5 cm, Width: 5 cm, Height: 0.6 cm
   - Font: Tahoma, 12pt, Bold, White
   - TextAlign: Right

5. Label (التاريخ والوقت):
   - Name: lbl_DateTime
   - Caption: [التاريخ والوقت]
   - Left: 18 cm, Top: 1.2 cm, Width: 5 cm, Height: 0.5 cm
   - Font: Tahoma, 10pt, White
   - TextAlign: Right

6. Command Button (تسجيل خروج):
   - Name: btn_Logout
   - Caption: تسجيل خروج
   - Left: 21 cm, Top: 1.8 cm, Width: 3 cm, Height: 0.6 cm
   - Font: Tahoma, 10pt, Bold, White
   - BackColor: RGB(220, 53, 69)
   - BorderStyle: Solid
```

#### أزرار الوحدات (شبكة 4×3):

**الصف الأول:**
```
1. btn_Dashboard:
   - Caption: 📊 لوحة التحكم
   - Left: 2 cm, Top: 4 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(46, 134, 171)
   - Font: Tahoma, 14pt, Bold, White

2. btn_Clients:
   - Caption: 👥 إدارة العملاء
   - Left: 9.5 cm, Top: 4 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(76, 175, 80)
   - Font: Tahoma, 14pt, Bold, White

3. btn_Sales:
   - Caption: 💰 إدارة المبيعات
   - Left: 17 cm, Top: 4 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(156, 39, 176)
   - Font: Tahoma, 14pt, Bold, White
```

**الصف الثاني:**
```
4. btn_Purchases:
   - Caption: 🛒 إدارة المشتريات
   - Left: 2 cm, Top: 8 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(255, 152, 0)
   - Font: Tahoma, 14pt, Bold, White

5. btn_Inventory:
   - Caption: 📦 إدارة المخازن
   - Left: 9.5 cm, Top: 8 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(121, 85, 72)
   - Font: Tahoma, 14pt, Bold, White

6. btn_Suppliers:
   - Caption: 🏭 إدارة الموردين
   - Left: 17 cm, Top: 8 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(0, 150, 136)
   - Font: Tahoma, 14pt, Bold, White
```

**الصف الثالث:**
```
7. btn_Employees:
   - Caption: 👤 إدارة الموظفين
   - Left: 2 cm, Top: 12 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(63, 81, 181)
   - Font: Tahoma, 14pt, Bold, White

8. btn_Accounting:
   - Caption: 💳 إدارة الحسابات
   - Left: 9.5 cm, Top: 12 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(255, 87, 34)
   - Font: Tahoma, 14pt, Bold, White

9. btn_Invoices:
   - Caption: 📄 إدارة الفواتير
   - Left: 17 cm, Top: 12 cm, Width: 6 cm, Height: 3 cm
   - BackColor: RGB(3, 169, 244)
   - Font: Tahoma, 14pt, Bold, White
```

**الصف الرابع:**
```
10. btn_Reports:
    - Caption: 📊 إدارة التقارير
    - Left: 2 cm, Top: 16 cm, Width: 6 cm, Height: 3 cm
    - BackColor: RGB(139, 195, 74)
    - Font: Tahoma, 14pt, Bold, White

11. btn_Users:
    - Caption: 🔐 إدارة المستخدمين
    - Left: 9.5 cm, Top: 16 cm, Width: 6 cm, Height: 3 cm
    - BackColor: RGB(244, 67, 54)
    - Font: Tahoma, 14pt, Bold, White

12. btn_Database:
    - Caption: 🗄️ إدارة قاعدة البيانات
    - Left: 17 cm, Top: 16 cm, Width: 6 cm, Height: 3 cm
    - BackColor: RGB(96, 125, 139)
    - Font: Tahoma, 14pt, Bold, White
```

#### كود VBA للنموذج الرئيسي:
```vba
Private Sub Form_Load()
    ' عرض معلومات المستخدم
    Me.lbl_UserInfo.Caption = "مرحباً، " & g_CurrentUsername
    
    ' عرض التاريخ والوقت
    Me.lbl_DateTime.Caption = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
    
    ' تشغيل مؤقت لتحديث الوقت
    Me.TimerInterval = 60000 ' كل دقيقة
End Sub

Private Sub Form_Timer()
    ' تحديث الوقت
    Me.lbl_DateTime.Caption = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
End Sub

' أحداث النقر على الأزرار
Private Sub btn_Dashboard_Click()
    DoCmd.OpenForm "frm_Dashboard"
End Sub

Private Sub btn_Clients_Click()
    DoCmd.OpenForm "frm_Clients"
End Sub

Private Sub btn_Sales_Click()
    MsgBox "وحدة إدارة المبيعات قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Purchases_Click()
    MsgBox "وحدة إدارة المشتريات قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Inventory_Click()
    DoCmd.OpenForm "frm_Inventory"
End Sub

Private Sub btn_Suppliers_Click()
    DoCmd.OpenForm "frm_Suppliers"
End Sub

Private Sub btn_Employees_Click()
    MsgBox "وحدة إدارة الموظفين قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Accounting_Click()
    MsgBox "وحدة إدارة الحسابات قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Invoices_Click()
    MsgBox "وحدة إدارة الفواتير قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Reports_Click()
    DoCmd.OpenForm "frm_Reports"
End Sub

Private Sub btn_Users_Click()
    DoCmd.OpenForm "frm_Users"
End Sub

Private Sub btn_Database_Click()
    MsgBox "وحدة إدارة قاعدة البيانات قيد التطوير", vbInformation, "قريباً"
End Sub

Private Sub btn_Logout_Click()
    If MsgBox("هل تريد تسجيل الخروج؟", vbYesNo + vbQuestion, "تأكيد") = vbYes Then
        ' مسح بيانات المستخدم
        g_CurrentUserID = 0
        g_CurrentUsername = ""
        g_CurrentUserRole = ""
        
        ' إغلاق النموذج الحالي وفتح نموذج الدخول
        DoCmd.Close acForm, Me.Name
        DoCmd.OpenForm "frm_Login"
    End If
End Sub

' تأثيرات التمرير على الأزرار
Private Sub btn_Dashboard_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
    Me.btn_Dashboard.BackColor = RGB(30, 100, 140)
End Sub

Private Sub Detail_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
    ' إعادة تعيين ألوان الأزرار الأصلية
    Me.btn_Dashboard.BackColor = RGB(46, 134, 171)
    Me.btn_Clients.BackColor = RGB(76, 175, 80)
    Me.btn_Sales.BackColor = RGB(156, 39, 176)
    ' ... باقي الأزرار
End Sub
```

---

### إعداد النموذج كنموذج البداية:

```
1. اذهب إلى File > Options > Current Database
2. في Display Form اختر "frm_Login"
3. في Application Title اكتب "نظام إدارة المؤسسات"
4. فعل "Use Access Special Keys" = No
5. فعل "Display Navigation Pane" = No
6. احفظ الإعدادات وأعد تشغيل قاعدة البيانات
```

هذا التصميم يوفر واجهة مستخدم احترافية وسهلة الاستخدام مع دعم كامل للغة العربية.
