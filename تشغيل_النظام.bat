@echo off
chcp 65001 >nul
title نظام إدارة المؤسسات - Enterprise Management System

echo.
echo ===============================================
echo    نظام إدارة المؤسسات الشامل
echo    Enterprise Management System
echo    الإصدار 1.0 - Version 1.0
echo ===============================================
echo.

REM التحقق من وجود Microsoft Access
echo [1/5] التحقق من متطلبات النظام...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Office" >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Microsoft Access غير مثبت على النظام
    echo    يرجى تثبيت Microsoft Access 2016 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود ملف قاعدة البيانات
echo [2/5] البحث عن ملف قاعدة البيانات...
if exist "نظام_إدارة_المؤسسات.accdb" (
    echo ✅ تم العثور على ملف قاعدة البيانات
) else (
    echo ⚠️  لم يتم العثور على ملف قاعدة البيانات
    echo    سيتم إنشاء قاعدة بيانات جديدة...
    call :CreateDatabase
)

REM إنشاء مجلدات النظام
echo [3/5] إنشاء مجلدات النظام...
if not exist "Backups" mkdir "Backups"
if not exist "Reports" mkdir "Reports"
if not exist "Logs" mkdir "Logs"
if not exist "Temp" mkdir "Temp"
echo ✅ تم إنشاء مجلدات النظام

REM التحقق من الصلاحيات
echo [4/5] التحقق من صلاحيات الملفات...
echo. > "test_write.tmp" 2>nul
if exist "test_write.tmp" (
    del "test_write.tmp" >nul 2>&1
    echo ✅ صلاحيات الكتابة متاحة
) else (
    echo ❌ خطأ: لا توجد صلاحيات كتابة في المجلد الحالي
    echo    يرجى تشغيل البرنامج كمدير أو نقل الملفات لمجلد آخر
    pause
    exit /b 1
)

REM تشغيل قاعدة البيانات
echo [5/5] تشغيل نظام إدارة المؤسسات...
echo.
echo 🚀 جاري تشغيل النظام...
echo    بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: 12345
echo.

REM تشغيل Access مع قاعدة البيانات
start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb"

REM انتظار قليل ثم التحقق من التشغيل
timeout /t 3 >nul
tasklist /fi "imagename eq msaccess.exe" 2>nul | find /i "msaccess.exe" >nul
if errorlevel 1 (
    echo ❌ فشل في تشغيل Microsoft Access
    echo    يرجى التحقق من تثبيت البرنامج بشكل صحيح
    pause
    exit /b 1
) else (
    echo ✅ تم تشغيل النظام بنجاح!
    echo.
    echo 📋 ملاحظات مهمة:
    echo    • استخدم بيانات تسجيل الدخول المذكورة أعلاه
    echo    • تأكد من تغيير كلمة المرور بعد أول دخول
    echo    • يتم حفظ النسخ الاحتياطية في مجلد Backups
    echo    • التقارير تُحفظ في مجلد Reports
    echo.
)

echo اضغط أي مفتاح للإغلاق...
pause >nul
exit /b 0

:CreateDatabase
echo.
echo 🔧 إنشاء قاعدة بيانات جديدة...
echo    هذا قد يستغرق بضع دقائق...

REM إنشاء ملف VBS لإنشاء قاعدة البيانات
echo Set objAccess = CreateObject("Access.Application") > create_db.vbs
echo objAccess.NewCurrentDatabase "نظام_إدارة_المؤسسات.accdb" >> create_db.vbs
echo objAccess.Quit >> create_db.vbs

REM تشغيل ملف VBS
cscript //nologo create_db.vbs

REM حذف ملف VBS المؤقت
del create_db.vbs >nul 2>&1

if exist "نظام_إدارة_المؤسسات.accdb" (
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
    echo ⚠️  تحتاج الآن لتنفيذ سكريبت إنشاء الجداول يدوياً
    echo    راجع ملف "Complete_Database_Script.sql"
) else (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo    يرجى إنشاؤها يدوياً باستخدام Microsoft Access
)
goto :eof
