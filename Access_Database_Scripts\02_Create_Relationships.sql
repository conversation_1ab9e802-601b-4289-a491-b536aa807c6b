-- Enterprise Management System - Relationships Creation Script
-- Microsoft Access SQL Script for creating table relationships
-- Note: In Access, relationships are typically created through the Relationships window
-- This script provides the ALTER TABLE statements for reference

-- ============================================================================
-- USER MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Users to Roles relationship (Many-to-Many through UserRoles)
ALTER TABLE tbl_UserRoles 
ADD CONSTRAINT FK_UserRoles_Users 
FOREIGN KEY (UserID) REFERENCES tbl_Users(UserID);

ALTER TABLE tbl_UserRoles 
ADD CONSTRAINT FK_UserRoles_Roles 
FOREIGN KEY (RoleID) REFERENCES tbl_Roles(RoleID);

-- Role Permissions relationship (Many-to-Many)
ALTER TABLE tbl_RolePermissions 
ADD CONSTRAINT FK_RolePermissions_Roles 
FOREIGN KEY (RoleID) REFERENCES tbl_Roles(RoleID);

ALTER TABLE tbl_RolePermissions 
ADD CONSTRAINT FK_RolePermissions_Permissions 
FOREIGN KEY (PermissionID) REFERENCES tbl_Permissions(PermissionID);

-- ============================================================================
-- CLIENT MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Client Contacts to Clients
ALTER TABLE tbl_ClientContacts 
ADD CONSTRAINT FK_ClientContacts_Clients 
FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID);

-- ============================================================================
-- PRODUCT AND INVENTORY RELATIONSHIPS
-- ============================================================================

-- Products to Categories
ALTER TABLE tbl_Products 
ADD CONSTRAINT FK_Products_Categories 
FOREIGN KEY (CategoryID) REFERENCES tbl_Categories(CategoryID);

-- Products to Suppliers
ALTER TABLE tbl_Products 
ADD CONSTRAINT FK_Products_Suppliers 
FOREIGN KEY (SupplierID) REFERENCES tbl_Suppliers(SupplierID);

-- Categories self-referencing (Parent Category)
ALTER TABLE tbl_Categories 
ADD CONSTRAINT FK_Categories_ParentCategory 
FOREIGN KEY (ParentCategoryID) REFERENCES tbl_Categories(CategoryID);

-- Stock Levels to Products
ALTER TABLE tbl_StockLevels 
ADD CONSTRAINT FK_StockLevels_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- Inventory Transactions to Products
ALTER TABLE tbl_InventoryTransactions 
ADD CONSTRAINT FK_InventoryTransactions_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- ============================================================================
-- SALES MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Quotations to Clients
ALTER TABLE tbl_Quotations 
ADD CONSTRAINT FK_Quotations_Clients 
FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID);

-- Quotation Items to Quotations
ALTER TABLE tbl_QuotationItems 
ADD CONSTRAINT FK_QuotationItems_Quotations 
FOREIGN KEY (QuoteID) REFERENCES tbl_Quotations(QuoteID);

-- Quotation Items to Products
ALTER TABLE tbl_QuotationItems 
ADD CONSTRAINT FK_QuotationItems_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- Sales Orders to Clients
ALTER TABLE tbl_SalesOrders 
ADD CONSTRAINT FK_SalesOrders_Clients 
FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID);

-- Sales Orders to Quotations (optional)
ALTER TABLE tbl_SalesOrders 
ADD CONSTRAINT FK_SalesOrders_Quotations 
FOREIGN KEY (QuoteID) REFERENCES tbl_Quotations(QuoteID);

-- Sales Order Items to Sales Orders
ALTER TABLE tbl_SalesOrderItems 
ADD CONSTRAINT FK_SalesOrderItems_SalesOrders 
FOREIGN KEY (OrderID) REFERENCES tbl_SalesOrders(OrderID);

-- Sales Order Items to Products
ALTER TABLE tbl_SalesOrderItems 
ADD CONSTRAINT FK_SalesOrderItems_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- ============================================================================
-- PURCHASE MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Purchase Orders to Suppliers
ALTER TABLE tbl_PurchaseOrders 
ADD CONSTRAINT FK_PurchaseOrders_Suppliers 
FOREIGN KEY (SupplierID) REFERENCES tbl_Suppliers(SupplierID);

-- Purchase Order Items to Purchase Orders
ALTER TABLE tbl_PurchaseOrderItems 
ADD CONSTRAINT FK_PurchaseOrderItems_PurchaseOrders 
FOREIGN KEY (PurchaseOrderID) REFERENCES tbl_PurchaseOrders(PurchaseOrderID);

-- Purchase Order Items to Products
ALTER TABLE tbl_PurchaseOrderItems 
ADD CONSTRAINT FK_PurchaseOrderItems_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- ============================================================================
-- ACCOUNTING MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Accounts self-referencing (Parent Account)
ALTER TABLE tbl_Accounts 
ADD CONSTRAINT FK_Accounts_ParentAccount 
FOREIGN KEY (ParentAccountID) REFERENCES tbl_Accounts(AccountID);

-- Transaction Items to Transactions
ALTER TABLE tbl_TransactionItems 
ADD CONSTRAINT FK_TransactionItems_Transactions 
FOREIGN KEY (TransactionID) REFERENCES tbl_Transactions(TransactionID);

-- Transaction Items to Accounts
ALTER TABLE tbl_TransactionItems 
ADD CONSTRAINT FK_TransactionItems_Accounts 
FOREIGN KEY (AccountID) REFERENCES tbl_Accounts(AccountID);

-- ============================================================================
-- INVOICE MANAGEMENT RELATIONSHIPS
-- ============================================================================

-- Invoices to Clients
ALTER TABLE tbl_Invoices 
ADD CONSTRAINT FK_Invoices_Clients 
FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID);

-- Invoices to Sales Orders (optional)
ALTER TABLE tbl_Invoices 
ADD CONSTRAINT FK_Invoices_SalesOrders 
FOREIGN KEY (SalesOrderID) REFERENCES tbl_SalesOrders(OrderID);

-- Invoice Items to Invoices
ALTER TABLE tbl_InvoiceItems 
ADD CONSTRAINT FK_InvoiceItems_Invoices 
FOREIGN KEY (InvoiceID) REFERENCES tbl_Invoices(InvoiceID);

-- Invoice Items to Products (optional)
ALTER TABLE tbl_InvoiceItems 
ADD CONSTRAINT FK_InvoiceItems_Products 
FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID);

-- Payments to Clients (optional)
ALTER TABLE tbl_Payments 
ADD CONSTRAINT FK_Payments_Clients 
FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID);

-- Payments to Invoices (optional)
ALTER TABLE tbl_Payments 
ADD CONSTRAINT FK_Payments_Invoices 
FOREIGN KEY (InvoiceID) REFERENCES tbl_Invoices(InvoiceID);

-- ============================================================================
-- REFERENTIAL INTEGRITY NOTES
-- ============================================================================

/*
IMPORTANT NOTES FOR ACCESS DATABASE RELATIONSHIPS:

1. Relationships in Access are typically created through the Relationships window (Database Tools > Relationships)

2. When creating relationships in Access:
   - Enable "Enforce Referential Integrity"
   - Enable "Cascade Update Related Fields" 
   - DO NOT enable "Cascade Delete Related Records" to preserve data integrity

3. Relationship Types:
   - One-to-Many: Most relationships in this system
   - Many-to-Many: Implemented through junction tables (UserRoles, RolePermissions)
   - One-to-One: StockLevels to Products

4. Index Creation:
   Access automatically creates indexes on:
   - Primary Key fields
   - Foreign Key fields in relationships
   - Fields with Unique constraints

5. To create these relationships in Access:
   a) Open the database
   b) Go to Database Tools > Relationships
   c) Add all tables to the relationships window
   d) Drag from the primary key field to the foreign key field
   e) Check "Enforce Referential Integrity" and "Cascade Update Related Fields"
   f) Click "Create"

6. Validation Rules:
   Some business rules should be implemented as field validation rules:
   - CreditLimit >= 0
   - PaymentTerms BETWEEN 0 AND 365
   - Quantity > 0
   - UnitPrice >= 0

7. Default Values:
   Set appropriate default values in table design:
   - IsActive = Yes
   - CreatedDate = Now()
   - Status fields = appropriate default status
   - Currency fields = 0

8. Required Fields:
   Mark essential fields as Required = Yes:
   - All Name fields
   - All Code fields
   - Date fields (except optional ones)
   - Foreign key fields (where appropriate)
*/
