# نظام إدارة المؤسسات الشامل - ملف التشغيل
# Enterprise Management System - Startup Script
# PowerShell Script for Advanced System Launch

param(
    [switch]$CreateNew,
    [switch]$Backup,
    [switch]$Repair,
    [string]$DatabasePath = "نظام_إدارة_المؤسسات.accdb"
)

# إعداد الترميز للعربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات النظام
$SystemName = "نظام إدارة المؤسسات الشامل"
$SystemVersion = "1.0"
$RequiredAccessVersion = "16.0" # Access 2016 or later

# ألوان النص
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Show-Header {
    Clear-Host
    Write-ColorText "===============================================" "Header"
    Write-ColorText "    $SystemName" "Header"
    Write-ColorText "    Enterprise Management System" "Header"
    Write-ColorText "    الإصدار $SystemVersion - Version $SystemVersion" "Header"
    Write-ColorText "===============================================" "Header"
    Write-Host ""
}

function Test-AccessInstallation {
    Write-ColorText "[1/6] التحقق من تثبيت Microsoft Access..." "Info"
    
    try {
        $accessApp = New-Object -ComObject Access.Application
        $version = $accessApp.Version
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        
        if ([double]$version -ge [double]$RequiredAccessVersion) {
            Write-ColorText "✅ Microsoft Access $version مثبت ومتوافق" "Success"
            return $true
        } else {
            Write-ColorText "❌ إصدار Access قديم ($version). يتطلب الإصدار $RequiredAccessVersion أو أحدث" "Error"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ Microsoft Access غير مثبت أو غير متاح" "Error"
        Write-ColorText "   يرجى تثبيت Microsoft Access 2016 أو أحدث" "Warning"
        return $false
    }
}

function Test-DatabaseFile {
    Write-ColorText "[2/6] التحقق من ملف قاعدة البيانات..." "Info"
    
    if (Test-Path $DatabasePath) {
        $fileSize = (Get-Item $DatabasePath).Length / 1MB
        Write-ColorText "✅ تم العثور على ملف قاعدة البيانات ($('{0:N2}' -f $fileSize) MB)" "Success"
        
        # التحقق من سلامة الملف
        try {
            $accessApp = New-Object -ComObject Access.Application
            $accessApp.OpenCurrentDatabase($DatabasePath)
            $tableCount = $accessApp.CurrentDb.TableDefs.Count
            $accessApp.CloseCurrentDatabase()
            $accessApp.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
            
            Write-ColorText "✅ قاعدة البيانات سليمة وتحتوي على $tableCount جدول" "Success"
            return $true
        }
        catch {
            Write-ColorText "⚠️ قد يكون ملف قاعدة البيانات تالفاً" "Warning"
            Write-ColorText "   استخدم المعامل -Repair لإصلاح قاعدة البيانات" "Info"
            return $false
        }
    }
    else {
        Write-ColorText "⚠️ لم يتم العثور على ملف قاعدة البيانات" "Warning"
        
        if ($CreateNew) {
            return New-Database
        }
        else {
            Write-ColorText "   استخدم المعامل -CreateNew لإنشاء قاعدة بيانات جديدة" "Info"
            return $false
        }
    }
}

function New-Database {
    Write-ColorText "[إنشاء قاعدة بيانات جديدة]" "Info"
    Write-ColorText "🔧 جاري إنشاء قاعدة بيانات جديدة..." "Info"
    
    try {
        $accessApp = New-Object -ComObject Access.Application
        $accessApp.NewCurrentDatabase($DatabasePath)
        
        # إنشاء جدول تجريبي للتأكد من عمل قاعدة البيانات
        $sql = @"
CREATE TABLE tbl_SystemInfo (
    ID AUTOINCREMENT PRIMARY KEY,
    SystemName TEXT(100),
    Version TEXT(20),
    CreatedDate DATETIME DEFAULT Now()
);
"@
        $accessApp.DoCmd.RunSQL($sql)
        
        # إدخال بيانات النظام
        $insertSql = "INSERT INTO tbl_SystemInfo (SystemName, Version) VALUES ('$SystemName', '$SystemVersion');"
        $accessApp.DoCmd.RunSQL($insertSql)
        
        $accessApp.CloseCurrentDatabase()
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        
        Write-ColorText "✅ تم إنشاء قاعدة البيانات بنجاح" "Success"
        Write-ColorText "⚠️ تحتاج الآن لتنفيذ سكريبت إنشاء الجداول:" "Warning"
        Write-ColorText "   1. افتح قاعدة البيانات في Access" "Info"
        Write-ColorText "   2. انسخ محتوى ملف Complete_Database_Script.sql" "Info"
        Write-ColorText "   3. نفذ السكريبت في SQL View" "Info"
        
        return $true
    }
    catch {
        Write-ColorText "❌ فشل في إنشاء قاعدة البيانات: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Repair-Database {
    Write-ColorText "[إصلاح قاعدة البيانات]" "Info"
    Write-ColorText "🔧 جاري إصلاح وضغط قاعدة البيانات..." "Info"
    
    try {
        $backupPath = "Backups\نظام_إدارة_المؤسسات_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').accdb"
        Copy-Item $DatabasePath $backupPath
        Write-ColorText "✅ تم إنشاء نسخة احتياطية: $backupPath" "Success"
        
        $accessApp = New-Object -ComObject Access.Application
        $tempPath = "temp_repaired.accdb"
        
        # ضغط وإصلاح قاعدة البيانات
        $accessApp.CompactRepair($DatabasePath, $tempPath)
        
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        
        # استبدال الملف الأصلي بالملف المُصلح
        Remove-Item $DatabasePath
        Rename-Item $tempPath $DatabasePath
        
        Write-ColorText "✅ تم إصلاح قاعدة البيانات بنجاح" "Success"
        return $true
    }
    catch {
        Write-ColorText "❌ فشل في إصلاح قاعدة البيانات: $($_.Exception.Message)" "Error"
        return $false
    }
}

function New-SystemFolders {
    Write-ColorText "[3/6] إنشاء مجلدات النظام..." "Info"
    
    $folders = @("Backups", "Reports", "Logs", "Temp", "Assets", "Documentation")
    
    foreach ($folder in $folders) {
        if (!(Test-Path $folder)) {
            New-Item -ItemType Directory -Path $folder -Force | Out-Null
            Write-ColorText "📁 تم إنشاء مجلد: $folder" "Success"
        }
    }
    
    Write-ColorText "✅ تم إنشاء جميع مجلدات النظام" "Success"
}

function Test-Permissions {
    Write-ColorText "[4/6] التحقق من صلاحيات النظام..." "Info"
    
    try {
        $testFile = "test_permissions.tmp"
        "test" | Out-File $testFile
        Remove-Item $testFile
        Write-ColorText "✅ صلاحيات الكتابة متاحة" "Success"
        return $true
    }
    catch {
        Write-ColorText "❌ لا توجد صلاحيات كتابة في المجلد الحالي" "Error"
        Write-ColorText "   يرجى تشغيل PowerShell كمدير أو نقل الملفات لمجلد آخر" "Warning"
        return $false
    }
}

function Start-BackupProcess {
    Write-ColorText "[إنشاء نسخة احتياطية]" "Info"
    
    if (!(Test-Path $DatabasePath)) {
        Write-ColorText "❌ لا يمكن العثور على ملف قاعدة البيانات للنسخ الاحتياطي" "Error"
        return $false
    }
    
    $backupPath = "Backups\نظام_إدارة_المؤسسات_$(Get-Date -Format 'yyyyMMdd_HHmmss').accdb"
    
    try {
        Copy-Item $DatabasePath $backupPath
        $backupSize = (Get-Item $backupPath).Length / 1MB
        Write-ColorText "✅ تم إنشاء النسخة الاحتياطية بنجاح" "Success"
        Write-ColorText "📁 المسار: $backupPath" "Info"
        Write-ColorText "📊 الحجم: $('{0:N2}' -f $backupSize) MB" "Info"
        return $true
    }
    catch {
        Write-ColorText "❌ فشل في إنشاء النسخة الاحتياطية: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-SystemInfo {
    Write-ColorText "[5/6] معلومات النظام..." "Info"
    
    Write-Host ""
    Write-ColorText "📋 معلومات النظام:" "Header"
    Write-ColorText "   • اسم النظام: $SystemName" "Info"
    Write-ColorText "   • الإصدار: $SystemVersion" "Info"
    Write-ColorText "   • ملف قاعدة البيانات: $DatabasePath" "Info"
    Write-ColorText "   • تاريخ التشغيل: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Info"
    Write-Host ""
    
    Write-ColorText "🔑 بيانات تسجيل الدخول الافتراضية:" "Header"
    Write-ColorText "   • اسم المستخدم: admin" "Success"
    Write-ColorText "   • كلمة المرور: 12345" "Success"
    Write-Host ""
    
    Write-ColorText "⚠️ ملاحظات مهمة:" "Warning"
    Write-ColorText "   • تأكد من تغيير كلمة المرور بعد أول دخول" "Warning"
    Write-ColorText "   • يتم حفظ النسخ الاحتياطية في مجلد Backups" "Info"
    Write-ColorText "   • التقارير تُحفظ في مجلد Reports" "Info"
    Write-ColorText "   • سجلات النظام في مجلد Logs" "Info"
    Write-Host ""
}

function Start-Application {
    Write-ColorText "[6/6] تشغيل نظام إدارة المؤسسات..." "Info"
    Write-ColorText "🚀 جاري تشغيل النظام..." "Success"
    
    try {
        Start-Process "msaccess.exe" -ArgumentList "`"$DatabasePath`"" -WindowStyle Maximized
        
        # انتظار قليل للتأكد من التشغيل
        Start-Sleep -Seconds 3
        
        $accessProcess = Get-Process -Name "msaccess" -ErrorAction SilentlyContinue
        if ($accessProcess) {
            Write-ColorText "✅ تم تشغيل النظام بنجاح!" "Success"
            Write-ColorText "🎉 مرحباً بك في نظام إدارة المؤسسات!" "Header"
            return $true
        }
        else {
            Write-ColorText "❌ فشل في تشغيل Microsoft Access" "Error"
            return $false
        }
    }
    catch {
        Write-ColorText "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-Menu {
    Write-Host ""
    Write-ColorText "📋 خيارات إضافية:" "Header"
    Write-ColorText "   1. إنشاء نسخة احتياطية" "Info"
    Write-ColorText "   2. إصلاح قاعدة البيانات" "Info"
    Write-ColorText "   3. إنشاء قاعدة بيانات جديدة" "Info"
    Write-ColorText "   4. عرض معلومات النظام" "Info"
    Write-ColorText "   5. خروج" "Info"
    Write-Host ""
    
    $choice = Read-Host "اختر رقماً (1-5) أو اضغط Enter للمتابعة"
    
    switch ($choice) {
        "1" { Start-BackupProcess; Show-Menu }
        "2" { Repair-Database; Show-Menu }
        "3" { New-Database; Show-Menu }
        "4" { Show-SystemInfo; Show-Menu }
        "5" { exit }
        default { return }
    }
}

# البرنامج الرئيسي
function Main {
    Show-Header
    
    # معالجة المعاملات
    if ($Backup) {
        Start-BackupProcess
        return
    }
    
    if ($Repair) {
        Repair-Database
        return
    }
    
    # التحقق من المتطلبات وتشغيل النظام
    if (!(Test-AccessInstallation)) { 
        Read-Host "اضغط Enter للخروج"
        return 
    }
    
    if (!(Test-DatabaseFile)) { 
        Show-Menu
        return 
    }
    
    New-SystemFolders
    
    if (!(Test-Permissions)) { 
        Read-Host "اضغط Enter للخروج"
        return 
    }
    
    Show-SystemInfo
    
    if (Start-Application) {
        Write-ColorText "النظام يعمل الآن. يمكنك إغلاق هذه النافذة." "Success"
    }
    else {
        Write-ColorText "فشل في تشغيل النظام. يرجى المحاولة مرة أخرى." "Error"
        Show-Menu
    }
    
    Read-Host "اضغط Enter للخروج"
}

# تشغيل البرنامج الرئيسي
Main
