# نظام إدارة التراخيص المتقدم - Advanced License Management System
# Enterprise Management System License Manager
# الإصدار 1.0 - Version 1.0

param(
    [switch]$GenerateLicense,
    [switch]$ValidateLicense,
    [switch]$CreateMasterKey,
    [string]$CompanyName = "",
    [string]$ContactEmail = "",
    [int]$ValidityDays = 365,
    [string]$LicenseType = "Standard"
)

# إعداد الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات النظام
$MasterKey = "EnterpriseLicenseSystem2025!@#$%^&*()"
$LicenseVersion = "1.0"
$DeveloperInfo = @{
    Company = "نظم إدارة المؤسسات"
    Email = "<EMAIL>"
    Phone = "+966-XX-XXXXXXX"
    Website = "www.enterprise-system.com"
}

function Write-Status {
    param([string]$Message, [string]$Status = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        "LICENSE" { Write-Host "[$timestamp] 🎫 $Message" -ForegroundColor Magenta }
        "SECURITY" { Write-Host "[$timestamp] 🔒 $Message" -ForegroundColor DarkMagenta }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Show-Header {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                 نظام إدارة التراخيص المتقدم                  ║" -ForegroundColor Magenta
    Write-Host "║            Advanced License Management System              ║" -ForegroundColor Magenta
    Write-Host "║                        الإصدار 1.0                         ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

# دالة تشفير متقدمة
function Encrypt-AdvancedString {
    param([string]$PlainText, [string]$Key = $MasterKey)
    
    try {
        # إضافة طوابع زمنية وتوقيعات
        $timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
        $signature = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($PlainText + $Key))
        $signatureString = [System.BitConverter]::ToString($signature) -replace "-", ""
        
        $dataToEncrypt = @{
            Data = $PlainText
            Timestamp = $timestamp
            Signature = $signatureString
            Version = $LicenseVersion
        } | ConvertTo-Json -Compress
        
        # تشفير AES-256
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($dataToEncrypt)
        $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
        
        $aes = [System.Security.Cryptography.AesCryptoServiceProvider]::new()
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        $aes.Key = (New-Object System.Security.Cryptography.SHA256Managed).ComputeHash($keyBytes)[0..31]
        $aes.GenerateIV()
        
        $encryptor = $aes.CreateEncryptor()
        $encryptedBytes = $encryptor.TransformFinalBlock($bytes, 0, $bytes.Length)
        
        # دمج IV مع البيانات المشفرة
        $result = $aes.IV + $encryptedBytes
        
        $aes.Dispose()
        return [Convert]::ToBase64String($result)
    }
    catch {
        Write-Status "خطأ في التشفير المتقدم: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة فك التشفير المتقدمة
function Decrypt-AdvancedString {
    param([string]$EncryptedText, [string]$Key = $MasterKey)
    
    try {
        $encryptedBytes = [Convert]::FromBase64String($EncryptedText)
        $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
        
        $aes = [System.Security.Cryptography.AesCryptoServiceProvider]::new()
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        $aes.Key = (New-Object System.Security.Cryptography.SHA256Managed).ComputeHash($keyBytes)[0..31]
        
        # استخراج IV من البيانات
        $aes.IV = $encryptedBytes[0..15]
        $cipherBytes = $encryptedBytes[16..($encryptedBytes.Length-1)]
        
        $decryptor = $aes.CreateDecryptor()
        $decryptedBytes = $decryptor.TransformFinalBlock($cipherBytes, 0, $cipherBytes.Length)
        
        $aes.Dispose()
        $decryptedJson = [System.Text.Encoding]::UTF8.GetString($decryptedBytes)
        
        # فك تشفير JSON والتحقق من التوقيع
        $decryptedData = $decryptedJson | ConvertFrom-Json
        
        # التحقق من التوقيع
        $expectedSignature = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($decryptedData.Data + $Key))
        $expectedSignatureString = [System.BitConverter]::ToString($expectedSignature) -replace "-", ""
        
        if ($decryptedData.Signature -ne $expectedSignatureString) {
            throw "فشل في التحقق من التوقيع"
        }
        
        return $decryptedData
    }
    catch {
        Write-Status "خطأ في فك التشفير المتقدم: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة الحصول على بصمة الجهاز المتقدمة
function Get-AdvancedHardwareFingerprint {
    Write-Status "جمع بصمة الجهاز المتقدمة..." "SECURITY"
    
    try {
        # جمع معلومات الأجهزة
        $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
        $motherboard = Get-WmiObject -Class Win32_BaseBoard | Select-Object -First 1
        $bios = Get-WmiObject -Class Win32_BIOS | Select-Object -First 1
        $system = Get-WmiObject -Class Win32_ComputerSystemProduct | Select-Object -First 1
        $memory = Get-WmiObject -Class Win32_PhysicalMemory | Measure-Object -Property Capacity -Sum
        $disk = Get-WmiObject -Class Win32_DiskDrive | Select-Object -First 1
        
        # إنشاء بصمة شاملة
        $hardwareInfo = @{
            CPU_ID = $cpu.ProcessorId
            CPU_Name = $cpu.Name
            Motherboard_Serial = $motherboard.SerialNumber
            Motherboard_Product = $motherboard.Product
            BIOS_Serial = $bios.SerialNumber
            BIOS_Version = $bios.SMBIOSBIOSVersion
            System_UUID = $system.UUID
            Computer_Name = $env:COMPUTERNAME
            Total_Memory = $memory.Sum
            Disk_Serial = $disk.SerialNumber
            OS_Version = [System.Environment]::OSVersion.VersionString
            User_Domain = $env:USERDOMAIN
        }
        
        # تحويل إلى JSON وحساب الهاش
        $hardwareJson = $hardwareInfo | ConvertTo-Json -Compress
        $hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($hardwareJson))
        $hashString = [System.BitConverter]::ToString($hash) -replace "-", ""
        
        Write-Status "تم جمع بصمة الجهاز المتقدمة بنجاح" "SUCCESS"
        return @{
            Fingerprint = $hashString
            Details = $hardwareInfo
        }
    }
    catch {
        Write-Status "خطأ في جمع بصمة الجهاز: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# دالة إنشاء ترخيص متقدم
function New-AdvancedLicense {
    param(
        [string]$Company,
        [string]$Email,
        [int]$Days,
        [string]$Type,
        [array]$Features = @("FullAccess", "Reports", "Backup", "MultiUser"),
        [int]$MaxUsers = 10
    )
    
    Write-Status "إنشاء ترخيص متقدم..." "LICENSE"
    
    # الحصول على بصمة الجهاز
    $hardwareInfo = Get-AdvancedHardwareFingerprint
    if (!$hardwareInfo) {
        Write-Status "فشل في الحصول على بصمة الجهاز" "ERROR"
        return $null
    }
    
    # إنشاء معرف ترخيص فريد
    $licenseId = [System.Guid]::NewGuid().ToString()
    
    # تحديد تاريخ الإصدار وانتهاء الصلاحية
    $issueDate = Get-Date
    $expiryDate = $issueDate.AddDays($Days)
    
    # إنشاء بيانات الترخيص
    $licenseData = @{
        LicenseId = $licenseId
        Version = $LicenseVersion
        Company = $Company
        Email = $Email
        Type = $Type
        Features = $Features
        MaxUsers = $MaxUsers
        IssueDate = $issueDate.ToString("yyyy-MM-dd HH:mm:ss")
        ExpiryDate = $expiryDate.ToString("yyyy-MM-dd HH:mm:ss")
        ValidityDays = $Days
        HardwareFingerprint = $hardwareInfo.Fingerprint
        HardwareDetails = $hardwareInfo.Details
        Developer = $DeveloperInfo
        Status = "Active"
        ActivationCount = 0
        MaxActivations = 1
    }
    
    # تحويل إلى JSON
    $licenseJson = $licenseData | ConvertTo-Json -Depth 10 -Compress
    
    # تشفير بيانات الترخيص
    $encryptedLicense = Encrypt-AdvancedString -PlainText $licenseJson
    
    if ($encryptedLicense) {
        Write-Status "تم إنشاء الترخيص بنجاح" "SUCCESS"
        Write-Status "معرف الترخيص: $licenseId" "INFO"
        Write-Status "صالح حتى: $($expiryDate.ToString('yyyy-MM-dd'))" "INFO"
        
        return @{
            LicenseKey = $encryptedLicense
            LicenseData = $licenseData
        }
    } else {
        Write-Status "فشل في تشفير الترخيص" "ERROR"
        return $null
    }
}

# دالة التحقق من الترخيص المتقدم
function Test-AdvancedLicense {
    param([string]$LicenseKey)
    
    Write-Status "التحقق من الترخيص المتقدم..." "LICENSE"
    
    try {
        # فك تشفير الترخيص
        $decryptedData = Decrypt-AdvancedString -EncryptedText $LicenseKey
        if (!$decryptedData) {
            return @{ Valid = $false; Reason = "فشل في فك تشفير الترخيص" }
        }
        
        # تحويل البيانات من JSON
        $licenseData = $decryptedData.Data | ConvertFrom-Json
        
        # التحقق من إصدار الترخيص
        if ($licenseData.Version -ne $LicenseVersion) {
            return @{ Valid = $false; Reason = "إصدار الترخيص غير متوافق" }
        }
        
        # التحقق من تاريخ انتهاء الصلاحية
        $expiryDate = [DateTime]::Parse($licenseData.ExpiryDate)
        $currentDate = Get-Date
        
        if ($currentDate -gt $expiryDate) {
            return @{ 
                Valid = $false
                Reason = "انتهت صلاحية الترخيص في $($licenseData.ExpiryDate)"
                ExpiredDays = ($currentDate - $expiryDate).Days
            }
        }
        
        # التحقق من بصمة الجهاز
        $currentHardware = Get-AdvancedHardwareFingerprint
        if (!$currentHardware) {
            return @{ Valid = $false; Reason = "فشل في الحصول على بصمة الجهاز الحالية" }
        }
        
        if ($licenseData.HardwareFingerprint -ne $currentHardware.Fingerprint) {
            return @{ 
                Valid = $false
                Reason = "الترخيص غير صالح لهذا الجهاز"
                ExpectedFingerprint = $licenseData.HardwareFingerprint.Substring(0,16) + "..."
                CurrentFingerprint = $currentHardware.Fingerprint.Substring(0,16) + "..."
            }
        }
        
        # التحقق من حالة الترخيص
        if ($licenseData.Status -ne "Active") {
            return @{ Valid = $false; Reason = "الترخيص غير نشط. الحالة: $($licenseData.Status)" }
        }
        
        # حساب الأيام المتبقية
        $daysRemaining = ($expiryDate - $currentDate).Days
        
        # إنشاء تقرير صحة الترخيص
        $validationResult = @{ 
            Valid = $true
            LicenseId = $licenseData.LicenseId
            Company = $licenseData.Company
            Email = $licenseData.Email
            Type = $licenseData.Type
            Features = $licenseData.Features
            MaxUsers = $licenseData.MaxUsers
            IssueDate = $licenseData.IssueDate
            ExpiryDate = $licenseData.ExpiryDate
            DaysRemaining = $daysRemaining
            Status = $licenseData.Status
            Developer = $licenseData.Developer
        }
        
        # تحذيرات انتهاء الصلاحية
        if ($daysRemaining -le 30) {
            $validationResult.Warning = "الترخيص سينتهي خلال $daysRemaining يوم"
        }
        
        if ($daysRemaining -le 7) {
            $validationResult.CriticalWarning = "الترخيص سينتهي خلال $daysRemaining يوم - يرجى التجديد فوراً"
        }
        
        Write-Status "تم التحقق من الترخيص بنجاح" "SUCCESS"
        return $validationResult
    }
    catch {
        return @{ 
            Valid = $false
            Reason = "خطأ في التحقق من الترخيص: $($_.Exception.Message)"
        }
    }
}

# دالة إنشاء ملف ترخيص
function New-LicenseFile {
    param(
        [string]$Company,
        [string]$Email,
        [int]$Days,
        [string]$Type,
        [string]$OutputPath = "system.license"
    )
    
    # إنشاء الترخيص
    $license = New-AdvancedLicense -Company $Company -Email $Email -Days $Days -Type $Type
    
    if (!$license) {
        Write-Status "فشل في إنشاء الترخيص" "ERROR"
        return $false
    }
    
    # إنشاء محتوى ملف الترخيص
    $licenseFileContent = @"
# ═══════════════════════════════════════════════════════════════════════════
#                        ترخيص نظام إدارة المؤسسات
#                    Enterprise Management System License
# ═══════════════════════════════════════════════════════════════════════════
#
# معرف الترخيص: $($license.LicenseData.LicenseId)
# License ID: $($license.LicenseData.LicenseId)
#
# الشركة: $Company
# Company: $Company
#
# البريد الإلكتروني: $Email
# Email: $Email
#
# نوع الترخيص: $Type
# License Type: $Type
#
# تاريخ الإصدار: $($license.LicenseData.IssueDate)
# Issue Date: $($license.LicenseData.IssueDate)
#
# تاريخ انتهاء الصلاحية: $($license.LicenseData.ExpiryDate)
# Expiry Date: $($license.LicenseData.ExpiryDate)
#
# صالح لمدة: $Days يوم
# Valid for: $Days days
#
# المميزات المتاحة: $($license.LicenseData.Features -join ', ')
# Available Features: $($license.LicenseData.Features -join ', ')
#
# الحد الأقصى للمستخدمين: $($license.LicenseData.MaxUsers)
# Maximum Users: $($license.LicenseData.MaxUsers)
#
# ═══════════════════════════════════════════════════════════════════════════
#                              معلومات المطور
#                            Developer Information
# ═══════════════════════════════════════════════════════════════════════════
#
# الشركة المطورة: $($DeveloperInfo.Company)
# Developer Company: $($DeveloperInfo.Company)
#
# البريد الإلكتروني: $($DeveloperInfo.Email)
# Email: $($DeveloperInfo.Email)
#
# الهاتف: $($DeveloperInfo.Phone)
# Phone: $($DeveloperInfo.Phone)
#
# الموقع الإلكتروني: $($DeveloperInfo.Website)
# Website: $($DeveloperInfo.Website)
#
# ═══════════════════════════════════════════════════════════════════════════
#                                 تحذيرات
#                                Warnings
# ═══════════════════════════════════════════════════════════════════════════
#
# تحذير: هذا الترخيص محمي بحقوق الطبع والنشر
# Warning: This license is protected by copyright
#
# لا تقم بتعديل أو نسخ أو توزيع هذا الملف
# Do not modify, copy, or distribute this file
#
# أي محاولة للتلاعب ستؤدي إلى إلغاء الترخيص
# Any tampering attempt will void the license
#
# للدعم الفني، يرجى الاتصال بالمطور
# For technical support, please contact the developer
#
# ═══════════════════════════════════════════════════════════════════════════

LICENSE_KEY_START
$($license.LicenseKey)
LICENSE_KEY_END

# ═══════════════════════════════════════════════════════════════════════════
# تم إنشاء هذا الترخيص تلقائياً في: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# This license was automatically generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# ═══════════════════════════════════════════════════════════════════════════
"@

    # حفظ ملف الترخيص
    try {
        $licenseFileContent | Out-File -FilePath $OutputPath -Encoding UTF8
        Write-Status "تم إنشاء ملف الترخيص: $OutputPath" "SUCCESS"
        
        # التحقق من الترخيص المُنشأ
        $validation = Test-AdvancedLicense -LicenseKey $license.LicenseKey
        if ($validation.Valid) {
            Write-Status "تم التحقق من صحة الترخيص المُنشأ" "SUCCESS"
            
            # عرض ملخص الترخيص
            Write-Host ""
            Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
            Write-Host "║                      ملخص الترخيص                          ║" -ForegroundColor Green
            Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
            Write-Host "معرف الترخيص: $($validation.LicenseId)" -ForegroundColor Cyan
            Write-Host "الشركة: $($validation.Company)" -ForegroundColor White
            Write-Host "النوع: $($validation.Type)" -ForegroundColor White
            Write-Host "صالح حتى: $($validation.ExpiryDate)" -ForegroundColor Yellow
            Write-Host "الأيام المتبقية: $($validation.DaysRemaining)" -ForegroundColor Green
            Write-Host "المميزات: $($validation.Features -join ', ')" -ForegroundColor Cyan
            Write-Host ""
        } else {
            Write-Status "خطأ في التحقق من الترخيص: $($validation.Reason)" "ERROR"
        }
        
        return $true
    }
    catch {
        Write-Status "خطأ في حفظ ملف الترخيص: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة التحقق من ملف الترخيص
function Test-LicenseFile {
    param([string]$LicenseFilePath = "system.license")
    
    if (!(Test-Path $LicenseFilePath)) {
        Write-Status "ملف الترخيص غير موجود: $LicenseFilePath" "ERROR"
        return @{ Valid = $false; Reason = "ملف الترخيص غير موجود" }
    }
    
    try {
        # قراءة ملف الترخيص
        $content = Get-Content $LicenseFilePath -Raw -Encoding UTF8
        
        # استخراج مفتاح الترخيص
        if ($content -match "LICENSE_KEY_START\s*(.*?)\s*LICENSE_KEY_END") {
            $licenseKey = $matches[1].Trim()
            
            # التحقق من الترخيص
            return Test-AdvancedLicense -LicenseKey $licenseKey
        } else {
            return @{ Valid = $false; Reason = "تنسيق ملف الترخيص غير صحيح" }
        }
    }
    catch {
        return @{ Valid = $false; Reason = "خطأ في قراءة ملف الترخيص: $($_.Exception.Message)" }
    }
}

# القائمة التفاعلية
function Show-LicenseMenu {
    do {
        Show-Header
        
        Write-Status "🎫 خيارات إدارة التراخيص:" "LICENSE"
        Write-Host ""
        Write-Host "[1] 🆕 إنشاء ترخيص جديد" -ForegroundColor Cyan
        Write-Host "[2] ✅ التحقق من ترخيص موجود" -ForegroundColor Cyan
        Write-Host "[3] 📄 التحقق من ملف ترخيص" -ForegroundColor Cyan
        Write-Host "[4] 🖥️  عرض بصمة الجهاز" -ForegroundColor Cyan
        Write-Host "[5] 🔑 إنشاء مفتاح رئيسي جديد" -ForegroundColor Cyan
        Write-Host "[6] 📊 إحصائيات التراخيص" -ForegroundColor Cyan
        Write-Host "[7] 📋 معلومات النظام" -ForegroundColor Cyan
        Write-Host "[0] ❌ خروج" -ForegroundColor Yellow
        Write-Host ""
        
        $choice = Read-Host "اختر رقماً (0-7)"
        
        switch ($choice) {
            "1" { 
                Write-Host ""
                $company = Read-Host "أدخل اسم الشركة"
                $email = Read-Host "أدخل البريد الإلكتروني"
                $days = Read-Host "أدخل عدد أيام الصلاحية (افتراضي: 365)"
                if ($days -eq "") { $days = 365 }
                
                Write-Host ""
                Write-Host "أنواع التراخيص المتاحة:" -ForegroundColor Yellow
                Write-Host "1. Trial (تجريبي)" -ForegroundColor White
                Write-Host "2. Standard (قياسي)" -ForegroundColor White
                Write-Host "3. Professional (احترافي)" -ForegroundColor White
                Write-Host "4. Enterprise (مؤسسي)" -ForegroundColor White
                
                $typeChoice = Read-Host "اختر نوع الترخيص (1-4)"
                $licenseTypes = @("Trial", "Standard", "Professional", "Enterprise")
                $type = $licenseTypes[[int]$typeChoice - 1]
                
                New-LicenseFile -Company $company -Email $email -Days ([int]$days) -Type $type
                Read-Host "اضغط Enter للمتابعة"
            }
            "2" { 
                $licenseKey = Read-Host "أدخل مفتاح الترخيص"
                if ($licenseKey) {
                    $validation = Test-AdvancedLicense -LicenseKey $licenseKey
                    Show-LicenseValidationResult -ValidationResult $validation
                }
                Read-Host "اضغط Enter للمتابعة"
            }
            "3" { 
                $filePath = Read-Host "أدخل مسار ملف الترخيص (أو اضغط Enter للافتراضي)"
                if ($filePath -eq "") { $filePath = "system.license" }
                
                $validation = Test-LicenseFile -LicenseFilePath $filePath
                Show-LicenseValidationResult -ValidationResult $validation
                Read-Host "اضغط Enter للمتابعة"
            }
            "4" { 
                $hardware = Get-AdvancedHardwareFingerprint
                if ($hardware) {
                    Show-HardwareInfo -HardwareInfo $hardware
                }
                Read-Host "اضغط Enter للمتابعة"
            }
            "5" { 
                $newKey = -join ((65..90) + (97..122) + (48..57) + (33,35,36,37,38,42,43,45,61,63,64,94) | Get-Random -Count 48 | ForEach-Object {[char]$_})
                Write-Status "المفتاح الرئيسي الجديد:" "SUCCESS"
                Write-Host $newKey -ForegroundColor Yellow
                Write-Status "احفظ هذا المفتاح في مكان آمن جداً" "WARNING"
                Read-Host "اضغط Enter للمتابعة"
            }
            "6" { 
                Show-LicenseStatistics
                Read-Host "اضغط Enter للمتابعة"
            }
            "7" { 
                Show-SystemInfo
                Read-Host "اضغط Enter للمتابعة"
            }
            "0" { 
                Write-Status "👋 شكراً لاستخدام نظام إدارة التراخيص!" "SUCCESS"
                return 
            }
            default { 
                Write-Status "❌ اختيار غير صحيح" "ERROR"
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

function Show-LicenseValidationResult {
    param($ValidationResult)
    
    Write-Host ""
    if ($ValidationResult.Valid) {
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
        Write-Host "║                    ✅ الترخيص صالح                          ║" -ForegroundColor Green
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
        Write-Host ""
        Write-Host "معرف الترخيص: $($ValidationResult.LicenseId)" -ForegroundColor Cyan
        Write-Host "الشركة: $($ValidationResult.Company)" -ForegroundColor White
        Write-Host "البريد الإلكتروني: $($ValidationResult.Email)" -ForegroundColor White
        Write-Host "نوع الترخيص: $($ValidationResult.Type)" -ForegroundColor White
        Write-Host "تاريخ الإصدار: $($ValidationResult.IssueDate)" -ForegroundColor White
        Write-Host "تاريخ انتهاء الصلاحية: $($ValidationResult.ExpiryDate)" -ForegroundColor Yellow
        Write-Host "الأيام المتبقية: $($ValidationResult.DaysRemaining)" -ForegroundColor Green
        Write-Host "المميزات المتاحة: $($ValidationResult.Features -join ', ')" -ForegroundColor Cyan
        Write-Host "الحد الأقصى للمستخدمين: $($ValidationResult.MaxUsers)" -ForegroundColor White
        Write-Host "الحالة: $($ValidationResult.Status)" -ForegroundColor Green
        
        if ($ValidationResult.Warning) {
            Write-Host ""
            Write-Host "⚠️  تحذير: $($ValidationResult.Warning)" -ForegroundColor Yellow
        }
        
        if ($ValidationResult.CriticalWarning) {
            Write-Host ""
            Write-Host "🚨 تحذير هام: $($ValidationResult.CriticalWarning)" -ForegroundColor Red
        }
    } else {
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Red
        Write-Host "║                    ❌ الترخيص غير صالح                      ║" -ForegroundColor Red
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Red
        Write-Host ""
        Write-Host "السبب: $($ValidationResult.Reason)" -ForegroundColor Red
        
        if ($ValidationResult.ExpectedFingerprint) {
            Write-Host "بصمة الجهاز المتوقعة: $($ValidationResult.ExpectedFingerprint)" -ForegroundColor Yellow
            Write-Host "بصمة الجهاز الحالية: $($ValidationResult.CurrentFingerprint)" -ForegroundColor Yellow
        }
        
        if ($ValidationResult.ExpiredDays) {
            Write-Host "انتهت الصلاحية منذ: $($ValidationResult.ExpiredDays) يوم" -ForegroundColor Red
        }
        
        Write-Host ""
        Write-Host "للحصول على ترخيص صالح، يرجى الاتصال بـ:" -ForegroundColor Cyan
        Write-Host "البريد الإلكتروني: $($DeveloperInfo.Email)" -ForegroundColor White
        Write-Host "الهاتف: $($DeveloperInfo.Phone)" -ForegroundColor White
    }
}

function Show-HardwareInfo {
    param($HardwareInfo)
    
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                       معلومات الجهاز                        ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🖥️  بصمة الجهاز:" -ForegroundColor Yellow
    Write-Host $HardwareInfo.Fingerprint -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 تفاصيل الجهاز:" -ForegroundColor Yellow
    Write-Host "اسم الكمبيوتر: $($HardwareInfo.Details.Computer_Name)" -ForegroundColor White
    Write-Host "معالج: $($HardwareInfo.Details.CPU_Name)" -ForegroundColor White
    Write-Host "معرف المعالج: $($HardwareInfo.Details.CPU_ID)" -ForegroundColor White
    Write-Host "اللوحة الأم: $($HardwareInfo.Details.Motherboard_Product)" -ForegroundColor White
    Write-Host "إصدار BIOS: $($HardwareInfo.Details.BIOS_Version)" -ForegroundColor White
    Write-Host "معرف النظام: $($HardwareInfo.Details.System_UUID)" -ForegroundColor White
    Write-Host "إجمالي الذاكرة: $([math]::Round($HardwareInfo.Details.Total_Memory / 1GB, 2)) GB" -ForegroundColor White
    Write-Host "إصدار النظام: $($HardwareInfo.Details.OS_Version)" -ForegroundColor White
}

function Show-LicenseStatistics {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                     إحصائيات التراخيص                       ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
    
    # فحص ملفات التراخيص الموجودة
    $licenseFiles = Get-ChildItem -Path "." -Filter "*.license" -ErrorAction SilentlyContinue
    
    Write-Host "📊 إحصائيات عامة:" -ForegroundColor Yellow
    Write-Host "عدد ملفات التراخيص الموجودة: $($licenseFiles.Count)" -ForegroundColor White
    Write-Host "إصدار نظام التراخيص: $LicenseVersion" -ForegroundColor White
    Write-Host "تاريخ آخر فحص: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    
    if ($licenseFiles.Count -gt 0) {
        Write-Host ""
        Write-Host "📄 ملفات التراخيص:" -ForegroundColor Yellow
        foreach ($file in $licenseFiles) {
            $validation = Test-LicenseFile -LicenseFilePath $file.FullName
            $status = if ($validation.Valid) { "✅ صالح" } else { "❌ غير صالح" }
            Write-Host "   $($file.Name): $status" -ForegroundColor White
        }
    }
}

function Show-SystemInfo {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                      معلومات النظام                         ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "🏢 معلومات المطور:" -ForegroundColor Yellow
    Write-Host "الشركة: $($DeveloperInfo.Company)" -ForegroundColor White
    Write-Host "البريد الإلكتروني: $($DeveloperInfo.Email)" -ForegroundColor White
    Write-Host "الهاتف: $($DeveloperInfo.Phone)" -ForegroundColor White
    Write-Host "الموقع: $($DeveloperInfo.Website)" -ForegroundColor White
    Write-Host ""
    
    Write-Host "⚙️  معلومات النظام:" -ForegroundColor Yellow
    Write-Host "إصدار نظام التراخيص: $LicenseVersion" -ForegroundColor White
    Write-Host "تاريخ التشغيل: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "مسار العمل: $(Get-Location)" -ForegroundColor White
    Write-Host "إصدار PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor White
}

# البرنامج الرئيسي
function Main {
    # معالجة المعاملات
    if ($GenerateLicense) {
        Show-Header
        if ($CompanyName -eq "") {
            $CompanyName = Read-Host "أدخل اسم الشركة"
        }
        if ($ContactEmail -eq "") {
            $ContactEmail = Read-Host "أدخل البريد الإلكتروني"
        }
        New-LicenseFile -Company $CompanyName -Email $ContactEmail -Days $ValidityDays -Type $LicenseType
        return
    }
    
    if ($ValidateLicense) {
        Show-Header
        $validation = Test-LicenseFile
        Show-LicenseValidationResult -ValidationResult $validation
        return
    }
    
    if ($CreateMasterKey) {
        Show-Header
        $newKey = -join ((65..90) + (97..122) + (48..57) + (33,35,36,37,38,42,43,45,61,63,64,94) | Get-Random -Count 48 | ForEach-Object {[char]$_})
        Write-Status "المفتاح الرئيسي الجديد: $newKey" "SUCCESS"
        return
    }
    
    # عرض القائمة التفاعلية
    Show-LicenseMenu
}

# تشغيل البرنامج الرئيسي
Main
