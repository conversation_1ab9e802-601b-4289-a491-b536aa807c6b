# نظام إدارة المؤسسات الشامل - ملف التشغيل الرئيسي
# Enterprise Management System - Main Launcher
# الإصدار 1.0 - Version 1.0

param(
    [switch]$QuickStart,
    [switch]$Setup,
    [switch]$Backup,
    [switch]$GUI
)

# إعداد الترميز للعربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "نظام إدارة المؤسسات - Enterprise Management System"

# متغيرات النظام
$SystemName = "نظام إدارة المؤسسات الشامل"
$SystemVersion = "1.0"
$DatabaseFile = "نظام_إدارة_المؤسسات.accdb"

# ألوان النص
function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    
    $Colors = @{
        Success = "Green"
        Warning = "Yellow" 
        Error = "Red"
        Info = "Cyan"
        Header = "Magenta"
    }
    
    if ($Colors.ContainsKey($Color)) {
        Write-Host $Text -ForegroundColor $Colors[$Color]
    } else {
        Write-Host $Text -ForegroundColor $Color
    }
}

function Show-Header {
    Clear-Host
    Write-ColorText "╔══════════════════════════════════════════════════════════════╗" "Header"
    Write-ColorText "║                نظام إدارة المؤسسات الشامل                    ║" "Header"
    Write-ColorText "║              Enterprise Management System                   ║" "Header"
    Write-ColorText "║                     الإصدار $SystemVersion                        ║" "Header"
    Write-ColorText "╚══════════════════════════════════════════════════════════════╝" "Header"
    Write-Host ""
    Write-ColorText "🔑 بيانات تسجيل الدخول الافتراضية:" "Warning"
    Write-ColorText "   اسم المستخدم: admin" "Info"
    Write-ColorText "   كلمة المرور: 12345" "Info"
    Write-Host ""
}

function Test-SystemRequirements {
    Write-ColorText "🔍 فحص متطلبات النظام..." "Info"
    
    # فحص Microsoft Access
    try {
        $accessApp = New-Object -ComObject Access.Application
        $version = $accessApp.Version
        $accessApp.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($accessApp) | Out-Null
        Write-ColorText "✅ Microsoft Access $version متوفر" "Success"
        return $true
    }
    catch {
        Write-ColorText "❌ Microsoft Access غير مثبت أو غير متاح" "Error"
        Write-ColorText "   يرجى تثبيت Microsoft Access 2016 أو أحدث" "Warning"
        return $false
    }
}

function Test-DatabaseFile {
    if (Test-Path $DatabaseFile) {
        $fileSize = (Get-Item $DatabaseFile).Length / 1MB
        Write-ColorText "✅ ملف قاعدة البيانات موجود ($('{0:N2}' -f $fileSize) MB)" "Success"
        return $true
    } else {
        Write-ColorText "⚠️ لم يتم العثور على ملف قاعدة البيانات" "Warning"
        return $false
    }
}

function Start-QuickLaunch {
    Write-ColorText "🚀 تشغيل سريع للنظام..." "Info"
    
    if (Test-DatabaseFile) {
        try {
            Start-Process "msaccess.exe" -ArgumentList "`"$DatabaseFile`"" -WindowStyle Maximized
            Write-ColorText "✅ تم تشغيل النظام بنجاح!" "Success"
            Write-ColorText "📋 استخدم بيانات تسجيل الدخول المذكورة أعلاه" "Info"
            return $true
        }
        catch {
            Write-ColorText "❌ خطأ في تشغيل النظام: $($_.Exception.Message)" "Error"
            return $false
        }
    } else {
        Write-ColorText "❌ لا يمكن تشغيل النظام بدون ملف قاعدة البيانات" "Error"
        Write-ColorText "   استخدم المعامل -Setup لإعداد النظام أولاً" "Info"
        return $false
    }
}

function Start-SystemSetup {
    Write-ColorText "⚙️ إعداد النظام..." "Info"
    
    if (Test-Path "Setup-System.ps1") {
        try {
            & ".\Setup-System.ps1" -FullSetup
            return $true
        }
        catch {
            Write-ColorText "❌ خطأ في تشغيل الإعداد: $($_.Exception.Message)" "Error"
            return $false
        }
    } else {
        Write-ColorText "❌ ملف الإعداد غير موجود: Setup-System.ps1" "Error"
        return $false
    }
}

function New-BackupFile {
    Write-ColorText "💾 إنشاء نسخة احتياطية..." "Info"
    
    if (!(Test-Path $DatabaseFile)) {
        Write-ColorText "❌ لا يمكن العثور على ملف قاعدة البيانات للنسخ الاحتياطي" "Error"
        return $false
    }
    
    $backupDir = "Backups"
    if (!(Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir | Out-Null
        Write-ColorText "📁 تم إنشاء مجلد النسخ الاحتياطية" "Info"
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupPath = Join-Path $backupDir "نظام_إدارة_المؤسسات_backup_$timestamp.accdb"
    
    try {
        Copy-Item $DatabaseFile $backupPath
        $backupSize = (Get-Item $backupPath).Length / 1MB
        Write-ColorText "✅ تم إنشاء النسخة الاحتياطية بنجاح" "Success"
        Write-ColorText "📁 المسار: $backupPath" "Info"
        Write-ColorText "📊 الحجم: $('{0:N2}' -f $backupSize) MB" "Info"
        return $true
    }
    catch {
        Write-ColorText "❌ فشل في إنشاء النسخة الاحتياطية: $($_.Exception.Message)" "Error"
        return $false
    }
}

function Show-InteractiveMenu {
    do {
        Show-Header
        
        Write-ColorText "🎯 اختر العملية المطلوبة:" "Header"
        Write-Host ""
        Write-ColorText "[1] 🚀 تشغيل سريع للنظام" "Info"
        Write-ColorText "[2] 🔧 تشغيل متقدم مع فحص شامل" "Info"
        Write-ColorText "[3] ⚙️ إعداد النظام الكامل" "Info"
        Write-ColorText "[4] 💾 إنشاء نسخة احتياطية" "Info"
        Write-ColorText "[5] 📋 معلومات النظام" "Info"
        Write-ColorText "[6] 🖥️ واجهة رسومية" "Info"
        Write-ColorText "[0] ❌ خروج" "Warning"
        Write-Host ""
        
        $choice = Read-Host "اختر رقماً (0-6)"
        
        switch ($choice) {
            "1" { 
                if (Test-SystemRequirements) {
                    Start-QuickLaunch
                    Read-Host "اضغط Enter للمتابعة"
                }
            }
            "2" { 
                if (Test-Path "Start-EnterpriseSystem.ps1") {
                    & ".\Start-EnterpriseSystem.ps1"
                } else {
                    Write-ColorText "❌ ملف التشغيل المتقدم غير موجود" "Error"
                }
                Read-Host "اضغط Enter للمتابعة"
            }
            "3" { 
                Start-SystemSetup
                Read-Host "اضغط Enter للمتابعة"
            }
            "4" { 
                New-BackupFile
                Read-Host "اضغط Enter للمتابعة"
            }
            "5" { 
                Show-SystemInfo
                Read-Host "اضغط Enter للمتابعة"
            }
            "6" { 
                Start-GUIMode
                Read-Host "اضغط Enter للمتابعة"
            }
            "0" { 
                Write-ColorText "👋 شكراً لاستخدام نظام إدارة المؤسسات!" "Success"
                return 
            }
            default { 
                Write-ColorText "❌ اختيار غير صحيح. يرجى المحاولة مرة أخرى." "Error"
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

function Show-SystemInfo {
    Clear-Host
    Write-ColorText "╔══════════════════════════════════════════════════════════════╗" "Header"
    Write-ColorText "║                        معلومات النظام                        ║" "Header"
    Write-ColorText "╚══════════════════════════════════════════════════════════════╝" "Header"
    Write-Host ""
    
    Write-ColorText "📋 تفاصيل النظام:" "Info"
    Write-ColorText "   • الاسم: $SystemName" "White"
    Write-ColorText "   • الإصدار: $SystemVersion" "White"
    Write-ColorText "   • تاريخ التشغيل: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "White"
    Write-ColorText "   • مسار النظام: $(Get-Location)" "White"
    Write-Host ""
    
    Write-ColorText "📁 ملفات النظام:" "Info"
    if (Test-Path $DatabaseFile) {
        $dbSize = (Get-Item $DatabaseFile).Length / 1MB
        Write-ColorText "   ✅ قاعدة البيانات: موجودة ($('{0:N2}' -f $dbSize) MB)" "Success"
    } else {
        Write-ColorText "   ❌ قاعدة البيانات: غير موجودة" "Error"
    }
    
    $files = @("Complete_Database_Script.sql", "Start-EnterpriseSystem.ps1", "Setup-System.ps1", "تشغيل_سريع.cmd")
    foreach ($file in $files) {
        if (Test-Path $file) {
            Write-ColorText "   ✅ $file: موجود" "Success"
        } else {
            Write-ColorText "   ❌ $file: غير موجود" "Warning"
        }
    }
    
    Write-Host ""
    Write-ColorText "📂 مجلدات النظام:" "Info"
    $folders = @("Backups", "Reports", "Logs", "Temp")
    foreach ($folder in $folders) {
        if (Test-Path $folder) {
            $itemCount = (Get-ChildItem $folder -ErrorAction SilentlyContinue).Count
            Write-ColorText "   ✅ $folder: موجود ($itemCount عنصر)" "Success"
        } else {
            Write-ColorText "   ❌ $folder: غير موجود" "Warning"
        }
    }
    
    Write-Host ""
    Write-ColorText "💻 معلومات النظام:" "Info"
    Write-ColorText "   • نظام التشغيل: $($env:OS)" "White"
    Write-ColorText "   • اسم الكمبيوتر: $($env:COMPUTERNAME)" "White"
    Write-ColorText "   • المستخدم الحالي: $($env:USERNAME)" "White"
    Write-ColorText "   • PowerShell: $($PSVersionTable.PSVersion)" "White"
}

function Start-GUIMode {
    Write-ColorText "🖥️ تشغيل الواجهة الرسومية..." "Info"
    
    if (Test-Path "EnterpriseSystemLauncher.ps1") {
        try {
            & ".\EnterpriseSystemLauncher.ps1"
        }
        catch {
            Write-ColorText "❌ خطأ في تشغيل الواجهة الرسومية: $($_.Exception.Message)" "Error"
        }
    } else {
        Write-ColorText "❌ ملف الواجهة الرسومية غير موجود" "Error"
        Write-ColorText "   يرجى تشغيل Create_EXE_Launcher.ps1 أولاً" "Info"
    }
}

# البرنامج الرئيسي
function Main {
    # معالجة المعاملات
    if ($QuickStart) {
        Show-Header
        if (Test-SystemRequirements) {
            Start-QuickLaunch
        }
        return
    }
    
    if ($Setup) {
        Show-Header
        Start-SystemSetup
        return
    }
    
    if ($Backup) {
        Show-Header
        New-BackupFile
        return
    }
    
    if ($GUI) {
        Start-GUIMode
        return
    }
    
    # عرض القائمة التفاعلية
    Show-InteractiveMenu
}

# تشغيل البرنامج الرئيسي
Main
