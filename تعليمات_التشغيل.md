# تعليمات تشغيل النظام 🚀
## نظام إدارة المؤسسات الشامل

### 📋 نظرة عامة

يحتوي هذا المجلد على عدة ملفات لتشغيل نظام إدارة المؤسسات بطرق مختلفة حسب احتياجاتك ومستوى خبرتك.

---

### 🎯 ملفات التشغيل المتاحة

| الملف | الوصف | مُوصى للـ |
|-------|--------|-----------|
| **تشغيل_سريع.cmd** | تشغيل سريع ومبسط مع قائمة تفاعلية | المستخدمين العاديين |
| **تشغيل_النظام.bat** | تشغيل أساسي مع فحص المتطلبات | المستخدمين المبتدئين |
| **Start-EnterpriseSystem.ps1** | تشغيل متقدم مع فحص شامل | المديرين والمطورين |
| **Setup-System.ps1** | إعداد تلقائي كامل للنظام | التثبيت الأولي |

---

### 🚀 طرق التشغيل

#### 1️⃣ التشغيل السريع (مُوصى به للمستخدمين العاديين)

```
انقر نقراً مزدوجاً على: تشغيل_سريع.cmd
```

**المميزات:**
- ✅ واجهة تفاعلية بسيطة
- ✅ قائمة خيارات واضحة
- ✅ تشغيل سريع بدون تعقيدات
- ✅ إنشاء نسخ احتياطية
- ✅ إصلاح قاعدة البيانات
- ✅ عرض معلومات النظام

**الخيارات المتاحة:**
- **[1] تشغيل سريع:** تشغيل فوري للنظام
- **[2] تشغيل متقدم:** فحص شامل قبل التشغيل
- **[3] نسخة احتياطية:** إنشاء نسخة احتياطية
- **[4] إصلاح قاعدة البيانات:** إصلاح وضغط
- **[5] معلومات النظام:** عرض تفاصيل النظام

#### 2️⃣ التشغيل المتقدم (للمديرين والمطورين)

```powershell
# فتح PowerShell كمدير ثم تشغيل:
.\Start-EnterpriseSystem.ps1

# أو مع معاملات:
.\Start-EnterpriseSystem.ps1 -CreateNew    # إنشاء قاعدة بيانات جديدة
.\Start-EnterpriseSystem.ps1 -Backup      # إنشاء نسخة احتياطية فقط
.\Start-EnterpriseSystem.ps1 -Repair      # إصلاح قاعدة البيانات
```

**المميزات:**
- ✅ فحص شامل للمتطلبات
- ✅ اختبار سلامة قاعدة البيانات
- ✅ إنشاء مجلدات النظام تلقائياً
- ✅ فحص الصلاحيات والأمان
- ✅ تقارير مفصلة عن حالة النظام
- ✅ إصلاح تلقائي للمشاكل

#### 3️⃣ الإعداد التلقائي (للتثبيت الأولي)

```powershell
# للإعداد الكامل:
.\Setup-System.ps1 -FullSetup

# لإنشاء قاعدة البيانات فقط:
.\Setup-System.ps1 -DatabaseOnly

# للنماذج فقط:
.\Setup-System.ps1 -FormsOnly
```

**يقوم بـ:**
- ✅ إنشاء قاعدة البيانات من الصفر
- ✅ تنفيذ جميع سكريبت الجداول
- ✅ إنشاء هيكل المجلدات
- ✅ إنشاء المستخدم الافتراضي
- ✅ إنشاء اختصار سطح المكتب
- ✅ اختبار التثبيت

---

### 🔑 بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: 12345
```

**⚠️ مهم جداً:** تأكد من تغيير كلمة المرور بعد أول تسجيل دخول!

---

### 📁 هيكل المجلدات

```
نظام_إدارة_المؤسسات/
├── 📄 نظام_إدارة_المؤسسات.accdb (قاعدة البيانات الرئيسية)
├── 🚀 تشغيل_سريع.cmd (التشغيل السريع)
├── 🚀 تشغيل_النظام.bat (التشغيل الأساسي)
├── 🚀 Start-EnterpriseSystem.ps1 (التشغيل المتقدم)
├── ⚙️ Setup-System.ps1 (الإعداد التلقائي)
├── 📊 Complete_Database_Script.sql (سكريبت قاعدة البيانات)
├── 💻 Access_Complete_VBA_Code.bas (كود VBA)
├── 📁 Backups/ (النسخ الاحتياطية)
├── 📁 Reports/ (التقارير المُصدرة)
├── 📁 Logs/ (سجلات النظام)
├── 📁 Temp/ (الملفات المؤقتة)
└── 📁 Documentation/ (الوثائق والأدلة)
```

---

### 🔧 استكشاف الأخطاء

#### المشكلة: "Microsoft Access غير مثبت"
**الحل:**
```
1. تأكد من تثبيت Microsoft Access 2016 أو أحدث
2. أعد تشغيل الكمبيوتر بعد التثبيت
3. جرب تشغيل Access يدوياً للتأكد من عمله
```

#### المشكلة: "لم يتم العثور على ملف قاعدة البيانات"
**الحل:**
```
1. استخدم Setup-System.ps1 لإنشاء قاعدة بيانات جديدة
2. أو استخدم الخيار [3] في تشغيل_سريع.cmd
3. تأكد من وجود ملف Complete_Database_Script.sql
```

#### المشكلة: "خطأ في الصلاحيات"
**الحل:**
```
1. شغل الملف كمدير (Run as Administrator)
2. أو انقل الملفات لمجلد Documents
3. تأكد من عدم حماية المجلد بواسطة Antivirus
```

#### المشكلة: "قاعدة البيانات تالفة"
**الحل:**
```
1. استخدم الخيار [4] في تشغيل_سريع.cmd للإصلاح
2. أو استخدم Start-EnterpriseSystem.ps1 -Repair
3. استعد من نسخة احتياطية إذا لزم الأمر
```

---

### 📞 الدعم الفني

#### 🆘 الحصول على المساعدة:

**1. الأسئلة الشائعة:**
- راجع ملف "معلومات_التثبيت.txt" بعد الإعداد
- اقرأ ملف README.md للمعلومات الشاملة

**2. المشاكل التقنية:**
- استخدم الخيار [5] في تشغيل_سريع.cmd لعرض معلومات النظام
- احفظ محتوى مجلد Logs وأرسله مع طلب الدعم

**3. التواصل:**
```
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: +966-XX-XXXXXXX
🌐 الموقع: www.enterprise-system.com
🕐 ساعات العمل: 8:00 ص - 5:00 م (السبت - الخميس)
```

---

### 🎯 نصائح للاستخدام الأمثل

#### للمستخدمين الجدد:
1. ابدأ بـ **تشغيل_سريع.cmd**
2. استخدم الخيار [1] للتشغيل السريع
3. غير كلمة المرور فور الدخول
4. أنشئ نسخة احتياطية أسبوعياً

#### للمديرين:
1. استخدم **Start-EnterpriseSystem.ps1** للفحص الدوري
2. راقب مجلد Logs للأخطاء
3. أنشئ نسخ احتياطية تلقائية
4. راجع إعدادات الأمان دورياً

#### للمطورين:
1. استخدم **Setup-System.ps1** للبيئات الجديدة
2. راجع ملف Access_Complete_VBA_Code.bas للتخصيص
3. استخدم Complete_Database_Script.sql للتعديلات
4. احتفظ بنسخ من التخصيصات

---

### 📈 التحديثات والصيانة

#### الصيانة الدورية:
- **يومياً:** فحص سجلات الأخطاء
- **أسبوعياً:** إنشاء نسخة احتياطية
- **شهرياً:** إصلاح وضغط قاعدة البيانات
- **ربع سنوياً:** مراجعة الأمان والصلاحيات

#### التحديثات:
- تحقق من التحديثات على الموقع الرسمي
- احتفظ بنسخة احتياطية قبل أي تحديث
- اقرأ ملاحظات الإصدار قبل التحديث

---

### 🎉 مبروك!

**نظام إدارة المؤسسات الشامل جاهز للاستخدام!**

اختر طريقة التشغيل المناسبة لك وابدأ رحلتك في إدارة مؤسستك بكفاءة واحترافية.

*تم تطويره بعناية فائقة لخدمة المؤسسات العربية* 🇸🇦
