-- Enterprise Management System - Table Creation Script
-- Microsoft Access SQL Script for creating all system tables
-- Execute this script in Access Query Design View (SQL View)

-- ============================================================================
-- USER MANAGEMENT TABLES
-- ============================================================================

-- Create Users Table
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT CONSTRAINT PK_Users PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    RoleID LONG,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME,
    CONSTRAINT UK_Users_Username UNIQUE (Username),
    CONSTRAINT UK_Users_Email UNIQUE (Email)
);

-- Create Roles Table
CREATE TABLE tbl_Roles (
    RoleID AUTOINCREMENT CONSTRAINT PK_Roles PRIMARY KEY,
    RoleName TEXT(50) NOT NULL,
    Description TEXT(255),
    Permissions MEMO,
    IsActive YESNO DEFAULT Yes,
    CONSTRAINT UK_Roles_RoleName UNIQUE (RoleName)
);

-- Create Permissions Table
CREATE TABLE tbl_Permissions (
    PermissionID AUTOINCREMENT CONSTRAINT PK_Permissions PRIMARY KEY,
    ModuleName TEXT(50) NOT NULL,
    Action TEXT(20) NOT NULL,
    Description TEXT(255)
);

-- Create User Roles Junction Table
CREATE TABLE tbl_UserRoles (
    UserRoleID AUTOINCREMENT CONSTRAINT PK_UserRoles PRIMARY KEY,
    UserID LONG NOT NULL,
    RoleID LONG NOT NULL,
    AssignedDate DATETIME DEFAULT Now(),
    AssignedBy LONG
);

-- Create Role Permissions Junction Table
CREATE TABLE tbl_RolePermissions (
    RolePermissionID AUTOINCREMENT CONSTRAINT PK_RolePermissions PRIMARY KEY,
    RoleID LONG NOT NULL,
    PermissionID LONG NOT NULL,
    GrantedDate DATETIME DEFAULT Now(),
    GrantedBy LONG
);

-- ============================================================================
-- CLIENT MANAGEMENT TABLES
-- ============================================================================

-- Create Clients Table
CREATE TABLE tbl_Clients (
    ClientID AUTOINCREMENT CONSTRAINT PK_Clients PRIMARY KEY,
    ClientCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Website TEXT(200),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    CreditLimit CURRENCY DEFAULT 0,
    PaymentTerms LONG DEFAULT 30,
    ClientType TEXT(20) DEFAULT "Customer",
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Clients_ClientCode UNIQUE (ClientCode)
);

-- Create Client Contacts Table
CREATE TABLE tbl_ClientContacts (
    ContactID AUTOINCREMENT CONSTRAINT PK_ClientContacts PRIMARY KEY,
    ClientID LONG NOT NULL,
    ContactName TEXT(100) NOT NULL,
    Position TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    IsPrimary YESNO DEFAULT No
);

-- ============================================================================
-- SUPPLIER MANAGEMENT TABLES
-- ============================================================================

-- Create Suppliers Table
CREATE TABLE tbl_Suppliers (
    SupplierID AUTOINCREMENT CONSTRAINT PK_Suppliers PRIMARY KEY,
    SupplierCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    PaymentTerms LONG DEFAULT 30,
    Currency TEXT(3) DEFAULT "USD",
    Rating LONG DEFAULT 0,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO,
    CONSTRAINT UK_Suppliers_SupplierCode UNIQUE (SupplierCode)
);

-- ============================================================================
-- PRODUCT AND INVENTORY TABLES
-- ============================================================================

-- Create Categories Table
CREATE TABLE tbl_Categories (
    CategoryID AUTOINCREMENT CONSTRAINT PK_Categories PRIMARY KEY,
    CategoryName TEXT(100) NOT NULL,
    ParentCategoryID LONG,
    Description TEXT(255),
    IsActive YESNO DEFAULT Yes,
    CONSTRAINT UK_Categories_CategoryName UNIQUE (CategoryName)
);

-- Create Products Table
CREATE TABLE tbl_Products (
    ProductID AUTOINCREMENT CONSTRAINT PK_Products PRIMARY KEY,
    ProductCode TEXT(50) NOT NULL,
    ProductName TEXT(200) NOT NULL,
    CategoryID LONG,
    Description MEMO,
    UnitOfMeasure TEXT(20) DEFAULT "Each",
    CostPrice CURRENCY DEFAULT 0,
    SellingPrice CURRENCY DEFAULT 0,
    MinimumStock LONG DEFAULT 0,
    ReorderPoint LONG DEFAULT 0,
    Barcode TEXT(100),
    IsActive YESNO DEFAULT Yes,
    IsService YESNO DEFAULT No,
    SupplierID LONG,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    CONSTRAINT UK_Products_ProductCode UNIQUE (ProductCode)
);

-- Create Stock Levels Table
CREATE TABLE tbl_StockLevels (
    StockID AUTOINCREMENT CONSTRAINT PK_StockLevels PRIMARY KEY,
    ProductID LONG NOT NULL,
    CurrentStock LONG DEFAULT 0,
    ReservedStock LONG DEFAULT 0,
    LastUpdated DATETIME DEFAULT Now(),
    CONSTRAINT UK_StockLevels_ProductID UNIQUE (ProductID)
);

-- Create Inventory Transactions Table
CREATE TABLE tbl_InventoryTransactions (
    TransactionID AUTOINCREMENT CONSTRAINT PK_InventoryTransactions PRIMARY KEY,
    ProductID LONG NOT NULL,
    TransactionType TEXT(20) NOT NULL,
    Quantity LONG NOT NULL,
    UnitCost CURRENCY,
    ReferenceType TEXT(20),
    ReferenceID LONG,
    TransactionDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO
);

-- ============================================================================
-- EMPLOYEE MANAGEMENT TABLES
-- ============================================================================

-- Create Employees Table
CREATE TABLE tbl_Employees (
    EmployeeID AUTOINCREMENT CONSTRAINT PK_Employees PRIMARY KEY,
    EmployeeCode TEXT(20) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    HireDate DATETIME NOT NULL,
    Department TEXT(100),
    Position TEXT(100),
    Salary CURRENCY,
    IsActive YESNO DEFAULT Yes,
    Address MEMO,
    EmergencyContact TEXT(100),
    EmergencyPhone TEXT(20),
    CreatedDate DATETIME DEFAULT Now(),
    CONSTRAINT UK_Employees_EmployeeCode UNIQUE (EmployeeCode)
);

-- ============================================================================
-- SALES MANAGEMENT TABLES
-- ============================================================================

-- Create Quotations Table
CREATE TABLE tbl_Quotations (
    QuoteID AUTOINCREMENT CONSTRAINT PK_Quotations PRIMARY KEY,
    QuoteNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    QuoteDate DATETIME DEFAULT Now(),
    ExpiryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Quotations_QuoteNumber UNIQUE (QuoteNumber)
);

-- Create Quotation Items Table
CREATE TABLE tbl_QuotationItems (
    QuoteItemID AUTOINCREMENT CONSTRAINT PK_QuotationItems PRIMARY KEY,
    QuoteID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    DiscountPercentage SINGLE DEFAULT 0,
    LineTotal CURRENCY NOT NULL,
    Description TEXT(255)
);

-- Create Sales Orders Table
CREATE TABLE tbl_SalesOrders (
    OrderID AUTOINCREMENT CONSTRAINT PK_SalesOrders PRIMARY KEY,
    OrderNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    QuoteID LONG,
    OrderDate DATETIME DEFAULT Now(),
    DeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Pending",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    ShippingAddress MEMO,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_SalesOrders_OrderNumber UNIQUE (OrderNumber)
);

-- Create Sales Order Items Table
CREATE TABLE tbl_SalesOrderItems (
    OrderItemID AUTOINCREMENT CONSTRAINT PK_SalesOrderItems PRIMARY KEY,
    OrderID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    DiscountPercentage SINGLE DEFAULT 0,
    LineTotal CURRENCY NOT NULL,
    QuantityShipped LONG DEFAULT 0,
    Description TEXT(255)
);

-- ============================================================================
-- PURCHASE MANAGEMENT TABLES
-- ============================================================================

-- Create Purchase Orders Table
CREATE TABLE tbl_PurchaseOrders (
    PurchaseOrderID AUTOINCREMENT CONSTRAINT PK_PurchaseOrders PRIMARY KEY,
    PONumber TEXT(50) NOT NULL,
    SupplierID LONG NOT NULL,
    OrderDate DATETIME DEFAULT Now(),
    ExpectedDeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_PurchaseOrders_PONumber UNIQUE (PONumber)
);

-- Create Purchase Order Items Table
CREATE TABLE tbl_PurchaseOrderItems (
    POItemID AUTOINCREMENT CONSTRAINT PK_PurchaseOrderItems PRIMARY KEY,
    PurchaseOrderID LONG NOT NULL,
    ProductID LONG NOT NULL,
    Quantity LONG NOT NULL,
    UnitCost CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL,
    QuantityReceived LONG DEFAULT 0
);

-- ============================================================================
-- ACCOUNTING MANAGEMENT TABLES
-- ============================================================================

-- Create Chart of Accounts Table
CREATE TABLE tbl_Accounts (
    AccountID AUTOINCREMENT CONSTRAINT PK_Accounts PRIMARY KEY,
    AccountCode TEXT(20) NOT NULL,
    AccountName TEXT(200) NOT NULL,
    AccountType TEXT(50) NOT NULL,
    ParentAccountID LONG,
    IsActive YESNO DEFAULT Yes,
    Description TEXT(255),
    CONSTRAINT UK_Accounts_AccountCode UNIQUE (AccountCode)
);

-- Create Financial Transactions Table
CREATE TABLE tbl_Transactions (
    TransactionID AUTOINCREMENT CONSTRAINT PK_Transactions PRIMARY KEY,
    TransactionNumber TEXT(50) NOT NULL,
    TransactionDate DATETIME DEFAULT Now(),
    Description TEXT(255) NOT NULL,
    ReferenceType TEXT(20),
    ReferenceID LONG,
    TotalAmount CURRENCY NOT NULL,
    CreatedBy LONG,
    CONSTRAINT UK_Transactions_TransactionNumber UNIQUE (TransactionNumber)
);

-- Create Transaction Items Table
CREATE TABLE tbl_TransactionItems (
    TransactionItemID AUTOINCREMENT CONSTRAINT PK_TransactionItems PRIMARY KEY,
    TransactionID LONG NOT NULL,
    AccountID LONG NOT NULL,
    DebitAmount CURRENCY DEFAULT 0,
    CreditAmount CURRENCY DEFAULT 0,
    Description TEXT(255)
);

-- ============================================================================
-- INVOICE MANAGEMENT TABLES
-- ============================================================================

-- Create Invoices Table
CREATE TABLE tbl_Invoices (
    InvoiceID AUTOINCREMENT CONSTRAINT PK_Invoices PRIMARY KEY,
    InvoiceNumber TEXT(50) NOT NULL,
    ClientID LONG NOT NULL,
    SalesOrderID LONG,
    InvoiceDate DATETIME DEFAULT Now(),
    DueDate DATETIME NOT NULL,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    PaymentTerms LONG DEFAULT 30,
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Invoices_InvoiceNumber UNIQUE (InvoiceNumber)
);

-- Create Invoice Items Table
CREATE TABLE tbl_InvoiceItems (
    InvoiceItemID AUTOINCREMENT CONSTRAINT PK_InvoiceItems PRIMARY KEY,
    InvoiceID LONG NOT NULL,
    ProductID LONG,
    Description TEXT(255) NOT NULL,
    Quantity LONG NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL
);

-- Create Payments Table
CREATE TABLE tbl_Payments (
    PaymentID AUTOINCREMENT CONSTRAINT PK_Payments PRIMARY KEY,
    PaymentNumber TEXT(50) NOT NULL,
    ClientID LONG,
    InvoiceID LONG,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentMethod TEXT(50) NOT NULL,
    Amount CURRENCY NOT NULL,
    ReferenceNumber TEXT(100),
    CreatedBy LONG,
    Notes MEMO,
    CONSTRAINT UK_Payments_PaymentNumber UNIQUE (PaymentNumber)
);

-- ============================================================================
-- SYSTEM TABLES
-- ============================================================================

-- Create System Settings Table
CREATE TABLE tbl_SystemSettings (
    SettingID AUTOINCREMENT CONSTRAINT PK_SystemSettings PRIMARY KEY,
    SettingKey TEXT(100) NOT NULL,
    SettingValue MEMO,
    SettingType TEXT(20) DEFAULT "STRING",
    Description TEXT(255),
    IsSystem YESNO DEFAULT No,
    UpdatedDate DATETIME DEFAULT Now(),
    UpdatedBy LONG,
    CONSTRAINT UK_SystemSettings_SettingKey UNIQUE (SettingKey)
);

-- Create Audit Log Table
CREATE TABLE tbl_AuditLog (
    LogID AUTOINCREMENT CONSTRAINT PK_AuditLog PRIMARY KEY,
    TableName TEXT(100) NOT NULL,
    RecordID LONG NOT NULL,
    Action TEXT(20) NOT NULL,
    OldValues MEMO,
    NewValues MEMO,
    ChangedDate DATETIME DEFAULT Now(),
    ChangedBy LONG
);
