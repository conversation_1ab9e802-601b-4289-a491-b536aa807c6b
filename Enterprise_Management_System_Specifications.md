# نظام إدارة المؤسسات الشامل - Microsoft Access
## Enterprise Management System - Complete Specifications

### نظرة عامة على النظام / System Overview

نظام إدارة شامل مصمم خصيصاً للمؤسسات الصغيرة والمتوسطة باستخدام Microsoft Access، يتميز بواجهة مستخدم احترافية وأيقونات جذابة مع إمكانيات متقدمة لإدارة جميع جوانب العمل التجاري.

**Target Users**: Small to Medium Enterprises (10-500 employees)
**Platform**: Microsoft Access 2016/2019/365
**Language Support**: Arabic/English Interface
**Database**: Access Database (.accdb)

---

## المواصفات التقنية / Technical Specifications

### متطلبات النظام / System Requirements
- **Operating System**: Windows 10/11
- **Microsoft Access**: 2016 or later
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 2GB free space
- **Network**: Optional for multi-user deployment

### الهيكل المعماري / Architecture Structure

```
Enterprise Management System
├── Main Navigation (القائمة الرئيسية)
├── Dashboard (لوحة التحكم)
├── Core Modules (الوحدات الأساسية)
│   ├── Client Management (إدارة العملاء)
│   ├── Purchase Management (إدارة المشتريات)
│   ├── Sales Management (إدارة المبيعات)
│   ├── Inventory Management (إدارة المخازن)
│   ├── Supplier Management (إدارة الموردين)
│   ├── Employee Management (إدارة الموظفين)
│   ├── Accounting Management (إدارة الحسابات)
│   ├── Invoice Management (إدارة الفواتير)
│   └── Reporting Management (إدارة التقارير)
├── System Modules (وحدات النظام)
│   ├── Database Management (إدارة قاعدة البيانات)
│   ├── User Permissions (إدارة الصلاحيات)
│   └── Charts & Graphics (الرسوم البيانية)
└── Documentation (التوثيق)
```

---

## تصميم قاعدة البيانات / Database Design

### الجداول الأساسية / Core Tables

#### 1. إدارة المستخدمين / User Management
```sql
-- Users Table (جدول المستخدمين)
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL UNIQUE,
    Password TEXT(255) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    RoleID INTEGER,
    IsActive YES/NO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME,
    FOREIGN KEY (RoleID) REFERENCES tbl_Roles(RoleID)
);

-- Roles Table (جدول الأدوار)
CREATE TABLE tbl_Roles (
    RoleID AUTOINCREMENT PRIMARY KEY,
    RoleName TEXT(50) NOT NULL UNIQUE,
    Description TEXT(255),
    Permissions MEMO,
    IsActive YES/NO DEFAULT Yes
);
```

#### 2. إدارة العملاء / Client Management
```sql
-- Clients Table (جدول العملاء)
CREATE TABLE tbl_Clients (
    ClientID AUTOINCREMENT PRIMARY KEY,
    ClientCode TEXT(20) NOT NULL UNIQUE,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Website TEXT(200),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    CreditLimit CURRENCY DEFAULT 0,
    PaymentTerms INTEGER DEFAULT 30,
    ClientType TEXT(20) DEFAULT "Customer",
    IsActive YES/NO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy INTEGER,
    Notes MEMO
);

-- Client Contacts (جهات الاتصال)
CREATE TABLE tbl_ClientContacts (
    ContactID AUTOINCREMENT PRIMARY KEY,
    ClientID INTEGER NOT NULL,
    ContactName TEXT(100) NOT NULL,
    Position TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    IsPrimary YES/NO DEFAULT No,
    FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID)
);
```

#### 3. إدارة الموردين / Supplier Management
```sql
-- Suppliers Table (جدول الموردين)
CREATE TABLE tbl_Suppliers (
    SupplierID AUTOINCREMENT PRIMARY KEY,
    SupplierCode TEXT(20) NOT NULL UNIQUE,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    PaymentTerms INTEGER DEFAULT 30,
    Currency TEXT(3) DEFAULT "USD",
    Rating INTEGER DEFAULT 0,
    IsActive YES/NO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
);
```

#### 4. إدارة المنتجات والمخازن / Products & Inventory
```sql
-- Categories Table (جدول الفئات)
CREATE TABLE tbl_Categories (
    CategoryID AUTOINCREMENT PRIMARY KEY,
    CategoryName TEXT(100) NOT NULL UNIQUE,
    ParentCategoryID INTEGER,
    Description TEXT(255),
    IsActive YES/NO DEFAULT Yes
);

-- Products Table (جدول المنتجات)
CREATE TABLE tbl_Products (
    ProductID AUTOINCREMENT PRIMARY KEY,
    ProductCode TEXT(50) NOT NULL UNIQUE,
    ProductName TEXT(200) NOT NULL,
    CategoryID INTEGER,
    Description MEMO,
    UnitOfMeasure TEXT(20) DEFAULT "Each",
    CostPrice CURRENCY DEFAULT 0,
    SellingPrice CURRENCY DEFAULT 0,
    MinimumStock INTEGER DEFAULT 0,
    ReorderPoint INTEGER DEFAULT 0,
    Barcode TEXT(100),
    IsActive YES/NO DEFAULT Yes,
    IsService YES/NO DEFAULT No,
    SupplierID INTEGER,
    CreatedDate DATETIME DEFAULT Now(),
    FOREIGN KEY (CategoryID) REFERENCES tbl_Categories(CategoryID),
    FOREIGN KEY (SupplierID) REFERENCES tbl_Suppliers(SupplierID)
);

-- Stock Levels (مستويات المخزون)
CREATE TABLE tbl_StockLevels (
    StockID AUTOINCREMENT PRIMARY KEY,
    ProductID INTEGER NOT NULL,
    CurrentStock INTEGER DEFAULT 0,
    ReservedStock INTEGER DEFAULT 0,
    AvailableStock INTEGER, -- Calculated field
    LastUpdated DATETIME DEFAULT Now(),
    FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID)
);

-- Inventory Transactions (حركات المخزون)
CREATE TABLE tbl_InventoryTransactions (
    TransactionID AUTOINCREMENT PRIMARY KEY,
    ProductID INTEGER NOT NULL,
    TransactionType TEXT(20) NOT NULL, -- IN, OUT, ADJUSTMENT
    Quantity INTEGER NOT NULL,
    UnitCost CURRENCY,
    ReferenceType TEXT(20), -- PURCHASE, SALE, ADJUSTMENT
    ReferenceID INTEGER,
    TransactionDate DATETIME DEFAULT Now(),
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID)
);
```

#### 5. إدارة المبيعات / Sales Management
```sql
-- Sales Orders (أوامر البيع)
CREATE TABLE tbl_SalesOrders (
    OrderID AUTOINCREMENT PRIMARY KEY,
    OrderNumber TEXT(50) NOT NULL UNIQUE,
    ClientID INTEGER NOT NULL,
    OrderDate DATETIME DEFAULT Now(),
    DeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Pending",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    ShippingAddress MEMO,
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID)
);

-- Sales Order Items (بنود أوامر البيع)
CREATE TABLE tbl_SalesOrderItems (
    OrderItemID AUTOINCREMENT PRIMARY KEY,
    OrderID INTEGER NOT NULL,
    ProductID INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    DiscountPercentage SINGLE DEFAULT 0,
    LineTotal CURRENCY NOT NULL,
    Description TEXT(255),
    FOREIGN KEY (OrderID) REFERENCES tbl_SalesOrders(OrderID),
    FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID)
);

-- Quotations (عروض الأسعار)
CREATE TABLE tbl_Quotations (
    QuoteID AUTOINCREMENT PRIMARY KEY,
    QuoteNumber TEXT(50) NOT NULL UNIQUE,
    ClientID INTEGER NOT NULL,
    QuoteDate DATETIME DEFAULT Now(),
    ExpiryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID)
);
```

#### 6. إدارة المشتريات / Purchase Management
```sql
-- Purchase Orders (أوامر الشراء)
CREATE TABLE tbl_PurchaseOrders (
    PurchaseOrderID AUTOINCREMENT PRIMARY KEY,
    PONumber TEXT(50) NOT NULL UNIQUE,
    SupplierID INTEGER NOT NULL,
    OrderDate DATETIME DEFAULT Now(),
    ExpectedDeliveryDate DATETIME,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaymentStatus TEXT(20) DEFAULT "Pending",
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (SupplierID) REFERENCES tbl_Suppliers(SupplierID)
);

-- Purchase Order Items (بنود أوامر الشراء)
CREATE TABLE tbl_PurchaseOrderItems (
    POItemID AUTOINCREMENT PRIMARY KEY,
    PurchaseOrderID INTEGER NOT NULL,
    ProductID INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitCost CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL,
    QuantityReceived INTEGER DEFAULT 0,
    FOREIGN KEY (PurchaseOrderID) REFERENCES tbl_PurchaseOrders(PurchaseOrderID),
    FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID)
);
```

#### 7. إدارة الموظفين / Employee Management
```sql
-- Employees Table (جدول الموظفين)
CREATE TABLE tbl_Employees (
    EmployeeID AUTOINCREMENT PRIMARY KEY,
    EmployeeCode TEXT(20) NOT NULL UNIQUE,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    HireDate DATETIME NOT NULL,
    Department TEXT(100),
    Position TEXT(100),
    Salary CURRENCY,
    IsActive YES/NO DEFAULT Yes,
    Address MEMO,
    EmergencyContact TEXT(100),
    EmergencyPhone TEXT(20),
    CreatedDate DATETIME DEFAULT Now()
);
```

#### 8. إدارة الحسابات / Accounting Management
```sql
-- Chart of Accounts (دليل الحسابات)
CREATE TABLE tbl_Accounts (
    AccountID AUTOINCREMENT PRIMARY KEY,
    AccountCode TEXT(20) NOT NULL UNIQUE,
    AccountName TEXT(200) NOT NULL,
    AccountType TEXT(50) NOT NULL,
    ParentAccountID INTEGER,
    IsActive YES/NO DEFAULT Yes,
    Description TEXT(255)
);

-- Financial Transactions (المعاملات المالية)
CREATE TABLE tbl_Transactions (
    TransactionID AUTOINCREMENT PRIMARY KEY,
    TransactionNumber TEXT(50) NOT NULL UNIQUE,
    TransactionDate DATETIME DEFAULT Now(),
    Description TEXT(255) NOT NULL,
    ReferenceType TEXT(20),
    ReferenceID INTEGER,
    TotalAmount CURRENCY NOT NULL,
    CreatedBy INTEGER
);

-- Transaction Items (بنود المعاملات)
CREATE TABLE tbl_TransactionItems (
    TransactionItemID AUTOINCREMENT PRIMARY KEY,
    TransactionID INTEGER NOT NULL,
    AccountID INTEGER NOT NULL,
    DebitAmount CURRENCY DEFAULT 0,
    CreditAmount CURRENCY DEFAULT 0,
    Description TEXT(255),
    FOREIGN KEY (TransactionID) REFERENCES tbl_Transactions(TransactionID),
    FOREIGN KEY (AccountID) REFERENCES tbl_Accounts(AccountID)
);
```

#### 9. إدارة الفواتير / Invoice Management
```sql
-- Invoices (الفواتير)
CREATE TABLE tbl_Invoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    InvoiceNumber TEXT(50) NOT NULL UNIQUE,
    ClientID INTEGER NOT NULL,
    SalesOrderID INTEGER,
    InvoiceDate DATETIME DEFAULT Now(),
    DueDate DATETIME NOT NULL,
    Status TEXT(20) DEFAULT "Draft",
    Subtotal CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    TotalAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    BalanceDue CURRENCY, -- Calculated field
    PaymentTerms INTEGER DEFAULT 30,
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID),
    FOREIGN KEY (SalesOrderID) REFERENCES tbl_SalesOrders(OrderID)
);

-- Invoice Items (بنود الفواتير)
CREATE TABLE tbl_InvoiceItems (
    InvoiceItemID AUTOINCREMENT PRIMARY KEY,
    InvoiceID INTEGER NOT NULL,
    ProductID INTEGER,
    Description TEXT(255) NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    LineTotal CURRENCY NOT NULL,
    FOREIGN KEY (InvoiceID) REFERENCES tbl_Invoices(InvoiceID),
    FOREIGN KEY (ProductID) REFERENCES tbl_Products(ProductID)
);

-- Payments (المدفوعات)
CREATE TABLE tbl_Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    PaymentNumber TEXT(50) NOT NULL UNIQUE,
    ClientID INTEGER,
    InvoiceID INTEGER,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentMethod TEXT(50) NOT NULL,
    Amount CURRENCY NOT NULL,
    ReferenceNumber TEXT(100),
    CreatedBy INTEGER,
    Notes MEMO,
    FOREIGN KEY (ClientID) REFERENCES tbl_Clients(ClientID),
    FOREIGN KEY (InvoiceID) REFERENCES tbl_Invoices(InvoiceID)
);
```

#### 10. إعدادات النظام / System Settings
```sql
-- System Settings (إعدادات النظام)
CREATE TABLE tbl_SystemSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingKey TEXT(100) NOT NULL UNIQUE,
    SettingValue MEMO,
    SettingType TEXT(20) DEFAULT "STRING",
    Description TEXT(255),
    IsSystem YES/NO DEFAULT No,
    UpdatedDate DATETIME DEFAULT Now(),
    UpdatedBy INTEGER
);

-- Audit Log (سجل المراجعة)
CREATE TABLE tbl_AuditLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    TableName TEXT(100) NOT NULL,
    RecordID INTEGER NOT NULL,
    Action TEXT(20) NOT NULL,
    OldValues MEMO,
    NewValues MEMO,
    ChangedDate DATETIME DEFAULT Now(),
    ChangedBy INTEGER
);
```

---

## العلاقات بين الجداول / Table Relationships

### Primary Relationships
1. **Users → Roles** (Many-to-One)
2. **Clients → ClientContacts** (One-to-Many)
3. **Products → Categories** (Many-to-One)
4. **Products → Suppliers** (Many-to-One)
5. **SalesOrders → Clients** (Many-to-One)
6. **SalesOrders → SalesOrderItems** (One-to-Many)
7. **PurchaseOrders → Suppliers** (Many-to-One)
8. **PurchaseOrders → PurchaseOrderItems** (One-to-Many)
9. **Invoices → Clients** (Many-to-One)
10. **Invoices → InvoiceItems** (One-to-Many)

### Referential Integrity Rules
- **Cascade Update**: Yes for all relationships
- **Cascade Delete**: No (to preserve data integrity)
- **Enforce Referential Integrity**: Yes for all relationships

---

## الفهارس والأداء / Indexes and Performance

### Primary Indexes
```sql
-- Performance optimization indexes
CREATE INDEX idx_Clients_CompanyName ON tbl_Clients(CompanyName);
CREATE INDEX idx_Products_ProductCode ON tbl_Products(ProductCode);
CREATE INDEX idx_SalesOrders_OrderDate ON tbl_SalesOrders(OrderDate);
CREATE INDEX idx_Invoices_InvoiceDate ON tbl_Invoices(InvoiceDate);
CREATE INDEX idx_Transactions_TransactionDate ON tbl_Transactions(TransactionDate);
```

### Composite Indexes
```sql
CREATE INDEX idx_SalesOrders_Client_Date ON tbl_SalesOrders(ClientID, OrderDate);
CREATE INDEX idx_Inventory_Product_Date ON tbl_InventoryTransactions(ProductID, TransactionDate);
```

---

## التحقق من صحة البيانات / Data Validation

### Field Validation Rules
- **Email Fields**: Format validation using regex
- **Phone Numbers**: Format validation (###) ###-####
- **Currency Fields**: >= 0
- **Date Fields**: >= Date()
- **Required Fields**: NOT NULL constraints

### Business Rules
- **Credit Limit**: Cannot exceed company policy limits
- **Stock Levels**: Cannot go below zero
- **Payment Terms**: Must be between 0-365 days
- **User Permissions**: Role-based access control

---

## تصميم واجهة المستخدم / User Interface Design

### المبادئ التصميمية / Design Principles
- **Professional Appearance**: واجهة احترافية مع ألوان متناسقة
- **Intuitive Navigation**: تنقل سهل ومنطقي بين الوحدات
- **Responsive Layout**: تصميم متجاوب مع أحجام الشاشات المختلفة
- **Arabic/English Support**: دعم كامل للغة العربية والإنجليزية
- **Icon-Based Navigation**: أيقونات واضحة ومعبرة لكل وحدة

### نظام الألوان / Color Scheme
```
Primary Colors:
- Main Blue: #2E86AB (للعناوين والأزرار الرئيسية)
- Secondary Blue: #A23B72 (للتمييز والتنبيهات)
- Success Green: #F18F01 (للعمليات الناجحة)
- Warning Orange: #C73E1D (للتحذيرات والأخطاء)

Background Colors:
- Light Gray: #F5F5F5 (خلفية النماذج)
- White: #FFFFFF (خلفية المحتوى)
- Dark Gray: #333333 (النصوص الرئيسية)
```

### الخطوط / Typography
- **Arabic Font**: Tahoma, Arial Unicode MS
- **English Font**: Segoe UI, Calibri
- **Header Size**: 16-18pt Bold
- **Body Text**: 11-12pt Regular
- **Small Text**: 9-10pt Regular

---

## القائمة الرئيسية / Main Navigation Interface

### تصميم الشاشة الرئيسية / Main Screen Design

```vba
' Main Navigation Form (frm_MainNavigation)
' Form Properties:
' - Modal: No
' - PopUp: No
' - AutoCenter: Yes
' - BorderStyle: Dialog
' - RecordSelectors: No
' - NavigationButtons: No
' - DividingLines: No
' - ScrollBars: Neither
```

### أيقونات الوحدات / Module Icons

#### 1. لوحة التحكم / Dashboard
- **Icon**: 📊 Dashboard icon
- **Color**: Blue (#2E86AB)
- **Size**: 64x64 pixels
- **Description**: عرض المؤشرات الرئيسية والإحصائيات

#### 2. إدارة العملاء / Client Management
- **Icon**: 👥 Clients icon
- **Color**: Green (#4CAF50)
- **Description**: إدارة بيانات العملاء وجهات الاتصال

#### 3. إدارة المشتريات / Purchase Management
- **Icon**: 🛒 Shopping cart icon
- **Color**: Orange (#FF9800)
- **Description**: إدارة أوامر الشراء والموردين

#### 4. إدارة المبيعات / Sales Management
- **Icon**: 💰 Sales icon
- **Color**: Purple (#9C27B0)
- **Description**: إدارة أوامر البيع وعروض الأسعار

#### 5. إدارة المخازن / Inventory Management
- **Icon**: 📦 Warehouse icon
- **Color**: Brown (#795548)
- **Description**: إدارة المخزون وحركة البضائع

#### 6. إدارة الموردين / Supplier Management
- **Icon**: 🏭 Factory icon
- **Color**: Teal (#009688)
- **Description**: إدارة بيانات الموردين والتقييمات

#### 7. إدارة الموظفين / Employee Management
- **Icon**: 👤 Employee icon
- **Color**: Indigo (#3F51B5)
- **Description**: إدارة بيانات الموظفين والأقسام

#### 8. إدارة الحسابات / Accounting Management
- **Icon**: 💳 Accounting icon
- **Color**: Deep Orange (#FF5722)
- **Description**: إدارة الحسابات والمعاملات المالية

#### 9. إدارة الفواتير / Invoice Management
- **Icon**: 📄 Invoice icon
- **Color**: Light Blue (#03A9F4)
- **Description**: إنشاء وإدارة الفواتير والمدفوعات

#### 10. إدارة التقارير / Reporting Management
- **Icon**: 📈 Reports icon
- **Color**: Light Green (#8BC34A)
- **Description**: إنشاء وعرض التقارير المختلفة

#### 11. إدارة قاعدة البيانات / Database Management
- **Icon**: 🗄️ Database icon
- **Color**: Blue Grey (#607D8B)
- **Description**: نسخ احتياطي وصيانة قاعدة البيانات

#### 12. إدارة الصلاحيات / User Permissions
- **Icon**: 🔐 Security icon
- **Color**: Red (#F44336)
- **Description**: إدارة المستخدمين والصلاحيات

#### 13. الرسوم البيانية / Charts & Graphics
- **Icon**: 📊 Chart icon
- **Color**: Cyan (#00BCD4)
- **Description**: عرض الرسوم البيانية والإحصائيات

---

## مواصفات الوحدات / Module Specifications

### 1. وحدة إدارة العملاء / Client Management Module

#### النماذج / Forms
```vba
' Main Client Form (frm_Clients)
Private Sub Form_Load()
    ' Initialize form
    Me.RecordSource = "SELECT * FROM tbl_Clients WHERE IsActive = True ORDER BY CompanyName"

    ' Set form properties
    Me.AllowAdditions = True
    Me.AllowDeletions = False ' Soft delete only
    Me.AllowEdits = True

    ' Load client types
    Me.cbo_ClientType.RowSource = "Customer;Prospect;Inactive"
End Sub

Private Sub btn_NewClient_Click()
    ' Open new client form
    DoCmd.OpenForm "frm_ClientDetails", acFormAdd
End Sub

Private Sub btn_EditClient_Click()
    ' Open edit client form
    If Not IsNull(Me.ClientID) Then
        DoCmd.OpenForm "frm_ClientDetails", acFormEdit, , "ClientID=" & Me.ClientID
    End If
End Sub

Private Sub btn_ViewHistory_Click()
    ' Show client interaction history
    If Not IsNull(Me.ClientID) Then
        DoCmd.OpenForm "frm_ClientHistory", acFormReadOnly, , "ClientID=" & Me.ClientID
    End If
End Sub
```

#### الاستعلامات / Queries
```sql
-- Active Clients Query
SELECT
    c.ClientID,
    c.ClientCode,
    c.CompanyName,
    c.ContactPerson,
    c.Phone,
    c.Email,
    c.CreditLimit,
    c.PaymentTerms,
    c.ClientType,
    COUNT(so.OrderID) AS TotalOrders,
    SUM(so.TotalAmount) AS TotalSales
FROM tbl_Clients c
LEFT JOIN tbl_SalesOrders so ON c.ClientID = so.ClientID
WHERE c.IsActive = True
GROUP BY c.ClientID, c.ClientCode, c.CompanyName, c.ContactPerson,
         c.Phone, c.Email, c.CreditLimit, c.PaymentTerms, c.ClientType
ORDER BY c.CompanyName;

-- Client Credit Status Query
SELECT
    c.ClientID,
    c.CompanyName,
    c.CreditLimit,
    ISNULL(SUM(i.BalanceDue), 0) AS OutstandingBalance,
    c.CreditLimit - ISNULL(SUM(i.BalanceDue), 0) AS AvailableCredit,
    IIF(ISNULL(SUM(i.BalanceDue), 0) > c.CreditLimit, "Over Limit", "Within Limit") AS CreditStatus
FROM tbl_Clients c
LEFT JOIN tbl_Invoices i ON c.ClientID = i.ClientID AND i.Status <> "Paid"
WHERE c.IsActive = True
GROUP BY c.ClientID, c.CompanyName, c.CreditLimit;
```

#### التقارير / Reports
- **Client List Report**: قائمة العملاء مع التفاصيل الأساسية
- **Client Statement**: كشف حساب العميل
- **Credit Status Report**: تقرير حالة الائتمان
- **Client Activity Report**: تقرير نشاط العميل

### 2. وحدة إدارة المشتريات / Purchase Management Module

#### النماذج / Forms
```vba
' Purchase Order Form (frm_PurchaseOrders)
Private Sub Form_Load()
    ' Initialize purchase order form
    If Me.NewRecord Then
        Me.PONumber = GeneratePONumber()
        Me.OrderDate = Date
        Me.Status = "Draft"
        Me.CreatedBy = CurrentUser()
    End If
End Sub

Private Sub cbo_Supplier_AfterUpdate()
    ' Load supplier details
    Dim rs As Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT * FROM tbl_Suppliers WHERE SupplierID = " & Me.cbo_Supplier)

    If Not rs.EOF Then
        Me.txt_SupplierInfo = rs!CompanyName & vbCrLf & rs!Address
        Me.txt_PaymentTerms = rs!PaymentTerms
    End If
    rs.Close
End Sub

Private Sub btn_AddItem_Click()
    ' Add item to purchase order
    DoCmd.OpenForm "frm_POItemDetails", acFormAdd, , , , acDialog, "PurchaseOrderID=" & Me.PurchaseOrderID

    ' Refresh subform
    Me.subform_POItems.Requery
    CalculatePOTotals
End Sub

Private Sub CalculatePOTotals()
    ' Calculate purchase order totals
    Dim rs As Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT SUM(LineTotal) AS Subtotal FROM tbl_PurchaseOrderItems WHERE PurchaseOrderID = " & Me.PurchaseOrderID)

    If Not rs.EOF Then
        Me.Subtotal = Nz(rs!Subtotal, 0)
        Me.TaxAmount = Me.Subtotal * 0.1 ' 10% tax
        Me.TotalAmount = Me.Subtotal + Me.TaxAmount
    End If
    rs.Close
End Sub
```

### 3. وحدة إدارة المبيعات / Sales Management Module

#### النماذج / Forms
```vba
' Sales Order Form (frm_SalesOrders)
Private Sub Form_Load()
    ' Initialize sales order form
    If Me.NewRecord Then
        Me.OrderNumber = GenerateOrderNumber()
        Me.OrderDate = Date
        Me.Status = "Pending"
        Me.CreatedBy = CurrentUser()
    End If
End Sub

Private Sub cbo_Client_AfterUpdate()
    ' Load client details and check credit limit
    Dim rs As Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT * FROM qry_ClientCreditStatus WHERE ClientID = " & Me.cbo_Client)

    If Not rs.EOF Then
        Me.txt_ClientInfo = rs!CompanyName & vbCrLf & "Credit Limit: " & Format(rs!CreditLimit, "Currency")

        ' Check credit status
        If rs!CreditStatus = "Over Limit" Then
            MsgBox "Warning: Client is over credit limit!", vbExclamation
            Me.txt_ClientInfo.BackColor = RGB(255, 200, 200) ' Light red background
        End If
    End If
    rs.Close
End Sub

Private Sub btn_ConvertToInvoice_Click()
    ' Convert sales order to invoice
    If Me.Status = "Confirmed" Then
        Dim InvoiceID As Long
        InvoiceID = CreateInvoiceFromSalesOrder(Me.OrderID)

        If InvoiceID > 0 Then
            MsgBox "Invoice created successfully! Invoice ID: " & InvoiceID, vbInformation
            DoCmd.OpenForm "frm_Invoices", acFormEdit, , "InvoiceID=" & InvoiceID
        End If
    Else
        MsgBox "Sales order must be confirmed before converting to invoice.", vbExclamation
    End If
End Sub
```

### 4. وحدة إدارة المخازن / Inventory Management Module

#### النماذج / Forms
```vba
' Inventory Management Form (frm_Inventory)
Private Sub Form_Load()
    ' Load inventory with stock levels
    Me.RecordSource = "SELECT p.*, s.CurrentStock, s.AvailableStock, " & _
                     "IIF(s.CurrentStock <= p.ReorderPoint, 'Low Stock', 'OK') AS StockStatus " & _
                     "FROM tbl_Products p LEFT JOIN tbl_StockLevels s ON p.ProductID = s.ProductID " & _
                     "WHERE p.IsActive = True ORDER BY p.ProductName"
End Sub

Private Sub btn_StockAdjustment_Click()
    ' Open stock adjustment form
    DoCmd.OpenForm "frm_StockAdjustment", acFormAdd, , , , acDialog
    Me.Requery ' Refresh after adjustment
End Sub

Private Sub btn_LowStockReport_Click()
    ' Show low stock report
    DoCmd.OpenReport "rpt_LowStockItems", acViewPreview
End Sub

Private Sub Form_Current()
    ' Highlight low stock items
    If Me.StockStatus = "Low Stock" Then
        Me.Detail.BackColor = RGB(255, 235, 235) ' Light red
    Else
        Me.Detail.BackColor = RGB(255, 255, 255) ' White
    End If
End Sub
```

#### الاستعلامات / Queries
```sql
-- Low Stock Items Query
SELECT
    p.ProductID,
    p.ProductCode,
    p.ProductName,
    p.ReorderPoint,
    s.CurrentStock,
    s.AvailableStock,
    p.CostPrice,
    p.SellingPrice,
    sup.CompanyName AS SupplierName
FROM tbl_Products p
LEFT JOIN tbl_StockLevels s ON p.ProductID = s.ProductID
LEFT JOIN tbl_Suppliers sup ON p.SupplierID = sup.SupplierID
WHERE p.IsActive = True
  AND s.CurrentStock <= p.ReorderPoint
ORDER BY s.CurrentStock ASC;

-- Inventory Valuation Query
SELECT
    p.ProductID,
    p.ProductCode,
    p.ProductName,
    s.CurrentStock,
    p.CostPrice,
    s.CurrentStock * p.CostPrice AS InventoryValue
FROM tbl_Products p
INNER JOIN tbl_StockLevels s ON p.ProductID = s.ProductID
WHERE p.IsActive = True AND s.CurrentStock > 0
ORDER BY InventoryValue DESC;
```

### 5. وحدة لوحة التحكم / Dashboard Module

#### المؤشرات الرئيسية / Key Performance Indicators (KPIs)
```vba
' Dashboard Form (frm_Dashboard)
Private Sub Form_Load()
    ' Load dashboard KPIs
    LoadSalesKPIs
    LoadInventoryKPIs
    LoadFinancialKPIs
    LoadCustomerKPIs

    ' Load charts
    LoadSalesChart
    LoadInventoryChart
    LoadRevenueChart
End Sub

Private Sub LoadSalesKPIs()
    ' Sales KPIs for current month
    Dim rs As Recordset

    ' Total Sales This Month
    Set rs = CurrentDb.OpenRecordset("SELECT SUM(TotalAmount) AS TotalSales FROM tbl_SalesOrders WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date())")
    Me.lbl_TotalSales = Format(Nz(rs!TotalSales, 0), "Currency")
    rs.Close

    ' Number of Orders This Month
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS OrderCount FROM tbl_SalesOrders WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date())")
    Me.lbl_OrderCount = rs!OrderCount
    rs.Close

    ' Average Order Value
    Set rs = CurrentDb.OpenRecordset("SELECT AVG(TotalAmount) AS AvgOrder FROM tbl_SalesOrders WHERE MONTH(OrderDate) = MONTH(Date()) AND YEAR(OrderDate) = YEAR(Date())")
    Me.lbl_AvgOrderValue = Format(Nz(rs!AvgOrder, 0), "Currency")
    rs.Close
End Sub

Private Sub LoadInventoryKPIs()
    ' Inventory KPIs
    Dim rs As Recordset

    ' Total Inventory Value
    Set rs = CurrentDb.OpenRecordset("SELECT SUM(s.CurrentStock * p.CostPrice) AS InventoryValue FROM tbl_StockLevels s INNER JOIN tbl_Products p ON s.ProductID = p.ProductID WHERE p.IsActive = True")
    Me.lbl_InventoryValue = Format(Nz(rs!InventoryValue, 0), "Currency")
    rs.Close

    ' Low Stock Items Count
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS LowStockCount FROM tbl_Products p INNER JOIN tbl_StockLevels s ON p.ProductID = s.ProductID WHERE p.IsActive = True AND s.CurrentStock <= p.ReorderPoint")
    Me.lbl_LowStockCount = rs!LowStockCount
    rs.Close
End Sub
```

---

## نظام الأمان والصلاحيات / Security and Permissions System

### أدوار المستخدمين / User Roles
1. **مدير النظام / System Administrator**
   - صلاحية كاملة على جميع الوحدات
   - إدارة المستخدمين والصلاحيات
   - نسخ احتياطي واستعادة قاعدة البيانات

2. **مدير عام / General Manager**
   - عرض جميع التقارير والإحصائيات
   - الموافقة على العمليات المالية الكبيرة
   - عرض لوحة التحكم التنفيذية

3. **مدير المبيعات / Sales Manager**
   - إدارة العملاء والمبيعات
   - عرض تقارير المبيعات
   - إدارة عروض الأسعار

4. **مدير المشتريات / Purchase Manager**
   - إدارة الموردين والمشتريات
   - الموافقة على أوامر الشراء
   - عرض تقارير المشتريات

5. **أمين المخزن / Warehouse Keeper**
   - إدارة المخزون وحركة البضائع
   - تسجيل الاستلام والصرف
   - عرض تقارير المخزون

6. **محاسب / Accountant**
   - إدارة الحسابات والمعاملات المالية
   - إنشاء الفواتير والمدفوعات
   - عرض التقارير المالية

7. **موظف إدخال بيانات / Data Entry Clerk**
   - إدخال البيانات الأساسية
   - تحديث معلومات العملاء والموردين
   - عرض محدود للتقارير

### تطبيق الصلاحيات / Permission Implementation
```vba
' Permission Check Function
Public Function HasPermission(ModuleName As String, Action As String) As Boolean
    Dim rs As Recordset
    Dim SQL As String

    SQL = "SELECT COUNT(*) AS PermissionCount " & _
          "FROM tbl_Users u " & _
          "INNER JOIN tbl_UserRoles ur ON u.UserID = ur.UserID " & _
          "INNER JOIN tbl_RolePermissions rp ON ur.RoleID = rp.RoleID " & _
          "INNER JOIN tbl_Permissions p ON rp.PermissionID = p.PermissionID " & _
          "WHERE u.Username = '" & CurrentUser() & "' " & _
          "AND p.ModuleName = '" & ModuleName & "' " & _
          "AND p.Action = '" & Action & "'"

    Set rs = CurrentDb.OpenRecordset(SQL)
    HasPermission = (rs!PermissionCount > 0)
    rs.Close
End Function

' Form Load with Permission Check
Private Sub Form_Load()
    ' Check if user has permission to view this form
    If Not HasPermission("Client Management", "Read") Then
        MsgBox "You don't have permission to access this module.", vbCritical
        DoCmd.Close acForm, Me.Name
        Exit Sub
    End If

    ' Set form permissions based on user role
    Me.AllowAdditions = HasPermission("Client Management", "Create")
    Me.AllowEdits = HasPermission("Client Management", "Update")
    Me.AllowDeletions = HasPermission("Client Management", "Delete")
End Sub
```

This completes the major sections of the specifications. The system provides a comprehensive, professional management solution with proper security, data validation, and user-friendly interface design.
