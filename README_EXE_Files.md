# ملفات التشغيل التنفيذية - EXE Files Guide
## نظام إدارة المؤسسات الشامل

### 🎯 نظرة عامة

تم إنشاء مجموعة شاملة من ملفات التشغيل التنفيذية لنظام إدارة المؤسسات لتوفير طرق متعددة للتشغيل تناسب جميع المستخدمين.

---

### 📁 ملفات التشغيل المُنشأة

| الملف | النوع | الوصف | مُوصى للـ |
|-------|--------|--------|-----------|
| **نظام_إدارة_المؤسسات.exe** | EXE | ملف تنفيذي رئيسي مع واجهة رسومية | جميع المستخدمين |
| **نظام_إدارة_المؤسسات_Final.bat** | BAT | ملف تشغيل محسن مع واجهة نصية | المستخدمين المتقدمين |
| **launcher.vbs** | VBS | ملف VBScript للتشغيل السريع | التشغيل الصامت |
| **نظام_إدارة_المؤسسات.ps1** | PS1 | ملف PowerShell الرئيسي | المطورين والمديرين |

---

### 🚀 طرق إنشاء الملفات التنفيذية

#### 1️⃣ الطريقة التلقائية (مُوصى بها)

```batch
# تشغيل ملف الإنشاء التلقائي:
Build_EXE.bat
```

**المميزات:**
- ✅ إنشاء تلقائي لجميع أنواع الملفات
- ✅ اختبار متطلبات النظام
- ✅ إنشاء ملفات بديلة في حالة الفشل
- ✅ تنظيف الملفات المؤقتة
- ✅ اختبار تشغيل فوري

#### 2️⃣ الطريقة المتقدمة

```powershell
# لإنشاء ملف EXE متقدم:
.\Create_EXE_Launcher.ps1

# مع معاملات:
.\Create_EXE_Launcher.ps1 -CreateInstaller  # مع معالج تثبيت
.\Create_EXE_Launcher.ps1 -OutputPath "MySystem.exe"  # اسم مخصص
```

**المميزات:**
- ✅ واجهة رسومية احترافية
- ✅ إنشاء معالج تثبيت
- ✅ تخصيص اسم الملف
- ✅ دعم الأيقونات المخصصة

#### 3️⃣ الطريقة اليدوية

```powershell
# تشغيل الملف الرئيسي مباشرة:
.\نظام_إدارة_المؤسسات.ps1

# مع معاملات:
.\نظام_إدارة_المؤسسات.ps1 -QuickStart  # تشغيل سريع
.\نظام_إدارة_المؤسسات.ps1 -Setup       # إعداد النظام
.\نظام_إدارة_المؤسسات.ps1 -Backup      # نسخة احتياطية
.\نظام_إدارة_المؤسسات.ps1 -GUI         # واجهة رسومية
```

---

### 🖥️ واجهات التشغيل

#### الواجهة الرسومية (GUI)
```
╔══════════════════════════════════════════════════════════════╗
║                نظام إدارة المؤسسات الشامل                    ║
║              Enterprise Management System                   ║
║                        الإصدار 1.0                         ║
╚══════════════════════════════════════════════════════════════╝

🔑 بيانات تسجيل الدخول الافتراضية:
   اسم المستخدم: admin
   كلمة المرور: 12345

┌─────────────────────────────────────────────────────────────┐
│  [🚀 تشغيل سريع]     [🔧 تشغيل متقدم]                      │
│  Quick Start         Advanced Start                        │
│                                                             │
│  [⚙️ إعداد النظام]    [💾 نسخة احتياطية]                    │
│  System Setup        Backup                                │
│                                                             │
│                    [❌ خروج - Exit]                         │
└─────────────────────────────────────────────────────────────┘
```

#### الواجهة النصية (CLI)
```
🎯 اختر العملية المطلوبة:

[1] 🚀 تشغيل سريع للنظام
[2] 🔧 تشغيل متقدم مع فحص شامل
[3] ⚙️ إعداد النظام الكامل
[4] 💾 إنشاء نسخة احتياطية
[5] 📋 معلومات النظام
[6] 🖥️ واجهة رسومية
[0] ❌ خروج

اختر رقماً (0-6):
```

---

### 🔧 المتطلبات التقنية

#### للملفات التنفيذية (.exe):
- **نظام التشغيل:** Windows 7/8/10/11
- **Microsoft Access:** 2016 أو أحدث
- **NET Framework:** 4.0 أو أحدث
- **الذاكرة:** 2 جيجابايت RAM (الحد الأدنى)

#### لملفات PowerShell (.ps1):
- **PowerShell:** 5.0 أو أحدث
- **ExecutionPolicy:** RemoteSigned أو Bypass
- **Microsoft Access:** 2016 أو أحدث

#### لملفات VBScript (.vbs):
- **Windows Script Host:** مُفعل
- **Microsoft Access:** مثبت ومُسجل

---

### 🛠️ استكشاف الأخطاء

#### المشكلة: "الملف التنفيذي لا يعمل"
**الحلول:**
```
1. تأكد من تشغيل الملف كمدير (Run as Administrator)
2. تحقق من وجود Microsoft Access
3. تأكد من وجود ملف قاعدة البيانات في نفس المجلد
4. فحص برنامج مكافحة الفيروسات (قد يحجب الملف)
```

#### المشكلة: "خطأ في PowerShell Execution Policy"
**الحل:**
```powershell
# تشغيل PowerShell كمدير ثم:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# أو للتشغيل المؤقت:
powershell -ExecutionPolicy Bypass -File "نظام_إدارة_المؤسسات.ps1"
```

#### المشكلة: "Microsoft Access غير موجود"
**الحل:**
```
1. تثبيت Microsoft Access 2016 أو أحدث
2. أو تثبيت Microsoft 365 مع Access
3. أو استخدام Access Runtime (مجاني)
```

#### المشكلة: "ملف قاعدة البيانات غير موجود"
**الحل:**
```
1. استخدم الخيار [3] إعداد النظام الكامل
2. أو شغل Setup-System.ps1 -FullSetup
3. أو انسخ ملف نظام_إدارة_المؤسسات.accdb للمجلد
```

---

### 📦 التوزيع والنشر

#### حزمة التوزيع الأساسية:
```
نظام_إدارة_المؤسسات/
├── 🚀 نظام_إدارة_المؤسسات.exe (الملف الرئيسي)
├── 📄 نظام_إدارة_المؤسسات.accdb (قاعدة البيانات)
├── 📋 تعليمات_التشغيل.md (دليل الاستخدام)
├── 📋 README_EXE_Files.md (هذا الملف)
└── 📁 Backups/ (مجلد النسخ الاحتياطية)
```

#### حزمة التوزيع الكاملة:
```
نظام_إدارة_المؤسسات_Complete/
├── 🚀 نظام_إدارة_المؤسسات.exe
├── 🚀 نظام_إدارة_المؤسسات_Final.bat
├── 🚀 launcher.vbs
├── 💻 نظام_إدارة_المؤسسات.ps1
├── 📄 نظام_إدارة_المؤسسات.accdb
├── 📊 Complete_Database_Script.sql
├── 💻 Access_Complete_VBA_Code.bas
├── ⚙️ Setup-System.ps1
├── 🔧 Start-EnterpriseSystem.ps1
├── 📋 جميع ملفات التوثيق
└── 📁 مجلدات النظام (Backups, Reports, Logs, etc.)
```

#### معالج التثبيت:
```
Install_Enterprise_System.bat
├── يقوم بنسخ جميع الملفات
├── ينشئ هيكل المجلدات
├── ينشئ اختصار سطح المكتب
├── يختبر التثبيت
└── يشغل النظام (اختياري)
```

---

### 🎯 أفضل الممارسات

#### للمستخدمين النهائيين:
1. **استخدم الملف التنفيذي (.exe)** للتشغيل اليومي
2. **أنشئ نسخة احتياطية** قبل أي تحديث
3. **غير كلمة المرور** بعد أول تسجيل دخول
4. **احتفظ بنسخة** من ملفات التثبيت

#### للمديرين:
1. **استخدم PowerShell** للإدارة المتقدمة
2. **راقب سجلات النظام** في مجلد Logs
3. **أنشئ نسخ احتياطية دورية** تلقائياً
4. **اختبر التحديثات** في بيئة منفصلة

#### للمطورين:
1. **استخدم ملفات PowerShell** للتخصيص
2. **احتفظ بنسخة من الكود المصدري**
3. **وثق أي تعديلات** تقوم بها
4. **اختبر التوافق** مع إصدارات Access المختلفة

---

### 📞 الدعم الفني

#### للحصول على المساعدة:

**1. المشاكل الأساسية:**
- راجع قسم "استكشاف الأخطاء" أعلاه
- تحقق من ملف "تعليمات_التشغيل.md"
- استخدم الخيار [5] لعرض معلومات النظام

**2. المشاكل التقنية:**
- احفظ محتوى مجلد Logs
- اذكر نوع نظام التشغيل وإصدار Access
- وصف الخطأ بالتفصيل

**3. التواصل:**
```
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: +966-XX-XXXXXXX
🌐 الموقع: www.enterprise-system.com
💬 المنتدى: forum.enterprise-system.com
```

---

### 🔄 التحديثات

#### فحص التحديثات:
- تحقق من الموقع الرسمي شهرياً
- اشترك في النشرة الإخبارية للتحديثات
- راجع ملاحظات الإصدار قبل التحديث

#### تطبيق التحديثات:
1. **أنشئ نسخة احتياطية** كاملة
2. **حمل الإصدار الجديد** من الموقع الرسمي
3. **اختبر التحديث** في بيئة منفصلة
4. **طبق التحديث** على النظام الرئيسي
5. **اختبر جميع الوظائف** بعد التحديث

---

### 🎉 خلاصة

**تم إنشاء مجموعة شاملة من ملفات التشغيل التنفيذية لنظام إدارة المؤسسات!**

**الملفات جاهزة للاستخدام الفوري ويمكن توزيعها على أي جهاز يحتوي على Microsoft Access.**

**اختر الطريقة المناسبة لك وابدأ رحلتك في إدارة مؤسستك بكفاءة واحترافية!**

*تم تطويره بعناية فائقة لخدمة المؤسسات العربية* 🇸🇦
