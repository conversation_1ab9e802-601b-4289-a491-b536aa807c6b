@echo off
chcp 65001 >nul
title نظام إدارة المستندات المتكامل - Integrated Document Management System

echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                نظام إدارة المستندات المتكامل                ║
echo     ║            Integrated Document Management System           ║
echo     ║                        الإصدار 1.0                         ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     🎉 مرحباً بك في نظام إدارة المستندات الشامل!
echo.
echo     📋 مكونات النظام:
echo     ├─ قاعدة بيانات متقدمة (10 جداول رئيسية)
echo     ├─ كود VBA شامل (15+ وظيفة متخصصة)
echo     ├─ نماذج احترافية (6 نماذج متكاملة)
echo     ├─ نظام PowerShell للإدارة والصيانة
echo     ├─ أمان متعدد المستويات مع تشفير
echo     ├─ تقارير وإحصائيات شاملة
echo     ├─ نسخ احتياطية تلقائية
echo     └─ دعم كامل للغة العربية
echo.

echo     🗂️  أنواع المستندات المدعومة:
echo     ├─ عقود العملاء (PDF, DOC, DOCX - حتى 20MB)
echo     ├─ فواتير (PDF, JPG, PNG - حتى 5MB)
echo     ├─ تقارير مالية (PDF, XLS, XLSX - حتى 15MB)
echo     ├─ مستندات الموظفين (PDF, DOC, JPG - حتى 10MB)
echo     ├─ شهادات المنتجات (PDF, JPG, PNG - حتى 10MB)
echo     ├─ مستندات الموردين (PDF, DOC, DOCX - حتى 20MB)
echo     ├─ تقارير المخزون (PDF, XLS, XLSX - حتى 10MB)
echo     ├─ مستندات قانونية (PDF, DOC, DOCX - حتى 25MB)
echo     ├─ صور المنتجات (JPG, PNG, GIF - حتى 5MB)
echo     └─ مستندات أخرى (جميع الأنواع - حتى 10MB)
echo.

echo     📁 هيكل المجلدات:
echo     C:\EnterpriseSystem\Documents\
echo     ├── 📁 Clients\         (مستندات العملاء)
echo     ├── 📁 Sales\           (مستندات المبيعات)
echo     ├── 📁 Purchases\       (مستندات المشتريات)
echo     ├── 📁 Inventory\       (مستندات المخزون)
echo     ├── 📁 Suppliers\       (مستندات الموردين)
echo     ├── 📁 Employees\       (مستندات الموظفين)
echo     ├── 📁 Accounts\        (مستندات الحسابات)
echo     ├── 📁 Invoices\        (مستندات الفواتير)
echo     ├── 📁 Reports\         (مستندات التقارير)
echo     ├── 📁 System\          (مستندات النظام)
echo     ├── 📁 Versions\        (إصدارات المستندات)
echo     ├── 📁 Templates\       (قوالب المستندات)
echo     ├── 📁 Backups\         (النسخ الاحتياطية)
echo     └── 📁 Archive\         (الأرشيف)
echo.

echo     🔐 مستويات الأمان:
echo     ├─ عام (Public): متاح لجميع المستخدمين
echo     ├─ عادي (Normal): متاح للمستخدمين المصرح لهم
echo     ├─ مقيد (Restricted): متاح لمجموعة محددة
echo     └─ سري (Confidential): متاح للإدارة العليا فقط
echo.

echo     📊 الملفات المتاحة:
if exist "Document_Management_System.sql" (
    echo     ✅ قاعدة البيانات: Document_Management_System.sql
) else (
    echo     ❌ قاعدة البيانات: غير موجودة
)

if exist "Document_Management_VBA.bas" (
    echo     ✅ كود VBA: Document_Management_VBA.bas
) else (
    echo     ❌ كود VBA: غير موجود
)

if exist "Document_Management_Forms.md" (
    echo     ✅ تصميم النماذج: Document_Management_Forms.md
) else (
    echo     ❌ تصميم النماذج: غير موجود
)

if exist "Document_Management_PowerShell.ps1" (
    echo     ✅ نظام PowerShell: Document_Management_PowerShell.ps1
) else (
    echo     ❌ نظام PowerShell: غير موجود
)

if exist "دليل_نظام_إدارة_المستندات.md" (
    echo     ✅ الدليل الشامل: دليل_نظام_إدارة_المستندات.md
) else (
    echo     ❌ الدليل الشامل: غير موجود
)

echo.
echo     🚀 خيارات التشغيل:
echo.
echo     [1] تشغيل نظام إدارة المستندات (PowerShell)
echo     [2] عرض قاعدة البيانات (SQL Script)
echo     [3] عرض كود VBA
echo     [4] عرض تصميم النماذج
echo     [5] عرض الدليل الشامل
echo     [6] إنشاء هيكل المجلدات
echo     [7] عرض معلومات مفصلة
echo     [0] خروج
echo.

set /p choice="    اختر رقماً (0-7): "

if "%choice%"=="1" goto RUN_POWERSHELL
if "%choice%"=="2" goto SHOW_DATABASE
if "%choice%"=="3" goto SHOW_VBA
if "%choice%"=="4" goto SHOW_FORMS
if "%choice%"=="5" goto SHOW_GUIDE
if "%choice%"=="6" goto CREATE_FOLDERS
if "%choice%"=="7" goto SHOW_DETAILS
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:RUN_POWERSHELL
cls
echo.
echo     🚀 تشغيل نظام إدارة المستندات...
echo.

if exist "Document_Management_PowerShell.ps1" (
    echo     ✅ تم العثور على ملف PowerShell
    echo     🔄 جاري تشغيل النظام...
    echo.
    echo     💡 نصائح للاستخدام:
    echo     • اختر الخيار [1] لإعداد النظام أولاً
    echo     • استخدم الخيار [3] لإنشاء نسخة احتياطية
    echo     • راجع الخيار [5] لفحص سلامة النظام
    echo.
    
    powershell -ExecutionPolicy Bypass -File "Document_Management_PowerShell.ps1"
    
    if errorlevel 1 (
        echo     ❌ فشل في تشغيل نظام PowerShell
        echo     تأكد من تثبيت PowerShell على النظام
    ) else (
        echo     ✅ تم تشغيل النظام بنجاح!
    )
) else (
    echo     ❌ لم يتم العثور على ملف PowerShell!
    echo     يرجى التأكد من وجود الملف في نفس المجلد
)

echo.
pause
goto MAIN_MENU

:SHOW_DATABASE
cls
echo.
echo     📊 عرض قاعدة البيانات...
echo.

if exist "Document_Management_System.sql" (
    echo     🔄 جاري فتح ملف قاعدة البيانات...
    notepad "Document_Management_System.sql"
    echo     ✅ تم فتح ملف قاعدة البيانات!
) else (
    echo     ❌ ملف قاعدة البيانات غير موجود
)

echo.
pause
goto MAIN_MENU

:SHOW_VBA
cls
echo.
echo     💻 عرض كود VBA...
echo.

if exist "Document_Management_VBA.bas" (
    echo     🔄 جاري فتح ملف كود VBA...
    notepad "Document_Management_VBA.bas"
    echo     ✅ تم فتح ملف كود VBA!
) else (
    echo     ❌ ملف كود VBA غير موجود
)

echo.
pause
goto MAIN_MENU

:SHOW_FORMS
cls
echo.
echo     🎨 عرض تصميم النماذج...
echo.

if exist "Document_Management_Forms.md" (
    echo     🔄 جاري فتح ملف تصميم النماذج...
    notepad "Document_Management_Forms.md"
    echo     ✅ تم فتح ملف تصميم النماذج!
) else (
    echo     ❌ ملف تصميم النماذج غير موجود
)

echo.
pause
goto MAIN_MENU

:SHOW_GUIDE
cls
echo.
echo     📖 عرض الدليل الشامل...
echo.

if exist "دليل_نظام_إدارة_المستندات.md" (
    echo     🔄 جاري فتح الدليل الشامل...
    notepad "دليل_نظام_إدارة_المستندات.md"
    echo     ✅ تم فتح الدليل الشامل!
) else (
    echo     ❌ الدليل الشامل غير موجود
)

echo.
pause
goto MAIN_MENU

:CREATE_FOLDERS
cls
echo.
echo     📁 إنشاء هيكل المجلدات...
echo.

set BASE_PATH=C:\EnterpriseSystem\Documents

echo     🔄 جاري إنشاء المجلدات...
echo.

mkdir "%BASE_PATH%" 2>nul
mkdir "%BASE_PATH%\Clients" 2>nul
mkdir "%BASE_PATH%\Clients\Contracts" 2>nul
mkdir "%BASE_PATH%\Clients\Correspondence" 2>nul
mkdir "%BASE_PATH%\Clients\Complaints" 2>nul
mkdir "%BASE_PATH%\Sales" 2>nul
mkdir "%BASE_PATH%\Sales\Quotations" 2>nul
mkdir "%BASE_PATH%\Sales\Orders" 2>nul
mkdir "%BASE_PATH%\Purchases" 2>nul
mkdir "%BASE_PATH%\Purchases\PurchaseOrders" 2>nul
mkdir "%BASE_PATH%\Purchases\SupplierInvoices" 2>nul
mkdir "%BASE_PATH%\Inventory" 2>nul
mkdir "%BASE_PATH%\Inventory\StockReports" 2>nul
mkdir "%BASE_PATH%\Inventory\Certificates" 2>nul
mkdir "%BASE_PATH%\Suppliers" 2>nul
mkdir "%BASE_PATH%\Employees" 2>nul
mkdir "%BASE_PATH%\Employees\CVs" 2>nul
mkdir "%BASE_PATH%\Employees\Certificates" 2>nul
mkdir "%BASE_PATH%\Employees\Contracts" 2>nul
mkdir "%BASE_PATH%\Accounts" 2>nul
mkdir "%BASE_PATH%\Invoices" 2>nul
mkdir "%BASE_PATH%\Reports" 2>nul
mkdir "%BASE_PATH%\System" 2>nul
mkdir "%BASE_PATH%\Versions" 2>nul
mkdir "%BASE_PATH%\Templates" 2>nul
mkdir "%BASE_PATH%\Backups" 2>nul
mkdir "%BASE_PATH%\Temp" 2>nul
mkdir "%BASE_PATH%\Logs" 2>nul
mkdir "%BASE_PATH%\Archive" 2>nul

echo     ✅ تم إنشاء هيكل المجلدات بنجاح!
echo     📁 المسار الأساسي: %BASE_PATH%
echo.
echo     🔄 جاري فتح مجلد المستندات...
explorer "%BASE_PATH%"

echo.
pause
goto MAIN_MENU

:SHOW_DETAILS
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                    معلومات مفصلة                            ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     🏢 نظام إدارة المستندات المتكامل
echo     📅 الإصدار: 1.0
echo     👨‍💻 المطور: فريق نظم إدارة المؤسسات
echo     📧 الدعم: <EMAIL>
echo.

echo     📊 مكونات النظام:
echo     ├─ قاعدة بيانات: 10 جداول رئيسية + 2 مشاهد
echo     ├─ كود VBA: 15+ وظيفة متخصصة (750+ سطر)
echo     ├─ النماذج: 6 نماذج احترافية متكاملة
echo     ├─ PowerShell: نظام إدارة وصيانة شامل
echo     ├─ الأمان: 4 مستويات أمان + تشفير
echo     ├─ التقارير: 5+ تقارير مفصلة
echo     └─ التوثيق: دليل شامل 300+ سطر
echo.

echo     🎯 الميزات الرئيسية:
echo     ├─ تحميل المستندات مع التحقق التلقائي
echo     ├─ تنظيم هرمي بالمجلدات والفئات
echo     ├─ بحث متقدم وفلترة ذكية
echo     ├─ مشاركة آمنة مع صلاحيات محددة
echo     ├─ إدارة إصدارات المستندات
echo     ├─ تعليقات وتعاون جماعي
echo     ├─ نسخ احتياطية تلقائية
echo     ├─ تقارير وإحصائيات شاملة
echo     ├─ فحص سلامة النظام
echo     └─ دعم كامل للغة العربية
echo.

echo     🔒 الأمان والحماية:
echo     ├─ تشفير المستندات الحساسة
echo     ├─ مستويات وصول متدرجة
echo     ├─ سجل شامل لجميع العمليات
echo     ├─ حماية من الوصول غير المصرح
echo     └─ نسخ احتياطية مشفرة
echo.

echo     📞 للدعم الفني:
echo     ├─ البريد الإلكتروني: <EMAIL>
echo     ├─ الهاتف: +966-XX-XXXXXXX
echo     └─ الموقع: www.enterprise-system.com
echo.

pause
goto MAIN_MENU

:EXIT
cls
echo.
echo     👋 شكراً لاستخدام نظام إدارة المستندات!
echo.
echo     🌟 تم تطوير نظام شامل ومتكامل يتضمن:
echo     ✅ قاعدة بيانات متقدمة مع 10 جداول
echo     ✅ كود VBA شامل مع 15+ وظيفة
echo     ✅ نماذج احترافية مع واجهة متقدمة
echo     ✅ نظام PowerShell للإدارة والصيانة
echo     ✅ أمان متعدد المستويات مع تشفير
echo     ✅ تقارير وإحصائيات مفصلة
echo     ✅ دعم كامل للغة العربية
echo.
echo     🎉 النظام جاهز للاستخدام الفوري!
echo     📞 للدعم: <EMAIL>
echo.
timeout /t 5 >nul
exit /b 0

:MAIN_MENU
goto :eof
