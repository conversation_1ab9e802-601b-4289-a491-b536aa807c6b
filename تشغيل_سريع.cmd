@echo off
chcp 65001 >nul
color 0B
title نظام إدارة المؤسسات - تشغيل سريع

:MAIN_MENU
cls
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                نظام إدارة المؤسسات الشامل                ║
echo     ║              Enterprise Management System               ║
echo     ║                     الإصدار 1.0                        ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.
echo     🚀 اختر طريقة التشغيل:
echo.
echo     [1] تشغيل سريع (مُوصى به)
echo     [2] تشغيل متقدم مع فحص النظام
echo     [3] إنشاء نسخة احتياطية
echo     [4] إصلاح قاعدة البيانات
echo     [5] معلومات النظام
echo     [0] خروج
echo.
set /p choice="    اختر رقماً (0-5): "

if "%choice%"=="1" goto QUICK_START
if "%choice%"=="2" goto ADVANCED_START
if "%choice%"=="3" goto BACKUP
if "%choice%"=="4" goto REPAIR
if "%choice%"=="5" goto INFO
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:QUICK_START
cls
echo.
echo     🚀 تشغيل سريع للنظام...
echo.
echo     ┌─────────────────────────────────────────────────────────┐
echo     │  بيانات تسجيل الدخول الافتراضية:                        │
echo     │  اسم المستخدم: admin                                   │
echo     │  كلمة المرور: 12345                                    │
echo     └─────────────────────────────────────────────────────────┘
echo.

REM التحقق السريع من وجود الملف
if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ✅ تم العثور على ملف قاعدة البيانات
    echo     🔄 جاري تشغيل النظام...
    echo.
    
    REM تشغيل Access
    start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb"
    
    REM انتظار قصير للتحقق من التشغيل
    timeout /t 2 >nul
    
    echo     ✅ تم تشغيل النظام بنجاح!
    echo.
    echo     📋 ملاحظات:
    echo     • استخدم بيانات تسجيل الدخول المذكورة أعلاه
    echo     • تأكد من تغيير كلمة المرور بعد أول دخول
    echo.
    echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
    pause >nul
    goto MAIN_MENU
) else (
    echo     ❌ لم يتم العثور على ملف قاعدة البيانات!
    echo.
    echo     📋 الحلول المقترحة:
    echo     1. تأكد من وجود الملف في نفس مجلد هذا البرنامج
    echo     2. استخدم الخيار [2] للتشغيل المتقدم
    echo     3. راجع ملف التعليمات
    echo.
    echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
    pause >nul
    goto MAIN_MENU
)

:ADVANCED_START
cls
echo.
echo     🔧 تشغيل متقدم مع فحص النظام...
echo.

REM استدعاء ملف PowerShell المتقدم
if exist "Start-EnterpriseSystem.ps1" (
    echo     🔄 جاري تشغيل الفحص المتقدم...
    powershell -ExecutionPolicy Bypass -File "Start-EnterpriseSystem.ps1"
) else (
    echo     ⚠️ ملف التشغيل المتقدم غير موجود
    echo     🔄 التبديل للتشغيل العادي...
    goto QUICK_START
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:BACKUP
cls
echo.
echo     💾 إنشاء نسخة احتياطية...
echo.

if not exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ❌ لا يمكن العثور على ملف قاعدة البيانات!
    echo.
    echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
    pause >nul
    goto MAIN_MENU
)

REM إنشاء مجلد النسخ الاحتياطية
if not exist "Backups" mkdir "Backups"

REM إنشاء اسم الملف مع التاريخ والوقت
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set mytime=%mytime: =%
set backup_name=نظام_إدارة_المؤسسات_backup_%mydate%_%mytime%.accdb

echo     🔄 جاري إنشاء النسخة الاحتياطية...
copy "نظام_إدارة_المؤسسات.accdb" "Backups\%backup_name%" >nul

if exist "Backups\%backup_name%" (
    echo     ✅ تم إنشاء النسخة الاحتياطية بنجاح!
    echo.
    echo     📁 اسم الملف: %backup_name%
    echo     📂 المجلد: Backups\
    echo.
    
    REM حساب حجم الملف
    for %%A in ("Backups\%backup_name%") do set size=%%~zA
    set /a size_mb=%size%/1024/1024
    echo     📊 حجم الملف: %size_mb% ميجابايت تقريباً
) else (
    echo     ❌ فشل في إنشاء النسخة الاحتياطية!
    echo     تأكد من وجود صلاحيات الكتابة في المجلد
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:REPAIR
cls
echo.
echo     🔧 إصلاح قاعدة البيانات...
echo.

if not exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ❌ لا يمكن العثور على ملف قاعدة البيانات!
    echo.
    echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
    pause >nul
    goto MAIN_MENU
)

echo     ⚠️ تحذير: سيتم إنشاء نسخة احتياطية قبل الإصلاح
echo.
set /p confirm="    هل تريد المتابعة؟ (Y/N): "

if /i not "%confirm%"=="Y" (
    echo     تم إلغاء العملية
    echo.
    echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
    pause >nul
    goto MAIN_MENU
)

REM إنشاء نسخة احتياطية أولاً
echo.
echo     💾 إنشاء نسخة احتياطية قبل الإصلاح...
if not exist "Backups" mkdir "Backups"

for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set mytime=%mytime: =%
set backup_name=نظام_إدارة_المؤسسات_before_repair_%mydate%_%mytime%.accdb

copy "نظام_إدارة_المؤسسات.accdb" "Backups\%backup_name%" >nul

if exist "Backups\%backup_name%" (
    echo     ✅ تم إنشاء النسخة الاحتياطية
    echo.
    echo     🔧 الآن سيتم فتح Access لإصلاح قاعدة البيانات يدوياً
    echo.
    echo     📋 خطوات الإصلاح:
    echo     1. سيتم فتح Access
    echo     2. اذهب إلى File ^> Info ^> Compact ^& Repair Database
    echo     3. أو استخدم Database Tools ^> Compact and Repair Database
    echo.
    echo     اضغط أي مفتاح لفتح Access...
    pause >nul
    
    start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb"
) else (
    echo     ❌ فشل في إنشاء النسخة الاحتياطية!
    echo     لا يمكن المتابعة بدون نسخة احتياطية
)

echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:INFO
cls
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                    معلومات النظام                       ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.
echo     📋 تفاصيل النظام:
echo     ├─ الاسم: نظام إدارة المؤسسات الشامل
echo     ├─ الإصدار: 1.0
echo     ├─ المطور: فريق نظم إدارة المؤسسات
echo     ├─ التاريخ: %date%
echo     └─ الوقت: %time%
echo.
echo     🔑 بيانات تسجيل الدخول الافتراضية:
echo     ├─ اسم المستخدم: admin
echo     └─ كلمة المرور: 12345
echo.
echo     📁 ملفات النظام:
if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ├─ قاعدة البيانات: ✅ موجودة
    for %%A in ("نظام_إدارة_المؤسسات.accdb") do set db_size=%%~zA
    set /a db_size_mb=!db_size!/1024/1024
    echo     ├─ حجم قاعدة البيانات: !db_size_mb! ميجابايت
) else (
    echo     ├─ قاعدة البيانات: ❌ غير موجودة
)

if exist "Complete_Database_Script.sql" (
    echo     ├─ سكريبت قاعدة البيانات: ✅ موجود
) else (
    echo     ├─ سكريبت قاعدة البيانات: ❌ غير موجود
)

if exist "Start-EnterpriseSystem.ps1" (
    echo     ├─ ملف التشغيل المتقدم: ✅ موجود
) else (
    echo     ├─ ملف التشغيل المتقدم: ❌ غير موجود
)

echo     └─ ملف التشغيل السريع: ✅ موجود
echo.
echo     📂 مجلدات النظام:
if exist "Backups" (
    echo     ├─ مجلد النسخ الاحتياطية: ✅ موجود
    dir /b "Backups\*.accdb" 2>nul | find /c ".accdb" > temp_count.txt
    set /p backup_count=<temp_count.txt
    del temp_count.txt >nul 2>&1
    echo     │  └─ عدد النسخ الاحتياطية: !backup_count!
) else (
    echo     ├─ مجلد النسخ الاحتياطية: ❌ غير موجود
)

if exist "Reports" (
    echo     ├─ مجلد التقارير: ✅ موجود
) else (
    echo     ├─ مجلد التقارير: ❌ غير موجود
)

if exist "Logs" (
    echo     └─ مجلد السجلات: ✅ موجود
) else (
    echo     └─ مجلد السجلات: ❌ غير موجود
)

echo.
echo     📞 الدعم الفني:
echo     ├─ البريد الإلكتروني: <EMAIL>
echo     ├─ الهاتف: +966-XX-XXXXXXX
echo     └─ الموقع: www.enterprise-system.com
echo.
echo     اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo     👋 شكراً لاستخدام نظام إدارة المؤسسات!
echo.
echo     🌟 نتمنى لك تجربة ممتازة مع النظام
echo.
timeout /t 2 >nul
exit /b 0
