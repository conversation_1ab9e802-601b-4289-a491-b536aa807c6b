' Enterprise Management System - Client Management Module
' VBA Module for client management functionality
' Module Name: Module_ClientManagement

Option Compare Database
Option Explicit

' ============================================================================
' CLIENT MANAGEMENT FUNCTIONS
' ============================================================================

Public Function GenerateClientCode() As String
    ' Generate next client code (format: CL0001)
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    Dim NextNumber As Long
    
    SQL = "SELECT MAX(Val(Mid(ClientCode, 3))) AS MaxNumber FROM tbl_Clients WHERE ClientCode LIKE 'CL*'"
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If IsNull(rs!MaxNumber) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNumber + 1
    End If
    
    GenerateClientCode = "CL" & Format(NextNumber, "0000")
    
    rs.Close
    Set rs = Nothing
    Exit Function
    
ErrorHandler:
    GenerateClientCode = "CL0001"
    If Not rs Is Nothing Then rs.Close
End Function

Public Function ValidateClientData(ClientForm As Form) As Boolean
    ' Validate client form data before saving
    On Error GoTo ErrorHandler
    
    Dim ErrorMessage As String
    ErrorMessage = ""
    
    ' Required field validation
    If IsNull(ClientForm!CompanyName) Or Trim(ClientForm!CompanyName) = "" Then
        ErrorMessage = ErrorMessage & "- اسم الشركة مطلوب" & vbCrLf
    End If
    
    If IsNull(ClientForm!ClientCode) Or Trim(ClientForm!ClientCode) = "" Then
        ErrorMessage = ErrorMessage & "- رمز العميل مطلوب" & vbCrLf
    End If
    
    ' Email validation
    If Not IsNull(ClientForm!Email) And Trim(ClientForm!Email) <> "" Then
        If Not IsValidEmail(ClientForm!Email) Then
            ErrorMessage = ErrorMessage & "- البريد الإلكتروني غير صحيح" & vbCrLf
        End If
    End If
    
    ' Credit limit validation
    If Not IsNull(ClientForm!CreditLimit) Then
        If ClientForm!CreditLimit < 0 Then
            ErrorMessage = ErrorMessage & "- حد الائتمان لا يمكن أن يكون سالباً" & vbCrLf
        End If
    End If
    
    ' Payment terms validation
    If Not IsNull(ClientForm!PaymentTerms) Then
        If ClientForm!PaymentTerms < 0 Or ClientForm!PaymentTerms > 365 Then
            ErrorMessage = ErrorMessage & "- شروط الدفع يجب أن تكون بين 0 و 365 يوم" & vbCrLf
        End If
    End If
    
    ' Check for duplicate client code
    If Not IsNull(ClientForm!ClientCode) Then
        If IsClientCodeExists(ClientForm!ClientCode, Nz(ClientForm!ClientID, 0)) Then
            ErrorMessage = ErrorMessage & "- رمز العميل موجود مسبقاً" & vbCrLf
        End If
    End If
    
    ' Show validation errors
    If ErrorMessage <> "" Then
        MsgBox "يرجى تصحيح الأخطاء التالية:" & vbCrLf & vbCrLf & ErrorMessage, vbExclamation, "خطأ في البيانات"
        ValidateClientData = False
    Else
        ValidateClientData = True
    End If
    
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في التحقق من البيانات: " & Err.Description, vbCritical, "خطأ"
    ValidateClientData = False
End Function

Public Function IsValidEmail(Email As String) As Boolean
    ' Simple email validation
    On Error GoTo ErrorHandler
    
    Dim AtPos As Integer
    Dim DotPos As Integer
    
    AtPos = InStr(Email, "@")
    DotPos = InStrRev(Email, ".")
    
    If AtPos > 1 And DotPos > AtPos + 1 And DotPos < Len(Email) Then
        IsValidEmail = True
    Else
        IsValidEmail = False
    End If
    
    Exit Function
    
ErrorHandler:
    IsValidEmail = False
End Function

Public Function IsClientCodeExists(ClientCode As String, ExcludeClientID As Long) As Boolean
    ' Check if client code already exists
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    SQL = "SELECT COUNT(*) AS CodeCount FROM tbl_Clients WHERE ClientCode = '" & ClientCode & "'"
    
    If ExcludeClientID > 0 Then
        SQL = SQL & " AND ClientID <> " & ExcludeClientID
    End If
    
    Set rs = CurrentDb.OpenRecordset(SQL)
    IsClientCodeExists = (rs!CodeCount > 0)
    rs.Close
    Set rs = Nothing
    
    Exit Function
    
ErrorHandler:
    IsClientCodeExists = False
    If Not rs Is Nothing Then rs.Close
End Function

Public Function SaveClientData(ClientForm As Form) As Boolean
    ' Save client data with validation and audit trail
    On Error GoTo ErrorHandler
    
    ' Validate data first
    If Not ValidateClientData(ClientForm) Then
        SaveClientData = False
        Exit Function
    End If
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim IsNewRecord As Boolean
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_Clients", dbOpenDynaset)
    
    ' Check if this is a new record
    IsNewRecord = IsNull(ClientForm!ClientID) Or ClientForm!ClientID = 0
    
    If Not IsNewRecord Then
        ' Find existing record
        rs.FindFirst "ClientID = " & ClientForm!ClientID
        If rs.NoMatch Then
            MsgBox "لم يتم العثور على العميل", vbExclamation, "خطأ"
            SaveClientData = False
            Exit Function
        End If
    Else
        ' Add new record
        rs.AddNew
        rs!ClientCode = GenerateClientCode()
        rs!CreatedDate = Now()
        rs!CreatedBy = g_CurrentUserID
    End If
    
    ' Update fields
    rs!CompanyName = Nz(ClientForm!CompanyName, "")
    rs!ContactPerson = Nz(ClientForm!ContactPerson, "")
    rs!Phone = Nz(ClientForm!Phone, "")
    rs!Mobile = Nz(ClientForm!Mobile, "")
    rs!Email = Nz(ClientForm!Email, "")
    rs!Website = Nz(ClientForm!Website, "")
    rs!Address = Nz(ClientForm!Address, "")
    rs!City = Nz(ClientForm!City, "")
    rs!Country = Nz(ClientForm!Country, "")
    rs!TaxID = Nz(ClientForm!TaxID, "")
    rs!CreditLimit = Nz(ClientForm!CreditLimit, 0)
    rs!PaymentTerms = Nz(ClientForm!PaymentTerms, 30)
    rs!ClientType = Nz(ClientForm!ClientType, "Customer")
    rs!IsActive = Nz(ClientForm!IsActive, True)
    rs!Notes = Nz(ClientForm!Notes, "")
    
    ' Save changes
    rs.Update
    
    ' Get the ClientID for new records
    If IsNewRecord Then
        ClientForm!ClientID = rs!ClientID
        ClientForm!ClientCode = rs!ClientCode
    End If
    
    ' Log the activity
    If IsNewRecord Then
        LogUserActivity "CLIENT_CREATE", "Created new client: " & rs!CompanyName
    Else
        LogUserActivity "CLIENT_UPDATE", "Updated client: " & rs!CompanyName
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    SaveClientData = True
    MsgBox "تم حفظ بيانات العميل بنجاح", vbInformation, "نجح الحفظ"
    
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في حفظ بيانات العميل: " & Err.Description, vbCritical, "خطأ"
    SaveClientData = False
    If Not rs Is Nothing Then rs.Close
    If Not db Is Nothing Then Set db = Nothing
End Function

Public Function DeleteClient(ClientID As Long) As Boolean
    ' Soft delete client (mark as inactive)
    On Error GoTo ErrorHandler
    
    ' Check if client has related records
    If HasClientTransactions(ClientID) Then
        If MsgBox("هذا العميل له معاملات مسجلة. هل تريد إلغاء تفعيله بدلاً من حذفه؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
            ' Deactivate instead of delete
            CurrentDb.Execute "UPDATE tbl_Clients SET IsActive = False WHERE ClientID = " & ClientID
            LogUserActivity "CLIENT_DEACTIVATE", "Deactivated client ID: " & ClientID
            MsgBox "تم إلغاء تفعيل العميل بنجاح", vbInformation, "تم الإلغاء"
            DeleteClient = True
        Else
            DeleteClient = False
        End If
    Else
        ' Safe to delete
        If MsgBox("هل أنت متأكد من حذف هذا العميل؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
            CurrentDb.Execute "DELETE FROM tbl_Clients WHERE ClientID = " & ClientID
            LogUserActivity "CLIENT_DELETE", "Deleted client ID: " & ClientID
            MsgBox "تم حذف العميل بنجاح", vbInformation, "تم الحذف"
            DeleteClient = True
        Else
            DeleteClient = False
        End If
    End If
    
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في حذف العميل: " & Err.Description, vbCritical, "خطأ"
    DeleteClient = False
End Function

Public Function HasClientTransactions(ClientID As Long) As Boolean
    ' Check if client has any transactions
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim TransactionCount As Long
    
    ' Check sales orders
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS TransCount FROM tbl_SalesOrders WHERE ClientID = " & ClientID)
    TransactionCount = TransactionCount + rs!TransCount
    rs.Close
    
    ' Check invoices
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS TransCount FROM tbl_Invoices WHERE ClientID = " & ClientID)
    TransactionCount = TransactionCount + rs!TransCount
    rs.Close
    
    ' Check quotations
    Set rs = CurrentDb.OpenRecordset("SELECT COUNT(*) AS TransCount FROM tbl_Quotations WHERE ClientID = " & ClientID)
    TransactionCount = TransactionCount + rs!TransCount
    rs.Close
    
    HasClientTransactions = (TransactionCount > 0)
    
    Exit Function
    
ErrorHandler:
    HasClientTransactions = True ' Assume has transactions to be safe
    If Not rs Is Nothing Then rs.Close
End Function

' ============================================================================
' CLIENT SEARCH AND FILTER FUNCTIONS
' ============================================================================

Public Function SearchClients(SearchTerm As String, SearchType As String) As String
    ' Build SQL for client search
    On Error GoTo ErrorHandler
    
    Dim SQL As String
    Dim WhereClause As String
    
    SQL = "SELECT ClientID, ClientCode, CompanyName, ContactPerson, Phone, Email, " & _
          "CreditLimit, PaymentTerms, ClientType, IsActive " & _
          "FROM tbl_Clients "
    
    If Trim(SearchTerm) <> "" Then
        Select Case SearchType
            Case "CompanyName"
                WhereClause = "CompanyName LIKE '*" & SearchTerm & "*'"
            Case "ClientCode"
                WhereClause = "ClientCode LIKE '*" & SearchTerm & "*'"
            Case "ContactPerson"
                WhereClause = "ContactPerson LIKE '*" & SearchTerm & "*'"
            Case "Phone"
                WhereClause = "Phone LIKE '*" & SearchTerm & "*' OR Mobile LIKE '*" & SearchTerm & "*'"
            Case "Email"
                WhereClause = "Email LIKE '*" & SearchTerm & "*'"
            Case "All"
                WhereClause = "CompanyName LIKE '*" & SearchTerm & "*' OR " & _
                             "ClientCode LIKE '*" & SearchTerm & "*' OR " & _
                             "ContactPerson LIKE '*" & SearchTerm & "*' OR " & _
                             "Phone LIKE '*" & SearchTerm & "*' OR " & _
                             "Email LIKE '*" & SearchTerm & "*'"
        End Select
        
        SQL = SQL & "WHERE " & WhereClause & " "
    End If
    
    SQL = SQL & "ORDER BY CompanyName"
    
    SearchClients = SQL
    Exit Function
    
ErrorHandler:
    SearchClients = "SELECT * FROM tbl_Clients ORDER BY CompanyName"
End Function

Public Function FilterClientsByType(ClientType As String) As String
    ' Filter clients by type
    Dim SQL As String
    
    SQL = "SELECT ClientID, ClientCode, CompanyName, ContactPerson, Phone, Email, " & _
          "CreditLimit, PaymentTerms, ClientType, IsActive " & _
          "FROM tbl_Clients "
    
    If ClientType <> "All" Then
        SQL = SQL & "WHERE ClientType = '" & ClientType & "' "
    End If
    
    SQL = SQL & "ORDER BY CompanyName"
    
    FilterClientsByType = SQL
End Function

Public Function FilterClientsByStatus(IsActive As Boolean) As String
    ' Filter clients by active status
    Dim SQL As String
    
    SQL = "SELECT ClientID, ClientCode, CompanyName, ContactPerson, Phone, Email, " & _
          "CreditLimit, PaymentTerms, ClientType, IsActive " & _
          "FROM tbl_Clients " & _
          "WHERE IsActive = " & IsActive & " " & _
          "ORDER BY CompanyName"
    
    FilterClientsByStatus = SQL
End Function

' ============================================================================
' CLIENT REPORTING FUNCTIONS
' ============================================================================

Public Function GetClientSummary(ClientID As Long) As String
    ' Get client summary information
    On Error GoTo ErrorHandler
    
    Dim rs As DAO.Recordset
    Dim Summary As String
    Dim SQL As String
    
    ' Get client basic info
    SQL = "SELECT CompanyName, ClientCode, ContactPerson, Phone, Email FROM tbl_Clients WHERE ClientID = " & ClientID
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        Summary = "العميل: " & rs!CompanyName & vbCrLf
        Summary = Summary & "الرمز: " & rs!ClientCode & vbCrLf
        Summary = Summary & "جهة الاتصال: " & Nz(rs!ContactPerson, "غير محدد") & vbCrLf
        Summary = Summary & "الهاتف: " & Nz(rs!Phone, "غير محدد") & vbCrLf
        Summary = Summary & "البريد: " & Nz(rs!Email, "غير محدد") & vbCrLf & vbCrLf
    End If
    rs.Close
    
    ' Get sales summary
    SQL = "SELECT COUNT(*) AS OrderCount, ISNULL(SUM(TotalAmount), 0) AS TotalSales " & _
          "FROM tbl_SalesOrders WHERE ClientID = " & ClientID
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        Summary = Summary & "إجمالي الطلبات: " & rs!OrderCount & vbCrLf
        Summary = Summary & "إجمالي المبيعات: " & Format(rs!TotalSales, "Currency") & vbCrLf
    End If
    rs.Close
    
    ' Get outstanding balance
    SQL = "SELECT ISNULL(SUM(TotalAmount - PaidAmount), 0) AS OutstandingBalance " & _
          "FROM tbl_Invoices WHERE ClientID = " & ClientID & " AND Status <> 'Paid'"
    Set rs = CurrentDb.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        Summary = Summary & "الرصيد المستحق: " & Format(rs!OutstandingBalance, "Currency") & vbCrLf
    End If
    rs.Close
    
    GetClientSummary = Summary
    Exit Function
    
ErrorHandler:
    GetClientSummary = "خطأ في جلب ملخص العميل"
    If Not rs Is Nothing Then rs.Close
End Function

Public Sub ExportClientsToExcel()
    ' Export clients list to Excel
    On Error GoTo ErrorHandler
    
    Dim SQL As String
    Dim ExportPath As String
    
    SQL = "SELECT ClientCode AS [رمز العميل], CompanyName AS [اسم الشركة], " & _
          "ContactPerson AS [جهة الاتصال], Phone AS [الهاتف], Email AS [البريد الإلكتروني], " & _
          "CreditLimit AS [حد الائتمان], PaymentTerms AS [شروط الدفع], " & _
          "ClientType AS [نوع العميل], IIF(IsActive, 'نشط', 'غير نشط') AS [الحالة] " & _
          "FROM tbl_Clients ORDER BY CompanyName"
    
    ExportPath = "C:\Temp\ClientsList_" & Format(Now(), "yyyymmdd_hhnnss") & ".xlsx"
    
    ' Create temp directory if not exists
    If Dir("C:\Temp\", vbDirectory) = "" Then
        MkDir "C:\Temp\"
    End If
    
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, , ExportPath, True, , SQL
    
    MsgBox "تم تصدير قائمة العملاء إلى: " & vbCrLf & ExportPath, vbInformation, "تم التصدير"
    
    ' Open the exported file
    Shell "explorer.exe " & ExportPath, vbNormalFocus
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في تصدير قائمة العملاء: " & Err.Description, vbCritical, "خطأ"
End Sub

' ============================================================================
' CLIENT CONTACT MANAGEMENT
' ============================================================================

Public Function AddClientContact(ClientID As Long, ContactName As String, Position As String, Phone As String, Email As String, IsPrimary As Boolean) As Boolean
    ' Add new contact for client
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("tbl_ClientContacts", dbOpenDynaset)
    
    ' If this is primary contact, remove primary flag from others
    If IsPrimary Then
        db.Execute "UPDATE tbl_ClientContacts SET IsPrimary = False WHERE ClientID = " & ClientID
    End If
    
    ' Add new contact
    rs.AddNew
    rs!ClientID = ClientID
    rs!ContactName = ContactName
    rs!Position = Nz(Position, "")
    rs!Phone = Nz(Phone, "")
    rs!Email = Nz(Email, "")
    rs!IsPrimary = IsPrimary
    rs.Update
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    AddClientContact = True
    LogUserActivity "CLIENT_CONTACT_ADD", "Added contact for client ID: " & ClientID
    
    Exit Function
    
ErrorHandler:
    AddClientContact = False
    If Not rs Is Nothing Then rs.Close
    If Not db Is Nothing Then Set db = Nothing
End Function
