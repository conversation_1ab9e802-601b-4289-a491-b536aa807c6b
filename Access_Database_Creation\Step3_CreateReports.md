# إنشاء التقارير - الخطوة الثالثة
## Creating Reports - Step Three

### تقرير قائمة العملاء (rpt_ClientList)

#### إنشاء التقرير:
```
1. اذ<PERSON><PERSON> إلى Create > Report Wizard
2. اختر الجدول: tbl_Clients
3. اختر الحقول التالية:
   - ClientCode (رمز العميل)
   - CompanyName (اسم الشركة)
   - ContactPerson (جهة الاتصال)
   - Phone (الهاتف)
   - Email (البريد الإلكتروني)
   - CreditLimit (حد الائتمان)
   - ClientType (نوع العميل)
   - IsActive (نشط)

4. Grouping: ClientType
5. Sort: CompanyName
6. Layout: Stepped
7. احفظ باسم "rpt_ClientList"
```

#### تخصيص التقرير:
```
تخصيص الرأس:
1. Report Header:
   - أضف Label: "قائمة العملاء - Client List"
   - Font: <PERSON><PERSON><PERSON>, 16pt, Bold
   - أضف التاريخ: =Now()
   - أضف رقم الصفحة: ="صفحة " & [Page] & " من " & [Pages]

2. Page Header:
   - عدل عناوين الأعمدة للعربية:
     - "رمز العميل"
     - "اسم الشركة" 
     - "جهة الاتصال"
     - "الهاتف"
     - "البريد الإلكتروني"
     - "حد الائتمان"
     - "الحالة"

3. ClientType Header:
   - أضف Label: ="نوع العميل: " & [ClientType]
   - BackColor: RGB(230, 230, 230)

4. Detail Section:
   - اضبط عرض الأعمدة
   - أضف تنسيق للعملة: Format([CreditLimit], "Currency")
   - أضف تنسيق للحالة: =IIF([IsActive], "نشط", "غير نشط")

5. Report Footer:
   - أضف إجمالي العملاء: ="إجمالي العملاء: " & Count([ClientID])
```

---

### تقرير كشف حساب العميل (rpt_ClientStatement)

#### إنشاء الاستعلام الأساسي:
```sql
CREATE QUERY qry_ClientStatement AS
SELECT 
    c.ClientID,
    c.CompanyName,
    c.ContactPerson,
    c.Phone,
    c.Email,
    c.CreditLimit,
    'فاتورة' AS TransactionType,
    i.InvoiceNumber AS DocumentNumber,
    i.InvoiceDate AS TransactionDate,
    i.TotalAmount AS Amount,
    i.PaidAmount,
    (i.TotalAmount - i.PaidAmount) AS Balance
FROM tbl_Clients c 
INNER JOIN tbl_Invoices i ON c.ClientID = i.ClientID
WHERE i.Status <> 'Cancelled'

UNION ALL

SELECT 
    c.ClientID,
    c.CompanyName,
    c.ContactPerson,
    c.Phone,
    c.Email,
    c.CreditLimit,
    'دفعة' AS TransactionType,
    p.PaymentNumber AS DocumentNumber,
    p.PaymentDate AS TransactionDate,
    -p.Amount AS Amount,
    0 AS PaidAmount,
    -p.Amount AS Balance
FROM tbl_Clients c 
INNER JOIN tbl_Payments p ON c.ClientID = p.ClientID

ORDER BY ClientID, TransactionDate;
```

#### إنشاء التقرير:
```
1. Create > Report Wizard
2. اختر qry_ClientStatement
3. اختر جميع الحقول
4. Group by: ClientID
5. Sort by: TransactionDate
6. احفظ باسم "rpt_ClientStatement"
```

#### تخصيص كشف الحساب:
```
1. Report Header:
   - العنوان: "كشف حساب العميل - Client Statement"
   - التاريخ والوقت

2. ClientID Header:
   - معلومات العميل:
     - اسم الشركة: [CompanyName]
     - جهة الاتصال: [ContactPerson]
     - الهاتف: [Phone]
     - حد الائتمان: [CreditLimit]

3. Page Header (داخل المجموعة):
   - عناوين الأعمدة:
     - "التاريخ"
     - "نوع المعاملة"
     - "رقم المستند"
     - "المبلغ"
     - "المدفوع"
     - "الرصيد"

4. Detail:
   - عرض تفاصيل المعاملات
   - تنسيق العملة للمبالغ

5. ClientID Footer:
   - إجمالي المبلغ: =Sum([Amount])
   - إجمالي المدفوع: =Sum([PaidAmount])
   - الرصيد المستحق: =Sum([Balance])
```

---

### تقرير المخزون (rpt_InventoryReport)

#### إنشاء الاستعلام:
```sql
CREATE QUERY qry_InventoryReport AS
SELECT 
    p.ProductCode,
    p.ProductName,
    c.CategoryName,
    p.UnitOfMeasure,
    p.CostPrice,
    p.SellingPrice,
    s.CurrentStock,
    s.ReservedStock,
    (s.CurrentStock - s.ReservedStock) AS AvailableStock,
    (s.CurrentStock * p.CostPrice) AS InventoryValue,
    IIF(s.CurrentStock <= p.ReorderPoint, "مخزون منخفض", "طبيعي") AS StockStatus,
    sup.CompanyName AS SupplierName
FROM ((tbl_Products p 
LEFT JOIN tbl_Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN tbl_StockLevels s ON p.ProductID = s.ProductID)
LEFT JOIN tbl_Suppliers sup ON p.SupplierID = sup.SupplierID
WHERE p.IsActive = True
ORDER BY c.CategoryName, p.ProductName;
```

#### إنشاء التقرير:
```
1. Create > Report Wizard
2. اختر qry_InventoryReport
3. اختر الحقول المطلوبة
4. Group by: CategoryName
5. Sort by: ProductName
6. احفظ باسم "rpt_InventoryReport"
```

#### تخصيص تقرير المخزون:
```
1. Report Header:
   - العنوان: "تقرير المخزون - Inventory Report"
   - التاريخ: =Format(Now(), "dd/mm/yyyy")

2. CategoryName Header:
   - اسم الفئة: ="فئة: " & [CategoryName]
   - خط عريض وخلفية ملونة

3. Page Header:
   - عناوين الأعمدة:
     - "رمز المنتج"
     - "اسم المنتج"
     - "وحدة القياس"
     - "سعر التكلفة"
     - "سعر البيع"
     - "المخزون الحالي"
     - "المخزون المتاح"
     - "قيمة المخزون"
     - "حالة المخزون"
     - "المورد"

4. Detail:
   - عرض تفاصيل المنتجات
   - تلوين الصفوف حسب حالة المخزون:
     - أحمر للمخزون المنخفض
     - أخضر للمخزون الطبيعي

5. CategoryName Footer:
   - إجمالي المنتجات في الفئة: =Count([ProductCode])
   - إجمالي قيمة المخزون: =Sum([InventoryValue])

6. Report Footer:
   - إجمالي عام للمنتجات: =Count([ProductCode])
   - إجمالي عام لقيمة المخزون: =Sum([InventoryValue])
```

---

### تقرير المبيعات الشهرية (rpt_MonthlySales)

#### إنشاء الاستعلام:
```sql
CREATE QUERY qry_MonthlySales AS
SELECT 
    Format([OrderDate], "yyyy-mm") AS MonthYear,
    Format([OrderDate], "mmmm yyyy") AS MonthName,
    COUNT(*) AS OrderCount,
    SUM([TotalAmount]) AS TotalSales,
    AVG([TotalAmount]) AS AverageOrder,
    c.CompanyName AS ClientName,
    so.OrderNumber,
    so.OrderDate,
    so.TotalAmount,
    so.Status
FROM tbl_SalesOrders so 
INNER JOIN tbl_Clients c ON so.ClientID = c.ClientID
WHERE so.Status <> 'Cancelled'
  AND so.OrderDate >= DateAdd("m", -12, Date())
GROUP BY Format([OrderDate], "yyyy-mm"), Format([OrderDate], "mmmm yyyy"),
         c.CompanyName, so.OrderNumber, so.OrderDate, so.TotalAmount, so.Status
ORDER BY Format([OrderDate], "yyyy-mm") DESC, so.OrderDate DESC;
```

#### تخصيص التقرير:
```
1. Report Header:
   - العنوان: "تقرير المبيعات الشهرية - Monthly Sales Report"
   - فترة التقرير: آخر 12 شهر

2. MonthYear Header:
   - اسم الشهر: [MonthName]
   - إحصائيات الشهر:
     - عدد الطلبات: [OrderCount]
     - إجمالي المبيعات: [TotalSales]
     - متوسط الطلب: [AverageOrder]

3. Detail:
   - تفاصيل كل طلب:
     - رقم الطلب
     - التاريخ
     - اسم العميل
     - المبلغ
     - الحالة

4. MonthYear Footer:
   - إجماليات الشهر

5. Report Footer:
   - الإجماليات العامة لكامل الفترة
```

---

### إنشاء قائمة التقارير (frm_Reports)

#### إنشاء نموذج التقارير:
```
1. Create > Form Design
2. احفظ باسم "frm_Reports"
3. خصائص النموذج:
   - Caption: إدارة التقارير
   - Modal: No
   - PopUp: No
   - BorderStyle: Dialog
```

#### إضافة أزرار التقارير:
```
1. btn_ClientList:
   - Caption: قائمة العملاء
   - OnClick: DoCmd.OpenReport "rpt_ClientList", acViewPreview

2. btn_ClientStatement:
   - Caption: كشف حساب العميل
   - OnClick: OpenClientStatement()

3. btn_InventoryReport:
   - Caption: تقرير المخزون
   - OnClick: DoCmd.OpenReport "rpt_InventoryReport", acViewPreview

4. btn_MonthlySales:
   - Caption: المبيعات الشهرية
   - OnClick: DoCmd.OpenReport "rpt_MonthlySales", acViewPreview

5. btn_ExportToExcel:
   - Caption: تصدير إلى Excel
   - OnClick: ExportReportsToExcel()

6. btn_Close:
   - Caption: إغلاق
   - OnClick: DoCmd.Close acForm, Me.Name
```

#### كود VBA لنموذج التقارير:
```vba
Private Sub OpenClientStatement()
    Dim ClientID As Long
    Dim ClientName As String
    
    ' فتح نموذج اختيار العميل
    DoCmd.OpenForm "frm_SelectClient", acDialog
    
    If Not IsNull(TempVars("SelectedClientID")) Then
        ClientID = TempVars("SelectedClientID")
        DoCmd.OpenReport "rpt_ClientStatement", acViewPreview, , "ClientID = " & ClientID
        TempVars.Remove "SelectedClientID"
    End If
End Sub

Private Sub ExportReportsToExcel()
    Dim ExportPath As String
    ExportPath = "C:\Reports\"
    
    ' إنشاء مجلد التقارير
    If Dir(ExportPath, vbDirectory) = "" Then
        MkDir ExportPath
    End If
    
    ' تصدير التقارير
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, "qry_InventoryReport", ExportPath & "تقرير_المخزون_" & Format(Now(), "yyyymmdd") & ".xlsx", True
    
    MsgBox "تم تصدير التقارير إلى: " & ExportPath, vbInformation
End Sub
```

### إضافة التقارير للنموذج الرئيسي:

```vba
' في frm_MainNavigation
Private Sub btn_Reports_Click()
    DoCmd.OpenForm "frm_Reports"
End Sub
```

### اختبار التقارير:

```
1. افتح النموذج الرئيسي
2. اضغط على "إدارة التقارير"
3. اختبر كل تقرير:
   - قائمة العملاء
   - تقرير المخزون
   - المبيعات الشهرية
4. اختبر التصدير إلى Excel
5. تأكد من التنسيق والبيانات
```

**الخطوة التالية:** إضافة المزيد من الوحدات والميزات المتقدمة
