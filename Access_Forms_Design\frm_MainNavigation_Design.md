# Main Navigation Form Design (frm_MainNavigation)
## نموذج القائمة الرئيسية - تصميم مفصل

### Form Properties / خصائص النموذج
```
Form Name: frm_MainNavigation
Caption: نظام إدارة المؤسسات - Enterprise Management System
Modal: No
PopUp: No
AutoCenter: Yes
AutoResize: No
BorderStyle: Dialog
RecordSelectors: No
NavigationButtons: No
DividingLines: No
ScrollBars: Neither
MinMaxButtons: Min Enabled
CloseButton: Yes
ControlBox: Yes
Width: 1200 (twips: 17280)
Height: 800 (twips: 11520)
BackColor: RGB(245, 245, 245) - Light Gray
```

### Header Section / قسم الرأس
```
Height: 100 (twips: 1440)
BackColor: RGB(46, 134, 171) - Main Blue

Controls:
1. lbl_SystemTitle (Label)
   - Caption: "نظام إدارة المؤسسات الشامل"
   - Font: <PERSON>hom<PERSON>, 18pt, Bold, White
   - Left: 50, Top: 20, Width: 400, Height: 30
   - TextAlign: Left

2. lbl_SystemTitleEn (Label)
   - Caption: "Enterprise Management System"
   - Font: Segoe UI, 14pt, Regular, White
   - Left: 50, Top: 55, Width: 350, Height: 25
   - TextAlign: Left

3. lbl_UserInfo (Label)
   - Caption: "مرحباً، [اسم المستخدم]"
   - Font: Tahoma, 12pt, Regular, White
   - Left: 800, Top: 20, Width: 300, Height: 25
   - TextAlign: Right

4. lbl_DateTime (Label)
   - Caption: [Current Date/Time]
   - Font: Tahoma, 10pt, Regular, White
   - Left: 800, Top: 50, Width: 300, Height: 20
   - TextAlign: Right

5. btn_Logout (Command Button)
   - Caption: "تسجيل خروج"
   - Font: Tahoma, 10pt, Regular
   - Left: 1100, Top: 30, Width: 80, Height: 30
   - BackColor: RGB(244, 67, 54) - Red
   - ForeColor: White
```

### Main Content Area / منطقة المحتوى الرئيسي
```
Height: 650 (twips: 9360)
BackColor: RGB(255, 255, 255) - White

Module Buttons Layout (4 columns x 4 rows):
Button Size: Width: 200, Height: 120
Spacing: Horizontal: 50, Vertical: 40
Starting Position: Left: 100, Top: 150
```

#### Row 1 - الصف الأول
```
1. btn_Dashboard (Command Button)
   - Caption: "لوحة التحكم" & vbCrLf & "Dashboard"
   - Left: 100, Top: 150
   - Icon: Dashboard icon (64x64)
   - BackColor: RGB(46, 134, 171) - Blue
   - OnClick: OpenDashboard()

2. btn_Clients (Command Button)
   - Caption: "إدارة العملاء" & vbCrLf & "Client Management"
   - Left: 350, Top: 150
   - Icon: Clients icon (64x64)
   - BackColor: RGB(76, 175, 80) - Green
   - OnClick: OpenClientManagement()

3. btn_Sales (Command Button)
   - Caption: "إدارة المبيعات" & vbCrLf & "Sales Management"
   - Left: 600, Top: 150
   - Icon: Sales icon (64x64)
   - BackColor: RGB(156, 39, 176) - Purple
   - OnClick: OpenSalesManagement()

4. btn_Purchases (Command Button)
   - Caption: "إدارة المشتريات" & vbCrLf & "Purchase Management"
   - Left: 850, Top: 150
   - Icon: Purchase icon (64x64)
   - BackColor: RGB(255, 152, 0) - Orange
   - OnClick: OpenPurchaseManagement()
```

#### Row 2 - الصف الثاني
```
5. btn_Inventory (Command Button)
   - Caption: "إدارة المخازن" & vbCrLf & "Inventory Management"
   - Left: 100, Top: 310
   - Icon: Warehouse icon (64x64)
   - BackColor: RGB(121, 85, 72) - Brown
   - OnClick: OpenInventoryManagement()

6. btn_Suppliers (Command Button)
   - Caption: "إدارة الموردين" & vbCrLf & "Supplier Management"
   - Left: 350, Top: 310
   - Icon: Supplier icon (64x64)
   - BackColor: RGB(0, 150, 136) - Teal
   - OnClick: OpenSupplierManagement()

7. btn_Employees (Command Button)
   - Caption: "إدارة الموظفين" & vbCrLf & "Employee Management"
   - Left: 600, Top: 310
   - Icon: Employee icon (64x64)
   - BackColor: RGB(63, 81, 181) - Indigo
   - OnClick: OpenEmployeeManagement()

8. btn_Accounting (Command Button)
   - Caption: "إدارة الحسابات" & vbCrLf & "Accounting Management"
   - Left: 850, Top: 310
   - Icon: Accounting icon (64x64)
   - BackColor: RGB(255, 87, 34) - Deep Orange
   - OnClick: OpenAccountingManagement()
```

#### Row 3 - الصف الثالث
```
9. btn_Invoices (Command Button)
   - Caption: "إدارة الفواتير" & vbCrLf & "Invoice Management"
   - Left: 100, Top: 470
   - Icon: Invoice icon (64x64)
   - BackColor: RGB(3, 169, 244) - Light Blue
   - OnClick: OpenInvoiceManagement()

10. btn_Reports (Command Button)
    - Caption: "إدارة التقارير" & vbCrLf & "Reporting Management"
    - Left: 350, Top: 470
    - Icon: Reports icon (64x64)
    - BackColor: RGB(139, 195, 74) - Light Green
    - OnClick: OpenReportingManagement()

11. btn_Users (Command Button)
    - Caption: "إدارة الصلاحيات" & vbCrLf & "User Management"
    - Left: 600, Top: 470
    - Icon: Security icon (64x64)
    - BackColor: RGB(244, 67, 54) - Red
    - OnClick: OpenUserManagement()

12. btn_Database (Command Button)
    - Caption: "إدارة قاعدة البيانات" & vbCrLf & "Database Management"
    - Left: 850, Top: 470
    - Icon: Database icon (64x64)
    - BackColor: RGB(96, 125, 139) - Blue Grey
    - OnClick: OpenDatabaseManagement()
```

### Footer Section / قسم التذييل
```
Height: 50 (twips: 720)
BackColor: RGB(245, 245, 245) - Light Gray

Controls:
1. lbl_CompanyInfo (Label)
   - Caption: "© 2025 Enterprise Management System - All Rights Reserved"
   - Font: Tahoma, 9pt, Regular, Gray
   - Left: 50, Top: 15, Width: 500, Height: 20
   - TextAlign: Left

2. lbl_Version (Label)
   - Caption: "Version 1.0"
   - Font: Tahoma, 9pt, Regular, Gray
   - Left: 1000, Top: 15, Width: 100, Height: 20
   - TextAlign: Right
```

### VBA Code for Form Events / كود VBA لأحداث النموذج

```vba
' Form Load Event
Private Sub Form_Load()
    ' Update user information
    Me.lbl_UserInfo.Caption = "مرحباً، " & g_CurrentUser
    
    ' Update date/time
    Me.lbl_DateTime.Caption = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
    
    ' Set button permissions based on user role
    SetButtonPermissions
    
    ' Start timer for date/time update
    Me.TimerInterval = 60000 ' Update every minute
End Sub

' Timer Event for updating date/time
Private Sub Form_Timer()
    Me.lbl_DateTime.Caption = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
End Sub

' Set button permissions based on user role
Private Sub SetButtonPermissions()
    Me.btn_Dashboard.Enabled = HasPermission("Dashboard", "Read")
    Me.btn_Clients.Enabled = HasPermission("Client Management", "Read")
    Me.btn_Sales.Enabled = HasPermission("Sales Management", "Read")
    Me.btn_Purchases.Enabled = HasPermission("Purchase Management", "Read")
    Me.btn_Inventory.Enabled = HasPermission("Inventory Management", "Read")
    Me.btn_Suppliers.Enabled = HasPermission("Supplier Management", "Read")
    Me.btn_Employees.Enabled = HasPermission("Employee Management", "Read")
    Me.btn_Accounting.Enabled = HasPermission("Accounting Management", "Read")
    Me.btn_Invoices.Enabled = HasPermission("Invoice Management", "Read")
    Me.btn_Reports.Enabled = HasPermission("Reporting Management", "Read")
    Me.btn_Users.Enabled = HasPermission("User Management", "Read")
    Me.btn_Database.Enabled = HasPermission("Database Management", "Read")
End Sub

' Logout button click event
Private Sub btn_Logout_Click()
    LogoutUser
End Sub

' Form Close Event
Private Sub Form_Close()
    ExitApplication
End Sub
```

### Button Hover Effects / تأثيرات التمرير على الأزرار

```vba
' Mouse Enter Event (for each button)
Private Sub btn_Dashboard_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
    Me.btn_Dashboard.BackColor = RGB(30, 100, 140) ' Darker blue
    Me.btn_Dashboard.BorderColor = RGB(255, 255, 255)
    Me.btn_Dashboard.BorderWidth = 2
End Sub

' Mouse Leave Event (for each button)
Private Sub Detail_MouseMove(Button As Integer, Shift As Integer, X As Single, Y As Single)
    ' Reset all button colors to original
    Me.btn_Dashboard.BackColor = RGB(46, 134, 171)
    Me.btn_Dashboard.BorderWidth = 1
    ' ... repeat for all buttons
End Sub
```

### Responsive Design Considerations / اعتبارات التصميم المتجاوب

```vba
' Form Resize Event
Private Sub Form_Resize()
    ' Adjust button positions based on form size
    Dim ButtonWidth As Integer
    Dim ButtonHeight As Integer
    Dim SpacingX As Integer
    Dim SpacingY As Integer
    
    ButtonWidth = 200
    ButtonHeight = 120
    SpacingX = (Me.Width - (4 * ButtonWidth)) / 5
    SpacingY = 40
    
    ' Reposition buttons dynamically
    ' Row 1
    Me.btn_Dashboard.Left = SpacingX
    Me.btn_Clients.Left = SpacingX + ButtonWidth + SpacingX
    ' ... continue for all buttons
End Sub
```

### Accessibility Features / ميزات إمكانية الوصول

```
1. Tab Order: Set proper tab order for keyboard navigation
2. Access Keys: Set AccessKey property for each button (Alt+Key)
3. ToolTips: Add descriptive ControlTipText for each button
4. High Contrast: Support for high contrast mode
5. Screen Reader: Proper labeling for screen readers
```

### Icon Integration / دمج الأيقونات

```
1. Icon Format: PNG, 64x64 pixels, transparent background
2. Icon Storage: Store in application folder or embed in database
3. Icon Loading: Load icons dynamically in Form_Load event
4. Fallback: Text-only display if icons fail to load
```

This design provides a professional, user-friendly main navigation interface that supports both Arabic and English languages, with proper permission-based access control and responsive design elements.
