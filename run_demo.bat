@echo off
chcp 65001 >nul
title نظام إدارة المؤسسات - Enterprise Management System Demo

echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                نظام إدارة المؤسسات الشامل                ║
echo     ║              Enterprise Management System               ║
echo     ║                     الإصدار 1.0                        ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.

echo     🎉 مرحباً بك في نظام إدارة المؤسسات الشامل!
echo.
echo     📋 ملخص النظام:
echo     ├─ 24 جدول في قاعدة البيانات
echo     ├─ 12 وحدة إدارية متكاملة
echo     ├─ واجهة مستخدم احترافية
echo     ├─ دعم كامل للغة العربية
echo     ├─ نظام أمان متقدم
echo     └─ تقارير شاملة وتحليلات
echo.

echo     🔑 بيانات تسجيل الدخول الافتراضية:
echo        اسم المستخدم: admin
echo        كلمة المرور: 12345
echo.

echo     📁 الملفات المتاحة:
if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ✅ قاعدة البيانات: نظام_إدارة_المؤسسات.accdb
) else (
    echo     ❌ قاعدة البيانات: غير موجودة
)

if exist "Complete_Database_Script.sql" (
    echo     ✅ سكريبت قاعدة البيانات: Complete_Database_Script.sql
) else (
    echo     ❌ سكريبت قاعدة البيانات: غير موجود
)

if exist "Access_Complete_VBA_Code.bas" (
    echo     ✅ كود VBA: Access_Complete_VBA_Code.bas
) else (
    echo     ❌ كود VBA: غير موجود
)

if exist "Security_Encryption_System.ps1" (
    echo     ✅ نظام التشفير: Security_Encryption_System.ps1
) else (
    echo     ❌ نظام التشفير: غير موجود
)

if exist "License_Management_System.ps1" (
    echo     ✅ إدارة التراخيص: License_Management_System.ps1
) else (
    echo     ❌ إدارة التراخيص: غير موجود
)

echo.
echo     🚀 خيارات التشغيل:
echo.
echo     [1] تشغيل قاعدة البيانات مباشرة
echo     [2] تشغيل نظام التشفير والحماية
echo     [3] تشغيل نظام إدارة التراخيص
echo     [4] إنشاء ملف تنفيذي محمي
echo     [5] عرض معلومات مفصلة
echo     [0] خروج
echo.

set /p choice="    اختر رقماً (0-5): "

if "%choice%"=="1" goto RUN_DATABASE
if "%choice%"=="2" goto RUN_SECURITY
if "%choice%"=="3" goto RUN_LICENSE
if "%choice%"=="4" goto CREATE_EXE
if "%choice%"=="5" goto SHOW_INFO
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:RUN_DATABASE
cls
echo.
echo     🚀 تشغيل قاعدة البيانات...
echo.

if exist "نظام_إدارة_المؤسسات.accdb" (
    echo     ✅ تم العثور على ملف قاعدة البيانات
    echo     🔄 جاري تشغيل Microsoft Access...
    echo.
    echo     💡 نصائح للاستخدام:
    echo     • استخدم اسم المستخدم: admin
    echo     • استخدم كلمة المرور: 12345
    echo     • تأكد من تغيير كلمة المرور بعد أول دخول
    echo.
    
    start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb"
    
    if errorlevel 1 (
        echo     ❌ فشل في تشغيل Microsoft Access
        echo     تأكد من تثبيت Microsoft Access على النظام
    ) else (
        echo     ✅ تم تشغيل النظام بنجاح!
    )
) else (
    echo     ❌ لم يتم العثور على ملف قاعدة البيانات!
    echo     يرجى التأكد من وجود الملف في نفس المجلد
)

echo.
pause
goto MAIN_MENU

:RUN_SECURITY
cls
echo.
echo     🔒 تشغيل نظام التشفير والحماية...
echo.

if exist "Security_Encryption_System.ps1" (
    echo     🔄 جاري تشغيل نظام التشفير...
    powershell -ExecutionPolicy Bypass -File "Security_Encryption_System.ps1"
) else (
    echo     ❌ ملف نظام التشفير غير موجود
)

echo.
pause
goto MAIN_MENU

:RUN_LICENSE
cls
echo.
echo     🎫 تشغيل نظام إدارة التراخيص...
echo.

if exist "License_Management_System.ps1" (
    echo     🔄 جاري تشغيل نظام إدارة التراخيص...
    powershell -ExecutionPolicy Bypass -File "License_Management_System.ps1"
) else (
    echo     ❌ ملف إدارة التراخيص غير موجود
)

echo.
pause
goto MAIN_MENU

:CREATE_EXE
cls
echo.
echo     🔧 إنشاء ملف تنفيذي محمي...
echo.

if exist "Build_EXE.bat" (
    echo     🔄 جاري تشغيل منشئ الملفات التنفيذية...
    call "Build_EXE.bat"
) else (
    echo     ❌ ملف منشئ EXE غير موجود
    echo     سيتم إنشاء ملف تشغيل بديل...
    
    echo @echo off > "نظام_إدارة_المؤسسات_Launcher.bat"
    echo chcp 65001 ^>nul >> "نظام_إدارة_المؤسسات_Launcher.bat"
    echo title نظام إدارة المؤسسات >> "نظام_إدارة_المؤسسات_Launcher.bat"
    echo echo مرحباً بك في نظام إدارة المؤسسات >> "نظام_إدارة_المؤسسات_Launcher.bat"
    echo start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb" >> "نظام_إدارة_المؤسسات_Launcher.bat"
    
    echo     ✅ تم إنشاء ملف تشغيل بديل: نظام_إدارة_المؤسسات_Launcher.bat
)

echo.
pause
goto MAIN_MENU

:SHOW_INFO
cls
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                    معلومات مفصلة                        ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.

echo     🏢 نظام إدارة المؤسسات الشامل
echo     📅 الإصدار: 1.0
echo     👨‍💻 المطور: فريق نظم إدارة المؤسسات
echo     📧 الدعم: <EMAIL>
echo.

echo     📊 مكونات النظام:
echo     ├─ قاعدة بيانات شاملة (24 جدول)
echo     ├─ واجهة مستخدم احترافية
echo     ├─ نظام أمان متقدم
echo     ├─ نظام تشفير وحماية
echo     ├─ إدارة تراخيص متطورة
echo     ├─ تقارير وتحليلات شاملة
echo     └─ دعم كامل للغة العربية
echo.

echo     🎯 الوحدات المتاحة:
echo     ├─ 👥 إدارة العملاء
echo     ├─ 💰 إدارة المبيعات
echo     ├─ 🛒 إدارة المشتريات
echo     ├─ 📦 إدارة المخازن
echo     ├─ 🏭 إدارة الموردين
echo     ├─ 👤 إدارة الموظفين
echo     ├─ 💳 إدارة الحسابات
echo     ├─ 📄 إدارة الفواتير
echo     ├─ 📊 إدارة التقارير
echo     ├─ 🔐 إدارة المستخدمين
echo     ├─ 🗄️ إدارة قاعدة البيانات
echo     └─ 📋 لوحة التحكم
echo.

echo     🔒 مميزات الأمان:
echo     ├─ تشفير AES-256
echo     ├─ نظام ترخيص مرتبط بالجهاز
echo     ├─ مكافحة التصحيح
echo     ├─ حماية من التلاعب
echo     └─ فحص سلامة الملفات
echo.

echo     📞 للدعم الفني:
echo     ├─ البريد الإلكتروني: <EMAIL>
echo     ├─ الهاتف: +966-XX-XXXXXXX
echo     └─ الموقع: www.enterprise-system.com
echo.

pause
goto MAIN_MENU

:EXIT
cls
echo.
echo     👋 شكراً لاستخدام نظام إدارة المؤسسات!
echo.
echo     🌟 نتمنى لك تجربة ممتازة مع النظام
echo     📞 للدعم الفني: <EMAIL>
echo.
timeout /t 3 >nul
exit /b 0

:MAIN_MENU
goto :eof
