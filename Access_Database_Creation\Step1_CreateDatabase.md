# إنشاء ملف Access - خطوة بخطوة
## Creating Access Database File - Step by Step

### الخطوة الأولى: إنشاء قاعدة البيانات الجديدة

#### 1. فتح Microsoft Access
```
1. افتح Microsoft Access
2. اختر "Blank Database" (قاعدة بيانات فارغة)
3. اسم الملف: "نظام_إدارة_المؤسسات.accdb"
4. اختر المجلد المناسب للحفظ
5. اضغط "Create" (إنشاء)
```

#### 2. إعداد قاعدة البيانات الأولي
```
بعد إنشاء قاعدة البيانات:
1. احذف الجدول الافتراضي "Table1"
2. اذهب إلى File > Options > Current Database
3. غير Application Title إلى "نظام إدارة المؤسسات"
4. اختر Display Form: (سنحدده لاحقاً)
5. احفظ الإعدادات
```

### الخطوة الثانية: إنشاء الجداول

#### تنفيذ سكريبت الجداول:
```sql
-- نفذ هذا الكود في Query Design View (SQL View)

-- 1. جدول المستخدمين
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT CONSTRAINT PK_Users PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    RoleID LONG,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME
);

-- 2. جدول الأدوار
CREATE TABLE tbl_Roles (
    RoleID AUTOINCREMENT CONSTRAINT PK_Roles PRIMARY KEY,
    RoleName TEXT(50) NOT NULL,
    Description TEXT(255),
    IsActive YESNO DEFAULT Yes
);

-- 3. جدول العملاء
CREATE TABLE tbl_Clients (
    ClientID AUTOINCREMENT CONSTRAINT PK_Clients PRIMARY KEY,
    ClientCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Website TEXT(200),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    CreditLimit CURRENCY DEFAULT 0,
    PaymentTerms LONG DEFAULT 30,
    ClientType TEXT(20) DEFAULT "Customer",
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO
);

-- 4. جدول جهات اتصال العملاء
CREATE TABLE tbl_ClientContacts (
    ContactID AUTOINCREMENT CONSTRAINT PK_ClientContacts PRIMARY KEY,
    ClientID LONG NOT NULL,
    ContactName TEXT(100) NOT NULL,
    Position TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    IsPrimary YESNO DEFAULT No
);

-- 5. جدول الموردين
CREATE TABLE tbl_Suppliers (
    SupplierID AUTOINCREMENT CONSTRAINT PK_Suppliers PRIMARY KEY,
    SupplierCode TEXT(20) NOT NULL,
    CompanyName TEXT(200) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    Address MEMO,
    City TEXT(50),
    Country TEXT(50),
    TaxID TEXT(50),
    PaymentTerms LONG DEFAULT 30,
    Currency TEXT(3) DEFAULT "SAR",
    Rating LONG DEFAULT 0,
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
);

-- 6. جدول فئات المنتجات
CREATE TABLE tbl_Categories (
    CategoryID AUTOINCREMENT CONSTRAINT PK_Categories PRIMARY KEY,
    CategoryName TEXT(100) NOT NULL,
    ParentCategoryID LONG,
    Description TEXT(255),
    IsActive YESNO DEFAULT Yes
);

-- 7. جدول المنتجات
CREATE TABLE tbl_Products (
    ProductID AUTOINCREMENT CONSTRAINT PK_Products PRIMARY KEY,
    ProductCode TEXT(50) NOT NULL,
    ProductName TEXT(200) NOT NULL,
    CategoryID LONG,
    Description MEMO,
    UnitOfMeasure TEXT(20) DEFAULT "قطعة",
    CostPrice CURRENCY DEFAULT 0,
    SellingPrice CURRENCY DEFAULT 0,
    MinimumStock LONG DEFAULT 0,
    ReorderPoint LONG DEFAULT 0,
    Barcode TEXT(100),
    IsActive YESNO DEFAULT Yes,
    IsService YESNO DEFAULT No,
    SupplierID LONG,
    CreatedDate DATETIME DEFAULT Now(),
    CreatedBy LONG
);

-- 8. جدول مستويات المخزون
CREATE TABLE tbl_StockLevels (
    StockID AUTOINCREMENT CONSTRAINT PK_StockLevels PRIMARY KEY,
    ProductID LONG NOT NULL,
    CurrentStock LONG DEFAULT 0,
    ReservedStock LONG DEFAULT 0,
    LastUpdated DATETIME DEFAULT Now()
);

-- 9. جدول حركات المخزون
CREATE TABLE tbl_InventoryTransactions (
    TransactionID AUTOINCREMENT CONSTRAINT PK_InventoryTransactions PRIMARY KEY,
    ProductID LONG NOT NULL,
    TransactionType TEXT(20) NOT NULL,
    Quantity LONG NOT NULL,
    UnitCost CURRENCY,
    ReferenceType TEXT(20),
    ReferenceID LONG,
    TransactionDate DATETIME DEFAULT Now(),
    CreatedBy LONG,
    Notes MEMO
);

-- 10. جدول الموظفين
CREATE TABLE tbl_Employees (
    EmployeeID AUTOINCREMENT CONSTRAINT PK_Employees PRIMARY KEY,
    EmployeeCode TEXT(20) NOT NULL,
    FirstName TEXT(50) NOT NULL,
    LastName TEXT(50) NOT NULL,
    Email TEXT(100),
    Phone TEXT(20),
    HireDate DATETIME NOT NULL,
    Department TEXT(100),
    Position TEXT(100),
    Salary CURRENCY,
    IsActive YESNO DEFAULT Yes,
    Address MEMO,
    EmergencyContact TEXT(100),
    EmergencyPhone TEXT(20),
    CreatedDate DATETIME DEFAULT Now()
);
```

### الخطوة الثالثة: إدخال البيانات الأساسية

```sql
-- إدخال الأدوار الافتراضية
INSERT INTO tbl_Roles (RoleName, Description) VALUES 
('مدير النظام', 'صلاحية كاملة على النظام'),
('مدير عام', 'صلاحيات إدارية عامة'),
('مدير مبيعات', 'إدارة المبيعات والعملاء'),
('مدير مشتريات', 'إدارة المشتريات والموردين'),
('أمين مخزن', 'إدارة المخزون'),
('محاسب', 'إدارة الحسابات والفواتير'),
('موظف إدخال بيانات', 'إدخال البيانات الأساسية');

-- إدخال المستخدم الافتراضي
INSERT INTO tbl_Users (Username, Password, FirstName, LastName, RoleID, IsActive) 
VALUES ('admin', '12345', 'مدير', 'النظام', 1, True);

-- إدخال فئات المنتجات الأساسية
INSERT INTO tbl_Categories (CategoryName, Description) VALUES
('إلكترونيات', 'الأجهزة الإلكترونية والكهربائية'),
('مكتبية', 'اللوازم المكتبية والقرطاسية'),
('أثاث', 'الأثاث المكتبي والمنزلي'),
('خدمات', 'الخدمات المختلفة');

-- إدخال عميل تجريبي
INSERT INTO tbl_Clients (ClientCode, CompanyName, ContactPerson, Phone, Email, ClientType, IsActive, CreatedBy)
VALUES ('CL0001', 'شركة الاختبار المحدودة', 'أحمد محمد', '0501234567', '<EMAIL>', 'Customer', True, 1);

-- إدخال مورد تجريبي
INSERT INTO tbl_Suppliers (SupplierCode, CompanyName, ContactPerson, Phone, Email, IsActive)
VALUES ('SP0001', 'مورد الاختبار', 'محمد أحمد', '0507654321', '<EMAIL>', True);

-- إدخال منتج تجريبي
INSERT INTO tbl_Products (ProductCode, ProductName, CategoryID, UnitOfMeasure, CostPrice, SellingPrice, MinimumStock, ReorderPoint, IsActive, SupplierID, CreatedBy)
VALUES ('PR0001', 'منتج تجريبي', 1, 'قطعة', 100, 150, 10, 5, True, 1, 1);

-- إدخال مستوى مخزون للمنتج التجريبي
INSERT INTO tbl_StockLevels (ProductID, CurrentStock, ReservedStock)
VALUES (1, 50, 0);
```

### الخطوة الرابعة: إنشاء العلاقات

```
1. اذهب إلى Database Tools > Relationships
2. أضف جميع الجداول إلى نافذة العلاقات
3. أنشئ العلاقات التالية:

العلاقات الأساسية:
- tbl_Users.RoleID → tbl_Roles.RoleID (Many-to-One)
- tbl_ClientContacts.ClientID → tbl_Clients.ClientID (Many-to-One)
- tbl_Products.CategoryID → tbl_Categories.CategoryID (Many-to-One)
- tbl_Products.SupplierID → tbl_Suppliers.SupplierID (Many-to-One)
- tbl_StockLevels.ProductID → tbl_Products.ProductID (One-to-One)
- tbl_InventoryTransactions.ProductID → tbl_Products.ProductID (Many-to-One)

لكل علاقة:
✓ فعل "Enforce Referential Integrity"
✓ فعل "Cascade Update Related Fields"
✗ لا تفعل "Cascade Delete Related Records"
```

### الخطوة الخامسة: إنشاء الاستعلامات الأساسية

```sql
-- استعلام العملاء النشطين
CREATE QUERY qry_ActiveClients AS
SELECT ClientID, ClientCode, CompanyName, ContactPerson, Phone, Email, CreditLimit, ClientType
FROM tbl_Clients 
WHERE IsActive = True 
ORDER BY CompanyName;

-- استعلام المنتجات مع مستويات المخزون
CREATE QUERY qry_ProductsWithStock AS
SELECT p.ProductID, p.ProductCode, p.ProductName, c.CategoryName, 
       p.CostPrice, p.SellingPrice, s.CurrentStock, s.ReservedStock,
       (s.CurrentStock - s.ReservedStock) AS AvailableStock,
       IIF(s.CurrentStock <= p.ReorderPoint, "مخزون منخفض", "طبيعي") AS StockStatus
FROM (tbl_Products p LEFT JOIN tbl_Categories c ON p.CategoryID = c.CategoryID) 
     LEFT JOIN tbl_StockLevels s ON p.ProductID = s.ProductID
WHERE p.IsActive = True
ORDER BY p.ProductName;

-- استعلام المخزون المنخفض
CREATE QUERY qry_LowStockItems AS
SELECT p.ProductID, p.ProductCode, p.ProductName, s.CurrentStock, p.ReorderPoint,
       sup.CompanyName AS SupplierName
FROM (tbl_Products p LEFT JOIN tbl_StockLevels s ON p.ProductID = s.ProductID)
     LEFT JOIN tbl_Suppliers sup ON p.SupplierID = sup.SupplierID
WHERE p.IsActive = True AND s.CurrentStock <= p.ReorderPoint
ORDER BY s.CurrentStock;
```

### الخطوة السادسة: حفظ وإغلاق

```
1. احفظ قاعدة البيانات (Ctrl+S)
2. أغلق Access
3. أعد فتح الملف للتأكد من سلامة البيانات
4. تحقق من وجود جميع الجداول والعلاقات
```

### ملاحظات مهمة:

⚠️ **تأكد من:**
- حفظ نسخة احتياطية قبل البدء
- تنفيذ كل خطوة بعناية
- اختبار البيانات بعد كل خطوة
- التحقق من العلاقات والقيود

✅ **بعد إكمال هذه الخطوات ستحصل على:**
- قاعدة بيانات كاملة مع جميع الجداول
- بيانات تجريبية للاختبار
- علاقات صحيحة بين الجداول
- استعلامات أساسية جاهزة للاستخدام

**الخطوة التالية:** إنشاء النماذج والواجهات
