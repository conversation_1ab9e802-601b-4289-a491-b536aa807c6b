@echo off
chcp 65001 >nul
title إنشاء ملف EXE لنظام إدارة المؤسسات

echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║              إنشاء ملف EXE للنظام                       ║
echo     ║            Create EXE File for System                  ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود PowerShell
powershell -Command "Write-Host 'PowerShell متوفر'" >nul 2>&1
if errorlevel 1 (
    echo     ❌ PowerShell غير متوفر
    echo     يرجى تثبيت PowerShell أولاً
    pause
    exit /b 1
)

echo     🔄 جاري إنشاء ملف EXE...
echo.

REM إنشاء ملف PowerShell مؤقت لتحويل السكريبت إلى EXE
echo # تحويل PowerShell إلى EXE > temp_converter.ps1
echo Add-Type -AssemblyName System.Windows.Forms >> temp_converter.ps1
echo Add-Type -AssemblyName System.Drawing >> temp_converter.ps1
echo. >> temp_converter.ps1

REM إضافة محتوى السكريبت الرئيسي
type "نظام_إدارة_المؤسسات.ps1" >> temp_converter.ps1

REM محاولة إنشاء EXE باستخدام طرق مختلفة

echo     [1/4] محاولة استخدام PowerShell ISE...
powershell -Command "if (Get-Command 'powershell_ise.exe' -ErrorAction SilentlyContinue) { Write-Host 'PowerShell ISE متوفر' } else { Write-Host 'PowerShell ISE غير متوفر' }" >nul 2>&1

echo     [2/4] محاولة استخدام IExpress...
REM إنشاء ملف SED لـ IExpress
echo [Version] > enterprise.sed
echo Class=IEXPRESS >> enterprise.sed
echo SEDVersion=3 >> enterprise.sed
echo [Options] >> enterprise.sed
echo PackagePurpose=InstallApp >> enterprise.sed
echo ShowInstallProgramWindow=0 >> enterprise.sed
echo HideExtractAnimation=1 >> enterprise.sed
echo UseLongFileName=1 >> enterprise.sed
echo InsideCompressed=0 >> enterprise.sed
echo CAB_FixedSize=0 >> enterprise.sed
echo CAB_ResvCodeSigning=0 >> enterprise.sed
echo RebootMode=N >> enterprise.sed
echo InstallPrompt=هل تريد تشغيل نظام إدارة المؤسسات؟ >> enterprise.sed
echo DisplayLicense= >> enterprise.sed
echo FinishMessage=تم تشغيل نظام إدارة المؤسسات بنجاح! >> enterprise.sed
echo TargetName=نظام_إدارة_المؤسسات.exe >> enterprise.sed
echo FriendlyName=نظام إدارة المؤسسات الشامل >> enterprise.sed
echo AppLaunched=cmd.exe >> enterprise.sed
echo PostInstallCmd=^<None^> >> enterprise.sed
echo AdminQuietInstCmd= >> enterprise.sed
echo UserQuietInstCmd= >> enterprise.sed
echo FILE0="تشغيل_سريع.cmd" >> enterprise.sed
echo [Strings] >> enterprise.sed
echo [SourceFiles] >> enterprise.sed
echo SourceFiles0=. >> enterprise.sed
echo [SourceFiles0] >> enterprise.sed
echo %%FILE0%%= >> enterprise.sed

REM تشغيل IExpress
iexpress /N enterprise.sed >nul 2>&1
if exist "نظام_إدارة_المؤسسات.exe" (
    echo     ✅ تم إنشاء ملف EXE باستخدام IExpress
    del enterprise.sed >nul 2>&1
    goto SUCCESS
)

echo     [3/4] محاولة إنشاء ملف تنفيذي بديل...

REM إنشاء ملف VBS لتشغيل النظام
echo Set objShell = CreateObject("WScript.Shell") > launcher.vbs
echo Set objFSO = CreateObject("Scripting.FileSystemObject") >> launcher.vbs
echo. >> launcher.vbs
echo ' التحقق من وجود ملف قاعدة البيانات >> launcher.vbs
echo If objFSO.FileExists("نظام_إدارة_المؤسسات.accdb") Then >> launcher.vbs
echo     objShell.Run "msaccess.exe ""نظام_إدارة_المؤسسات.accdb""", 1, False >> launcher.vbs
echo ElseIf objFSO.FileExists("تشغيل_سريع.cmd") Then >> launcher.vbs
echo     objShell.Run "تشغيل_سريع.cmd", 1, False >> launcher.vbs
echo Else >> launcher.vbs
echo     MsgBox "لم يتم العثور على ملفات النظام!" ^& vbCrLf ^& "يرجى التأكد من وجود الملفات في نفس المجلد.", vbCritical, "خطأ" >> launcher.vbs
echo End If >> launcher.vbs

echo     ✅ تم إنشاء ملف VBS: launcher.vbs

echo     [4/4] إنشاء ملف تشغيل نهائي...

REM إنشاء ملف تشغيل نهائي محسن
echo @echo off > "نظام_إدارة_المؤسسات_Final.bat"
echo chcp 65001 ^>nul >> "نظام_إدارة_المؤسسات_Final.bat"
echo title نظام إدارة المؤسسات - Enterprise Management System >> "نظام_إدارة_المؤسسات_Final.bat"
echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo cls >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     ╔══════════════════════════════════════════════════════════╗ >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     ║                نظام إدارة المؤسسات الشامل                ║ >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     ║              Enterprise Management System               ║ >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     ║                     الإصدار 1.0                        ║ >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     ╚══════════════════════════════════════════════════════════╝ >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     🔑 بيانات تسجيل الدخول الافتراضية: >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo        اسم المستخدم: admin >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo        كلمة المرور: 12345 >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     🚀 جاري تشغيل النظام... >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo REM التحقق من وجود ملف قاعدة البيانات >> "نظام_إدارة_المؤسسات_Final.bat"
echo if exist "نظام_إدارة_المؤسسات.accdb" ^( >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     ✅ تم العثور على ملف قاعدة البيانات >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     🔄 جاري تشغيل Microsoft Access... >> "نظام_إدارة_المؤسسات_Final.bat"
echo     start "" "msaccess.exe" "نظام_إدارة_المؤسسات.accdb" >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     ✅ تم تشغيل النظام بنجاح! >> "نظام_إدارة_المؤسسات_Final.bat"
echo ^) else if exist "تشغيل_سريع.cmd" ^( >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     🔄 تشغيل الملف البديل... >> "نظام_إدارة_المؤسسات_Final.bat"
echo     call "تشغيل_سريع.cmd" >> "نظام_إدارة_المؤسسات_Final.bat"
echo ^) else ^( >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     ❌ لم يتم العثور على ملفات النظام! >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     يرجى التأكد من وجود الملفات في نفس المجلد >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     📋 الملفات المطلوبة: >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     • نظام_إدارة_المؤسسات.accdb >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo     • تشغيل_سريع.cmd >> "نظام_إدارة_المؤسسات_Final.bat"
echo     echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo     pause >> "نظام_إدارة_المؤسسات_Final.bat"
echo     exit /b 1 >> "نظام_إدارة_المؤسسات_Final.bat"
echo ^) >> "نظام_إدارة_المؤسسات_Final.bat"
echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     📋 ملاحظات مهمة: >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     • استخدم بيانات تسجيل الدخول المذكورة أعلاه >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     • تأكد من تغيير كلمة المرور بعد أول دخول >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo     • يتم حفظ النسخ الاحتياطية في مجلد Backups >> "نظام_إدارة_المؤسسات_Final.bat"
echo echo. >> "نظام_إدارة_المؤسسات_Final.bat"
echo timeout /t 3 ^>nul >> "نظام_إدارة_المؤسسات_Final.bat"

echo     ✅ تم إنشاء ملف التشغيل النهائي: نظام_إدارة_المؤسسات_Final.bat

:SUCCESS
echo.
echo     ╔══════════════════════════════════════════════════════════╗
echo     ║                    تم إكمال العملية!                     ║
echo     ╚══════════════════════════════════════════════════════════╝
echo.

echo     📋 الملفات التي تم إنشاؤها:
if exist "نظام_إدارة_المؤسسات.exe" (
    echo     ✅ نظام_إدارة_المؤسسات.exe ^(ملف تنفيذي^)
)
if exist "launcher.vbs" (
    echo     ✅ launcher.vbs ^(ملف VBScript^)
)
if exist "نظام_إدارة_المؤسسات_Final.bat" (
    echo     ✅ نظام_إدارة_المؤسسات_Final.bat ^(ملف تشغيل محسن^)
)

echo.
echo     🚀 طرق التشغيل المتاحة:
if exist "نظام_إدارة_المؤسسات.exe" (
    echo     1. انقر نقراً مزدوجاً على: نظام_إدارة_المؤسسات.exe
)
echo     2. انقر نقراً مزدوجاً على: نظام_إدارة_المؤسسات_Final.bat
echo     3. انقر نقراً مزدوجاً على: launcher.vbs
echo     4. استخدم: تشغيل_سريع.cmd

echo.
echo     🔑 بيانات تسجيل الدخول:
echo     • اسم المستخدم: admin
echo     • كلمة المرور: 12345

echo.
echo     📞 للدعم الفني:
echo     • البريد الإلكتروني: <EMAIL>
echo     • راجع ملف تعليمات_التشغيل.md

REM تنظيف الملفات المؤقتة
del temp_converter.ps1 >nul 2>&1
del enterprise.sed >nul 2>&1

echo.
echo     🎉 نظام إدارة المؤسسات جاهز للاستخدام!
echo.
echo     اضغط أي مفتاح للخروج...
pause >nul

REM اختبار تشغيل الملف إذا كان موجوداً
if exist "نظام_إدارة_المؤسسات.exe" (
    set /p test_run="    هل تريد اختبار تشغيل الملف الآن؟ ^(Y/N^): "
    if /i "!test_run!"=="Y" (
        echo.
        echo     🧪 اختبار تشغيل الملف...
        start "" "نظام_إدارة_المؤسسات.exe"
    )
) else if exist "نظام_إدارة_المؤسسات_Final.bat" (
    set /p test_run="    هل تريد اختبار تشغيل الملف الآن؟ ^(Y/N^): "
    if /i "!test_run!"=="Y" (
        echo.
        echo     🧪 اختبار تشغيل الملف...
        start "" "نظام_إدارة_المؤسسات_Final.bat"
    )
)

exit /b 0
